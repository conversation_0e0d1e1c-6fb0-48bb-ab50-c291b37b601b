// globals.d.ts

interface FbqFunction {
  (command: "track", eventName: string, properties?: Record<string, unknown>, options?: Record<string, unknown>): void;
  (command: "trackCustom", eventName: string, properties?: Record<string, unknown>): void;
  (command: "init", pixelId: string): void;
  (command: "config", pixelId: string, configOptions?: Record<string, unknown>): void;
}

interface TtqFunction {
  track(eventName: string, properties?: Record<string, unknown>): void;
}

declare var fbq: FbqFunction;
declare var ttq: TtqFunction;
