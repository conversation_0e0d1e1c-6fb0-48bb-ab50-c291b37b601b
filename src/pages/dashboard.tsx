import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { GetStoreAnalyticsParams, GetOrdersParams } from "@/api/interfaces";
import { GetOrders, GetStoreAnalytics } from "@/api";
import { useFetcher, useRequest } from "@/api/utils";
import useScreenSize from "@/components/hooks/useScreenSize";
import { DashboardLatestOrders, DashboardWallet, TopProducts } from "@/components/dashboard";
import DashboardLayout from "@/components/ui/layouts/dashboard";
import { TimeRange } from "@/components/ui/statistics-graph";
import authContext from "@/contexts/auth-context";
import WalletContext from "@/contexts/wallet-context";
import SetSecurityPinModal from "@/components/dashboard/set-security-pin";
import { COUNTRIES } from "@/assets/interfaces";
import { SCOPES, actionIsAllowed } from "@/assets/js/utils/permissions";
import { AddStoreCategoryModal } from "@/components/dashboard/setup-categories-modal";
import DashboardStatistics from "@/components/dashboard/stats";
import { GetChowbotStatisticsParams } from "@/api/interfaces/chowbot.interface";
import { GetChowbotStatistics } from "@/api/chowbot";
import useTabs from "@/components/hooks/useTabs";
import { CustomDomainNotice, NewStorefrontNotice } from "@/components/dashboard/notices";

const Dashboard = () => {
  return (
    <WalletContext.Provider>
      <DashboardLayout title="Dashboard" padding={false}>
        <div className="h-full overflow-y-auto pb-12.5">
          <DashboardMain />
        </div>
      </DashboardLayout>
    </WalletContext.Provider>
  );
};

const DashboardMain = () => {
  const { store, subscription, user, userRole, updateStore } = authContext.useContainer();
  const { width, hasLoaded } = useScreenSize();
  const [range, setRange] = useState(TimeRange.THIS_YEAR);
  const isSmall = width <= 1024;
  const usesChowbot = store?.flags?.uses_chowbot;

  const { tabs, switchTab, acitveKey, active } = useTabs(
    [usesChowbot ? "Chowbot" : null, "Storefront"].filter((t) => !!t),
    0
  );

  const catlogStatsReq = useRequest<GetStoreAnalyticsParams>(GetStoreAnalytics);
  const chowbotStatsReq = useRequest<GetChowbotStatisticsParams>(GetChowbotStatistics);

  const getLatestOrdersReq = useFetcher<GetOrdersParams>(GetOrders, {
    page: 1,
    per_page: 3,
    sort: "DESC",
    filter: {},
  });

  useEffect(() => {
    if (store?.id && !catlogStatsReq.isLoading && !chowbotStatsReq.isLoading) {
      fetchStoreData();
    }
  }, [store, range, active]);

  const fetchStoreData = async () => {
    acitveKey === "chowbot"
      ? await chowbotStatsReq.makeRequest({ filter: getFilter(range) })
      : await catlogStatsReq.makeRequest({ id: store?.id, filter: getFilter(range) });
  };

  const canManagePin = actionIsAllowed({
    userRole,
    permission: SCOPES.SETTINGS.UPDATE_SECURITY_PIN,
  });

  return (
    <>
      {!isSmall && (
        <div className="lg:grid grid-cols-[1fr,360px] border-b border-grey-border border-opacity-50">
          <div className="flex flex-col">
            <CustomDomainNotice user={user} />
            <DashboardStatistics
              {...{
                store,
                subscription,
                range,
                setRange,
                catlogStatsReq,
                chowbotStatsReq,
                tabs,
                switchTab,
                active,
                activeKey: acitveKey,
              }}
            />
          </div>
          <div className="border-l border-grey-border border-opacity-50">
            {hasLoaded && <DashboardWallet />}
            <DashboardLatestOrders getLatestOrdersReq={getLatestOrdersReq} />
          </div>
        </div>
      )}

      {isSmall && (
        <div className="block">
          <CustomDomainNotice user={user} />
          <DashboardStatistics
            {...{
              store,
              subscription,
              range,
              setRange,
              catlogStatsReq,
              chowbotStatsReq,
              usesChowbot,
              tabs,
              switchTab,
              active,
              activeKey: acitveKey,
            }}
          >
            {hasLoaded && <DashboardWallet />}
          </DashboardStatistics>
          <DashboardLatestOrders getLatestOrdersReq={getLatestOrdersReq} />
        </div>
      )}
      <TopProducts storeId={store?.id} />
      {store?.payments_enabled && !store?.onboarding_steps?.security_pin_added && canManagePin && (
        <SetSecurityPinModal />
      )}
      <AddStoreCategoryModal closeable={false} store={store} updateStore={updateStore} />
    </>
  );
};

export const getFilter = (range: TimeRange) => {
  const now = Date.now();

  const maps = {
    [TimeRange.ALL_TIME]: {
      from: dayjs(now).year(2021).format(),
      to: dayjs(now).format(),
    },
    [TimeRange.THIS_YEAR]: {
      from: dayjs(now).subtract(11, "months").format(),
      to: dayjs(now).format(),
    },
    [TimeRange.LAST_30_DAYS]: {
      from: dayjs(now).subtract(30, "days").format(),
      to: dayjs(now).format(),
    },
    [TimeRange.THIS_WEEK]: {
      from: dayjs(now).day(0).format(),
      to: dayjs(now).format(),
    },
    [TimeRange.LAST_WEEK]: {
      from: dayjs(now).day(-7).format(),
      to: dayjs(now).format(),
    },
    [TimeRange.TODAY]: {
      from: dayjs(now).startOf("day").format(),
      to: dayjs(now).format(),
    },
  };
  return maps[range];
};

export default Dashboard;
