import React, { useEffect, useMemo, useState } from "react";
import AuthLayout from "../components/ui/layouts/auth";
import {
  InputField,
  PhoneInput,
  PasswordField,
  InputWithAddon,
  SelectDropdown,
  SelectWithModal,
} from "../components/ui/form-elements";
import { AppBtn } from "../components/ui/buttons";
import Link from "next/link";
import * as Yup from "yup";
import { useFormik } from "formik";
import { SignUpParams, SignUpParamsV2 } from "../api/interfaces/auth.interface";
import { getFieldvalues, getStyles, phoneObjectFromString } from "../assets/js/utils/functions";
import authContext from "../contexts/auth-context";
import ErrorLabel from "../components/ui/error-label";
import { CreateStoreForm } from "../components/create-store";
import cx from "classnames";
import { phoneValidation } from "../assets/js/utils/common-validations";
import useSearchParams from "@/components/hooks/useSearchParams";
import { useFetcher, useRequest } from "@/api/utils";
import { CheckReferralCodeValidity } from "@/api/credits-and-referrals";
import ReCaptcha from "@/components/create-store/recaptcha";
import { emit } from "@/components/hooks/useListener";
import countryCodes from "@/assets/js/utils/country-codes.json";
import { STORE_TYPES, SUPPORTED_COUNTRIES } from "@/components/create-store/form";
import { GetCountries, GetSlugSuggestion, ValidateSlug } from "@/api";
import { getDropdownOptionsFromCountries, mixpanelTrack } from "@/assets/js/utils/utils";
import FeatureCard from "@/components/manage-features/feature-card";
import StoreAdditionalDetailsForm from "@/components/create-store/additional-store-details";
import { useRouter } from "next/router";
import { COUNTRIES } from "@/assets/interfaces";
import { useModals } from "@/components/hooks/useModals";
import StoreCountryModal from "@/components/create-store/store-country-confirmation-modal";

enum Steps {
  CREATE_ACCOUNT,
  CREATE_STORE,
}

interface ValidityData {
  exists: boolean;
  referral?: string;
  user?: {
    name: string;
    id: string;
  };
}

export interface SignUpFormParams extends Omit<SignUpParamsV2, "phone"> {
  phone: { code: string; digits: string };
}

const SignUp = () => {
  const { registerV2, signupRequestV2, visitorCountry } = authContext.useContainer();

  const router = useRouter();
  const [steps, setSteps] = useState(Steps.CREATE_ACCOUNT);
  const [errorText, setErrorText] = useState("");
  const { ref, product } = useSearchParams(["ref", "product"]);
  const { modals, toggleModal } = useModals(["country_confirmation"]);

  const slugSuggestionReq = useRequest(GetSlugSuggestion);
  const suggestedSlug = slugSuggestionReq?.response?.data?.slug;
  const validateSlugReq = useRequest(ValidateSlug);
  const refCodeValidityReq = useRequest(CheckReferralCodeValidity);
  const { isLoading, error } = signupRequestV2;

  const { response: supportedCountries } = useFetcher(GetCountries, {});
  const countryOptions = useMemo(() => {
    return getDropdownOptionsFromCountries(supportedCountries?.data, false);
  }, [supportedCountries]);

  let validityData: ValidityData = refCodeValidityReq?.response?.data;

  useEffect(() => {
    if (ref) {
      form.setFieldValue("referral_code", ref);
      checkReferralCodeValidity(ref);
    }
  }, [ref]);

  useEffect(() => {
    if (product) {
      form.setFieldValue(
        "store_type",
        product.toLocaleLowerCase() === "chowbot" ? STORE_TYPES.RESTAURANT : STORE_TYPES.REGULAR
      );
    }
  }, [product]);

  useEffect(() => {
    getPrefilledCountry();
  }, [visitorCountry]);

  const form = useFormik<SignUpFormParams>({
    initialValues: {
      name: "",
      email: "",
      country: SUPPORTED_COUNTRIES[0],
      store_type: STORE_TYPES.REGULAR,
      business_name: "",
      phone: {
        code: visitorCountry?.dial_code ?? "+234",
        digits: "",
      },
      password: "",
      referral_code: "",
      recaptcha_token: "",
      slug: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      const phone = `${values.phone.code}-${values.phone.digits}`;

      if (validityErrorText) {
        setErrorText("Referral code is invalid, please change or remove it");
        return;
      }

      const [response, error] = await registerV2({ ...values, phone }, false);

      if (response?.user) {
        const sourceAd = JSON.parse(localStorage?.getItem("sourceAd") ?? "{}");

        fbq("track", "Lead");
        ttq.track("Lead");

        mixpanelTrack("Sign Up", {
          "Signup Type": values.referral_code ? "Referral" : sourceAd.utm_source ? "Ad" : "Direct",
          $email: values.email,
          $name: values.name,
          $phone: phone.split("-").join(""),
          Country: values.country,
          "Business Name": values.business_name,
        });
        router.push("/setup/add-products");
        // setSteps(Steps.CREATE_STORE);
      }

      if (error) {
        await emit("RESET_RECAPTCHA");
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors({
            ...error.fields,
            phone: error.fields.phone && { digits: error.fields.phone },
          });
        } else {
          setErrorText(error.message);
        }
      }
    },
  });

  const showAddon = form.values.referral_code !== "" && (validityData?.exists || refCodeValidityReq?.isLoading);
  const refIsValid = validityData?.exists || form.values.referral_code !== "";
  const validityError = refCodeValidityReq?.error || (validityData && !validityData?.exists);
  const validityErrorText = validityError
    ? "Invalid Referral Code"
    : form.errors.referral_code && form.touched.referral_code
    ? form?.errors?.referral_code
    : null;

  const bubbleActive = " w-2 h-2 m-1 bg-primary-500 rounded-full";
  const bubbleInactive = "w-1.5 h-1.5 bg-gray-300 bg-opacity-70 rounded-full";

  const checkReferralCodeValidity = async (code: string) => {
    const [res, err] = await refCodeValidityReq.makeRequest({ code });

    if (err || !res?.data?.exists) {
      form.setFieldError("referral_code", "Invalid Referral Code");

      setTimeout(() => {
        form.setFieldTouched("referral_code", true);
      }, 500);
    }
  };

  const handleRefCodeBlur = (e: React.ChangeEvent<HTMLInputElement>) => {
    const code = e.target.value;

    form.handleBlur(e);

    if (!code.trim()) {
      form.setFieldError("referral_code", "");
      refCodeValidityReq.clearResponse();
      return;
    }

    checkReferralCodeValidity(code);
  };

  const handleBusinessNameBlur = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;

    const [res, err] = await slugSuggestionReq.makeRequest({ store_name: name });

    if (res) {
      form.setFieldValue("slug", res.data?.slug);
    }
  };

  const selectedCountry = useMemo(() => {
    return countryOptions.find((option) => option.value === form.values.country);
  }, [form.values.country, countryOptions]);

  const getPrefilledCountry = () => {
    let country = SUPPORTED_COUNTRIES[0];
    if (visitorCountry && visitorCountry.code && SUPPORTED_COUNTRIES.includes(visitorCountry.code)) {
      country = visitorCountry.code;
    }

    setCountry(country);
  };

  const setCountry = (code: string) => {
    form.setFieldValue("country", code);
    form.setFieldValue("phone", {
      code: countryCodes.find((c) => c.code === code)?.dial_code ?? "+234",
      digits: form.values.phone.digits,
    });
  };

  const renderStepContent = (step: Steps) => {
    switch (step) {
      case Steps.CREATE_ACCOUNT:
        return (
          <>
            <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-orange-500 m-auto flex items-center justify-center text-white">
              {/* prettier-ignore */}
              <svg className="w-[30px] sm:w-8" viewBox="0 0 24 24" fill="none">
                <path d="M12 2C9.38 2 7.25 4.13 7.25 6.75C7.25 9.32 9.26 11.4 11.88 11.49C11.96 11.48 12.04 11.48 12.1 11.49C12.12 11.49 12.13 11.49 12.15 11.49C12.16 11.49 12.16 11.49 12.17 11.49C14.73 11.4 16.74 9.32 16.75 6.75C16.75 4.13 14.62 2 12 2Z" fill="currentColor"/>
                <path d="M17.0809 14.1489C14.2909 12.2889 9.74094 12.2889 6.93094 14.1489C5.66094 14.9989 4.96094 16.1489 4.96094 17.3789C4.96094 18.6089 5.66094 19.7489 6.92094 20.5889C8.32094 21.5289 10.1609 21.9989 12.0009 21.9989C13.8409 21.9989 15.6809 21.5289 17.0809 20.5889C18.3409 19.7389 19.0409 18.5989 19.0409 17.3589C19.0309 16.1289 18.3409 14.9889 17.0809 14.1489Z" fill="currentColor"/>
              </svg>
            </figure>
            <h2 className="text-center font-light text-black text-2lg sm:text-[26px] lg:text-3xl mx-auto mt-3.5 !leading-tight">
              Create your Online
              <br />
              <b className="font-bold">Store In 5 Minutes</b>
            </h2>
            <div className="flex w-full justify-center mt-2.5">
              <SelectWithModal
                label="Select Country"
                options={countryOptions}
                value={form.values.country}
                onChange={(e) => setCountry(e.target.value)}
                isLoadingData={isLoading}
                className="!mt-0 self-center"
              >
                <button className="rounded-full flex items-center py-1.5 px-2 sm:px-2.5 sm:py-1.75 bg-grey-fields-200 font-medium text-dark text-1xs flex-shrink-0">
                  <figure className="h-5 w-5 rounded-full overflow-hidden">{countryFlags[form.values.country]}</figure>
                  <span className="ml-1 mr-0.5 text-1xs">{selectedCountry?.text}</span>
                  {/* prettier-ignore */}
                  <svg
                  width="18"
                  viewBox="0 0 20 20"
                  fill="none"
                  className={`transition-transform ml-auto text-dark`}
                >
                  <path
                    d="M15 7.5L10 12.5L5 7.5"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                </button>
              </SelectWithModal>
            </div>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                toggleModal("country_confirmation");
              }}
              className="mt-6.25 sm:mt-7.5 w-full"
            >
              <ErrorLabel perm error={errorText} />
              <InputField label="Full Name" {...getFieldvalues("name", form)} />
              <InputField
                label="Business Name"
                {...getFieldvalues("business_name", form)}
                onBlur={handleBusinessNameBlur}
              />
              {suggestedSlug && (
                <div className="flex items-center gap-1 mt-1">
                  {/* prettier-ignore */}
                  <svg width="14" viewBox="0 0 24 24" fill="none" className="text-accent-green-500">
                    <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M7.75 12L10.58 14.83L16.25 9.17004" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span className="text-dark text-1xs font-medium">{suggestedSlug}.catlog.shop</span>
                  <span className="text-black-placeholder text-xs">(Editable later)</span>
                </div>
              )}
              <InputField label="Email address" {...getFieldvalues("email", form)} />
              <PhoneInput label="Whatsapp number" {...getFieldvalues("phone", form)} />
              <PasswordField label="Your password" {...getFieldvalues("password", form)} />
              <InputWithAddon
                placeholder="Referral Code (optional)"
                {...getFieldvalues("referral_code", form)}
                showAfter={true}
                onBlur={handleRefCodeBlur}
                error={validityErrorText}
              >
                {showAddon && (
                  <div className="bg-white text-dark h-full flex items-center justify-center text-xs rounded-r-10 px-3.5 font-medium">
                    {validityData?.exists && (
                      <>
                        {/* prettier-ignore */}
                        <svg width="18" viewBox="0 0 24 24" fill="none" className="text-accent-green-500">
                        <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M7.75 12L10.58 14.83L16.25 9.17004" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      </>
                    )}
                    {refCodeValidityReq.isLoading && (
                      <>
                        <div className="spinner spinner--sm text-primary-500 !mt-0"></div>
                      </>
                    )}
                    {false && (
                      <>
                        {/* prettier-ignore */}
                        <svg width="18" viewBox="0 0 24 24" fill="none" className="text-accent-red-500">
                          <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M9.16998 14.83L14.83 9.17004" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M14.83 14.83L9.16998 9.17004" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </>
                    )}
                  </div>
                )}
              </InputWithAddon>
              {validityData?.exists && validityData?.user?.name && (
                <div className="flex items-start mt-4 px-3 py-3 rounded-10 bg-grey-fields-100 border border-grey-border border-opacity-50">
                  <figure className="h-8 w-8 rounded-full bg-white shadow-pill text-accent-orange-500 border border-grey-divider flex items-center justify-center flex-shrink-0 text-base">
                    🤩
                  </figure>
                  <div className="ml-3 flex-1 overflow-hidden">
                    <h4 className="font-display font-bold text-black text-1sm -mb-0.5 w-full overflow-hidden overflow-ellipsis whitespace-nowrap">
                      {validityData?.user?.name.split(" ")[0]} invited you to Catlog
                    </h4>
                    <span className="text-dark text-1xs">You'll get some credits when you setup your store</span>
                  </div>
                </div>
              )}
              {/* <ReCaptcha form={form} /> */}
              <AppBtn color="primary" isBlock className="mt-8" type="submit" disabled={isLoading} size="lg">
                {isLoading ? "Signing up..." : "Sign up"}
              </AppBtn>
              <div className="text-center text-sm text-dark mt-2.5">
                Already have an account?{" "}
                <Link href="/login">
                  <a className="inline-block font-semibold text-primary-500">Sign in</a>
                </Link>
              </div>
            </form>
            <StoreCountryModal
              show={modals.country_confirmation.show}
              toggle={() => toggleModal("country_confirmation")}
              country={form.values.country}
              setCountry={(country) => form.setFieldValue("country", country)}
              proceed={() => {
                toggleModal("country_confirmation");
                form.handleSubmit();
              }}
              supportedCountries={supportedCountries?.data ?? []}
            />
          </>
        );
      case Steps.CREATE_STORE:
        return (
          <div className="w-full flex flex-col items-center">
            <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-orange-500 m-auto flex items-center justify-center text-white">
              {/* prettier-ignore */}
              <svg className="w-[30px] sm:w-8" viewBox="0 0 24 24" fill="none">
                <path d="M12 2C9.38 2 7.25 4.13 7.25 6.75C7.25 9.32 9.26 11.4 11.88 11.49C11.96 11.48 12.04 11.48 12.1 11.49C12.12 11.49 12.13 11.49 12.15 11.49C12.16 11.49 12.16 11.49 12.17 11.49C14.73 11.4 16.74 9.32 16.75 6.75C16.75 4.13 14.62 2 12 2Z" fill="currentColor"/>
                <path d="M17.0809 14.1489C14.2909 12.2889 9.74094 12.2889 6.93094 14.1489C5.66094 14.9989 4.96094 16.1489 4.96094 17.3789C4.96094 18.6089 5.66094 19.7489 6.92094 20.5889C8.32094 21.5289 10.1609 21.9989 12.0009 21.9989C13.8409 21.9989 15.6809 21.5289 17.0809 20.5889C18.3409 19.7389 19.0409 18.5989 19.0409 17.3589C19.0309 16.1289 18.3409 14.9889 17.0809 14.1489Z" fill="currentColor"/>
              </svg>
            </figure>
            <h2 className="text-center font-light text-black text-2lg sm:text-[26px] lg:text-3xl mx-auto mt-3.5 !leading-tight">
              Welcome, {form.values.name.split(" ")[0]}
              <br />
              <b className="font-bold">Let's get you set up</b>
            </h2>
            <StoreAdditionalDetailsForm />
          </div>
        );
    }
  };

  return (
    <AuthLayout tags={seoTags}>
      <div className="w-full">
        {renderStepContent(steps)}
        <div className="mx-auto flex items-center justify-center mt-5">
          <div
            className={cx({
              [bubbleActive]: steps == Steps.CREATE_ACCOUNT,
              [bubbleInactive]: steps !== Steps.CREATE_ACCOUNT,
            })}
          ></div>
          <div
            className={cx({
              [bubbleActive]: steps !== Steps.CREATE_ACCOUNT,
              [bubbleInactive]: steps == Steps.CREATE_ACCOUNT,
            })}
          ></div>
        </div>
      </div>
    </AuthLayout>
  );
};

const seoTags = {
  title: "Create a store - Catlog",
  description: "Create a catlog store in less than 5 minutes",
  pageUrl: "/sign-up",
  image: "https://res.cloudinary.com/catlog/image/upload/v1674314911/seo-banners/Dashboard_Banner.png",
};

const validationSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  phone: phoneValidation(),
  email: Yup.string().email("Invalid email address").required("Email address is required"),
  password: Yup.string().required("Please choose a password"),
  // recaptcha_token: Yup.string().required("Recaptcha verification is required"),
});

export async function getStaticProps(context) {
  return {
    props: {}, // will be passed to the page component as props
  };
}

const storeTypes = [
  {
    title: "Storefronts",
    value: STORE_TYPES.REGULAR,
    placeholder: "Get an online store in 5 minutes. Manage your records, collect payments & book deliveries",
    color: "bg-accent-yellow-500 text-accent-yellow-500",
    icon:
      /* prettier-ignore */
      <svg width="50%" viewBox="0 0 24 24" fill="none"  >
        <path d="M22.3596 8.27L22.0696 5.5C21.6496 2.48 20.2796 1.25 17.3497 1.25H14.9896H13.5097H10.4697H8.98965H6.58965C3.64965 1.25 2.28965 2.48 1.85965 5.53L1.58965 8.28C1.48965 9.35 1.77965 10.39 2.40965 11.2C3.16965 12.19 4.33965 12.75 5.63965 12.75C6.89965 12.75 8.10965 12.12 8.86965 11.11C9.54965 12.12 10.7097 12.75 11.9997 12.75C13.2896 12.75 14.4197 12.15 15.1096 11.15C15.8797 12.14 17.0696 12.75 18.3096 12.75C19.6396 12.75 20.8396 12.16 21.5896 11.12C22.1896 10.32 22.4597 9.31 22.3596 8.27Z" fill="currentColor"/>
        <path d="M11.3491 16.6602C10.0791 16.7902 9.11914 17.8702 9.11914 19.1502V21.8902C9.11914 22.1602 9.33914 22.3802 9.60914 22.3802H14.3791C14.6491 22.3802 14.8691 22.1602 14.8691 21.8902V19.5002C14.8791 17.4102 13.6491 16.4202 11.3491 16.6602Z" fill="currentColor"/>
        <path d="M21.3709 14.3981V17.3781C21.3709 20.1381 19.1309 22.3781 16.3709 22.3781C16.1009 22.3781 15.8809 22.1581 15.8809 21.8881V19.4981C15.8809 18.2181 15.4909 17.2181 14.7309 16.5381C14.0609 15.9281 13.1509 15.6281 12.0209 15.6281C11.7709 15.6281 11.5209 15.6381 11.2509 15.6681C9.47086 15.8481 8.12086 17.3481 8.12086 19.1481V21.8881C8.12086 22.1581 7.90086 22.3781 7.63086 22.3781C4.87086 22.3781 2.63086 20.1381 2.63086 17.3781V14.4181C2.63086 13.7181 3.32086 13.2481 3.97086 13.4781C4.24086 13.5681 4.51086 13.6381 4.79086 13.6781C4.91086 13.6981 5.04086 13.7181 5.16086 13.7181C5.32086 13.7381 5.48086 13.7481 5.64086 13.7481C6.80086 13.7481 7.94086 13.3181 8.84086 12.5781C9.70086 13.3181 10.8209 13.7481 12.0009 13.7481C13.1909 13.7481 14.2909 13.3381 15.1509 12.5981C16.0509 13.3281 17.1709 13.7481 18.3109 13.7481C18.4909 13.7481 18.6709 13.7381 18.8409 13.7181C18.9609 13.7081 19.0709 13.6981 19.1809 13.6781C19.4909 13.6381 19.7709 13.5481 20.0509 13.4581C20.7009 13.2381 21.3709 13.7181 21.3709 14.3981Z" fill="currentColor"/>
      </svg>,
  },
  {
    title: "Chowbot",
    value: STORE_TYPES.RESTAURANT,
    placeholder: "Ordering chatbot on whatsapp for food businesses & cloud kitchens + everything in storefronts.",
    color: "bg-accent-green-500 text-accent-green-500",
    icon:
      /* prettier-ignore */
      <svg width="50%" viewBox="0 0 24 24" fill="none">
        <path fillRule="evenodd" clipRule="evenodd" d="M11.9877 0C10.7332 0 9.93211 1.01261 9.93211 2.26704C9.93211 2.79363 10.1416 3.22 10.362 3.6685C10.4138 3.77391 10.4662 3.88055 10.5166 3.98999C10.7029 4.39406 10.8144 4.81727 10.8811 5.25601H8.12534C3.92261 5.25601 0.515625 8.663 0.515625 12.8657C0.515625 14.6305 1.11635 16.2549 2.12447 17.5457L1.28315 24.0049L6.21275 20.2331C6.82386 20.3913 7.46477 20.4754 8.12534 20.4754H15.8699C20.0726 20.4754 23.4796 17.0685 23.4796 12.8657C23.4796 8.663 20.0726 5.25601 15.8699 5.25601H13.1073C13.174 4.81727 13.2855 4.39406 13.4718 3.98999C13.5222 3.88055 13.5746 3.77391 13.6264 3.6685C13.8468 3.21999 14.0563 2.79363 14.0563 2.26704C14.0563 1.01261 13.2421 0 11.9877 0ZM7.35815 11.6131C6.66882 11.6131 6.11 12.1719 6.11 12.8613C6.11 13.5506 6.66882 14.1094 7.35815 14.1094H9.79198C10.4813 14.1094 11.0401 13.5506 11.0401 12.8613C11.0401 12.1719 10.4813 11.6131 9.79198 11.6131H7.35815ZM14.2074 11.6131C13.518 11.6131 12.9592 12.1719 12.9592 12.8613C12.9592 13.5506 13.518 14.1094 14.2074 14.1094H16.6412C17.3305 14.1094 17.8893 13.5506 17.8893 12.8613C17.8893 12.1719 17.3305 11.6131 16.6412 11.6131H14.2074Z" fill="currentColor"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M8.12141 8.44531H15.8646C18.3004 8.44531 20.275 10.4199 20.275 12.8558C20.275 15.2916 18.3004 17.2663 15.8646 17.2663H8.12141C5.68558 17.2663 3.71094 15.2916 3.71094 12.8558C3.71094 10.42 5.68558 8.44531 8.12141 8.44531ZM7.35491 11.6137C6.66557 11.6137 6.10676 12.1725 6.10676 12.8619C6.10676 13.5512 6.66557 14.11 7.35491 14.11H9.78873C10.4781 14.11 11.0369 13.5512 11.0369 12.8619C11.0369 12.1725 10.4781 11.6137 9.78873 11.6137H7.35491ZM14.2041 11.6137C13.5148 11.6137 12.956 12.1725 12.956 12.8619C12.956 13.5512 13.5148 14.11 14.2041 14.11H16.6379C17.3273 14.11 17.8861 13.5512 17.8861 12.8619C17.8861 12.1725 17.3273 11.6137 16.6379 11.6137H14.2041Z" fill="currentColor"/>
      </svg>,
  },
];

export const countryFlags = {
  NG:
    // prettier-ignore
    <svg height="100%" viewBox="0 0 56 40" fill="none" className="transform -translate-x-1/2 relative left-1/2">
      <rect x="0.5" y="0.5" width="55" height="39" rx="3.5" fill="white" stroke="white"/>
      <mask id="mask0_1690_8219" style={getStyles({"mask-type": "alpha"})} maskUnits="userSpaceOnUse" x="0" y="0" width="56" height="40">
        <rect x="0.5" y="0.5" width="55" height="39" rx="3.5" fill="white" stroke="white"/>
      </mask>
      <g mask="url(#mask0_1690_8219)">
        <rect x="35" width="21" height="40" fill="#189B62"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M0 40H21V0H0V40Z" fill="#189B62"/>
      </g>
    </svg>,
  GH:
    //prettier-ignore
    <svg height="100%" viewBox="0 0 56 40" fill="none" className="transform -translate-x-1/2 relative left-1/2">
      <rect width="56" height="40" rx="4" fill="white"/>
      <mask id="mask0_1690_8224" style={getStyles({"mask-type": "alpha"})} maskUnits="userSpaceOnUse" x="0" y="0" width="56" height="40">
        <rect width="56" height="40" rx="4" fill="white"/>
      </mask>
      <g mask="url(#mask0_1690_8224)">
        <path fillRule="evenodd" clipRule="evenodd" d="M0 13.3333H56V0H0V13.3333Z" fill="#E71F37"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M0 40.0001H56V26.6667H0V40.0001Z" fill="#118B56"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M0 26.6666H56V13.3333H0V26.6666Z" fill="#FDD64C"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M28 23.2234L24.0815 26.0601L25.5685 21.4567L21.6597 18.6066L26.4972 18.5982L28 14L29.5028 18.5982L34.3404 18.6066L30.4316 21.4567L31.9186 26.0601L28 23.2234Z" fill="#262626"/>
      </g>
    </svg>,
  ZA:
    //prettier-ignore
    <svg height="100%" viewBox="0 0 15 15" fill="none" className="transform -translate-x-1/2 relative left-1/2">
      <g clipPath="url(#clip0_1450_4217)">
        <rect width="15" height="15" rx="7.5" fill="white"/>
        <path d="M13.5714 0H1.42857C0.639593 0 0 0.89543 0 2V13C0 14.1046 0.639593 15 1.42857 15H13.5714C14.3604 15 15 14.1046 15 13V2C15 0.89543 14.3604 0 13.5714 0Z" fill="white"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M0 10H15V15H0V10Z" fill="#1A47B8"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M0 0H15V5H0V0Z" fill="#F93939"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M5.72357 10L1.42857 15.033H0V0H1.42857L5.71429 5H15V10H5.72357Z" fill="white"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M5.5 9L0.357143 15L0 15.033V0H0.357143L5.5 6H15V9H5.5Z" fill="#249F58"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M0 3L4.28571 7.5L0 12V3Z" fill="#FFDA2C"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M0 4L3.39286 7.5L0 11V4Z" fill="#151515"/>
      </g>
      <defs>
        <clipPath id="clip0_1450_4217">
          <rect width="15" height="15" rx="7.5" fill="white"/>
        </clipPath>
      </defs>
    </svg>,
  KE:
    //prettier-ignore
    <svg height="100%" viewBox="0 0 15 15" fill="none" className="transform -translate-x-1/2 relative left-1/2">
      <g clipPath="url(#clip0_1450_4289)">
        <rect width="15" height="15" rx="7.5" fill="#151515"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M-3 10H18V11H-3V10Z" fill="white"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M-3 11H18V15H-3V11Z" fill="#0A6A30"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M-3 5H18V10H-3V5Z" fill="#F93939"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M-3 4H18V5H-3V4Z" fill="white"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M9.41392 5.914C9.48592 5.914 9.91392 5.244 9.91392 4.414C9.91392 3.586 9.48592 2.914 9.41392 2.914C9.34392 2.914 8.91392 3.586 8.91392 4.414C8.91392 5.244 9.34392 5.914 9.41392 5.914ZM6.47492 5.475C6.42492 5.525 5.64492 5.355 5.06092 4.768C4.47592 4.182 4.30392 3.404 4.35492 3.354C4.40492 3.304 5.18292 3.474 5.76892 4.061C6.35492 4.647 6.52592 5.425 6.47592 5.476L6.47492 5.475ZM8.35392 10.008C8.40392 9.961 9.18392 10.121 9.76892 10.668C10.3539 11.218 10.5259 11.945 10.4759 11.992C10.4259 12.039 9.64592 11.879 9.05992 11.332C8.47492 10.782 8.30292 10.055 8.35392 10.008ZM6.47492 10.008C6.42492 9.961 5.64492 10.121 5.06092 10.668C4.47592 11.218 4.30392 11.945 4.35492 11.992C4.40492 12.039 5.18292 11.879 5.76892 11.332C6.35492 10.782 6.52592 10.055 6.47592 10.008H6.47492Z" fill="white"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M7.5 12C7.857 12 10 9.985 10 7.5C10 5.015 7.857 3 7.5 3C7.143 3 5 5.015 5 7.5C5 9.985 7.143 12 7.5 12Z" fill="#F93939"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M5.5 9C5.57 9 6 8.328 6 7.5C6 6.672 5.57 6 5.5 6C5.43 6 5 6.672 5 7.5C5 8.328 5.43 9 5.5 9ZM9.5 9C9.57 9 10 8.328 10 7.5C10 6.672 9.57 6 9.5 6C9.43 6 9 6.672 9 7.5C9 8.328 9.43 9 9.5 9Z" fill="#151515"/>
        <path d="M8 7.5C8 6.67157 7.77614 6 7.5 6C7.22386 6 7 6.67157 7 7.5C7 8.32843 7.22386 9 7.5 9C7.77614 9 8 8.32843 8 7.5Z" fill="white"/>
      </g>
      <defs>
        <clipPath id="clip0_1450_4289">
          <rect width="15" height="15" rx="7.5" fill="white"/>
        </clipPath>
      </defs>
    </svg>,
};

export default SignUp;
