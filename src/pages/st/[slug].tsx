import { GetServerSideProps } from "next";
import { useRouter } from "next/router";
import React, { useEffect } from "react";

interface ShortUrlPageProps {
  fullUrl?: string;
  error?: boolean;
}

const ShortUrlPage: React.FC<ShortUrlPageProps> = ({ fullUrl, error }) => {
  const router = useRouter();

  useEffect(() => {
    if (error) {
      router.push("/404");
      return;
    }

    if (fullUrl) {
      // Redirect to the full URL
      window.location.href = fullUrl;
    }
  }, [fullUrl, error, router]);

  // Show loading state while redirecting
  return (
    <div className="h-screen w-screen grid place-items-center text-primary-500">
      <div className="flex flex-col items-center space-y-3">
        <div className="spinner"></div>
        <p className="text-sm text-placeholder">Redirecting...</p>
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps<ShortUrlPageProps> = async (context) => {
  const { slug } = context.params as { slug: string };

  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    const response = await fetch(`${apiUrl}/url-shortener/${slug}/full`);

    if (!response.ok) {
      return {
        props: {
          error: true,
        },
      };
    }

    const data = await response.json();

    if (!data.full_url) {
      return {
        props: {
          error: true,
        },
      };
    }

    return {
      props: {
        fullUrl: data.full_url,
      },
    };
  } catch (error) {
    console.error("Error fetching short URL:", error);
    return {
      props: {
        error: true,
      },
    };
  }
};

export default ShortUrlPage;
