import React, { useMemo } from "react";
import DashboardLayout from "../../../../components/ui/layouts/dashboard";
import { MilestoneCard } from "@/components/milestones";
import { milestonesData } from "@/api/interfaces/milestones.interface";
import { millify } from "@/assets/js/utils/functions";
import { useFetcher } from "@/api/utils";
import { GetMilestones } from "@/api";
import { GetServerSideProps } from "next";
import { getIconUrl, milestonesConfig } from "@/assets/js/utils/milestones";
import ErrorScreen from "@/components/ui/error-screen";
import { AppBtn } from "@/components/ui/buttons";
import authContext from "@/contexts/auth-context";
import { CURRENCIES } from "@/assets/interfaces";
import Confetti from "react-confetti";
import { toNaira } from "@/assets/js/utils/utils";

interface Props {
  slug: string;
}

const MileStones: React.FC<Props> = ({ slug }) => {
  const { store } = authContext.useContainer();
  const { response, isLoading } = useFetcher(GetMilestones);
  const milestonePage = milestonesConfig.find((m) => m.slug === slug);

  //convert the progress to naira if the slug is payments
  const progress = useMemo(() => {
    const data = response?.data;
    let _progress = 0;

    if (!data) return 0;

    if (milestonePage.responseKey) {
      _progress = data[milestonePage.responseKey];
    }

    if (slug === "payments") {
      _progress = toNaira(_progress);
    }

    return _progress;
  }, [response, slug]);

  if (!milestonePage) {
    return (
      <>
        <ErrorScreen title="404: This page looks strange" message="Check the url and try again or:">
          <AppBtn href="/" className="mt-5" size="md">
            Go back home
          </AppBtn>
        </ErrorScreen>
      </>
    );
  }

  return (
    <DashboardLayout
      title={milestonePage?.title}
      padding={false}
      breadCrumb={{ parent: { label: "Milestones", path: `/my-store/milestones` } }}
    >
      <div className="overflow-y-scroll h-full flex flex-col relative px-5 sm:px-6.25 lg:px-7.5 pb-10">
        <div className="pt-4.5 sm:pt-5 pb-20">
          {!isLoading && (
            <div className="grid gap-4.5 grid-cols-[repeat(auto-fill,minmax(300px,1fr))]">
              {milestonePage.key && milestonesData?.[milestonePage.key] ? (
                Array.isArray(milestonesData[milestonePage.key]) ? (
                  // milestonesData[milestonePage.key] is an array
                  milestonesData[milestonePage.key]?.map((milestone, index) => (
                    <MilestoneCard
                      title={`${millify(milestone)} ${milestonePage.title}`}
                      icon={getMilestoneIcon(milestone, slug)}
                      description={milestonePage.description(millify(milestone, 0))}
                      key={index}
                      target={milestone}
                      progress={progress}
                      active={progress >= milestone}
                      isLoading={isLoading}
                    />
                  ))
                ) : (
                  // milestonesData[milestonePage.key] is an object (e.g., order_volumes)
                  milestonesData[milestonePage.key]?.[store?.country?.currency]?.map((milestone, index) => (
                    <MilestoneCard
                      title={`${store?.country?.currency} ${millify(milestone)}`}
                      icon={getMilestoneIcon(milestone, slug, store?.country?.currency as CURRENCIES)}
                      description={milestonePage.description(millify(milestone, 0))}
                      key={index}
                      target={milestone}
                      progress={progress}
                      active={progress >= milestone}
                      isLoading={isLoading}
                    />
                  ))
                )
              ) : (
                <></>
              )}
            </div>
          )}

          {isLoading && (
            <div className="grid gap-4.5 grid-cols-[repeat(auto-fill,minmax(300px,1fr))]">
              {Array.from({ length: 6 }).map((_, index) => (
                <div
                  className="border border-grey-divider rounded-15 h-[240px] w-full bg-white p-3.75 md:p-4 relative"
                  key={index}
                >
                  <div className="milestone-bg absolute z-10 h-full w-full top-0 left-0"></div>
                  <div className="relative z-10 flex flex-col items-center justify-center">
                    <div className={`rounded-full w-16 h-16 sm:w-22.5 sm:h-22.5 bg-grey-loader animate-pulse`}></div>
                    <div className="mt-6 flex flex-col items-center text-center  gap-y-1.5">
                      <div className="h-4 bg-grey-loader animate-pulse rounded-full w-30"></div>
                      <div className="h-2.5 bg-grey-loader animate-pulse rounded-full w-60"></div>
                      <div className="h-2.5 bg-grey-loader animate-pulse rounded-full w-64 mt-3.5"></div>
                    </div>
                  </div>
                  <div className="bg-grey-loader absolute right-2 top-2 w-12 h-5 rounded-full"></div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      {!isLoading && <Confetti numberOfPieces={2000} recycle={false} style={{ zIndex: 200 }} />}
    </DashboardLayout>
  );
};

export default MileStones;

const getMilestoneIcon = (milestone: number, key: string, currency: CURRENCIES = CURRENCIES.NGN) => {
  const milestoneData = milestonesConfig.find((m) => m.slug === key);
  if (!milestoneData) return "";

  const iconsConfig = Array.isArray(milestoneData.icons_config)
    ? milestoneData.icons_config
    : (milestoneData.icons_config[currency] as { min_value: number; icon: string }[]);

  const icon = iconsConfig.find((m) => milestone >= m?.min_value)?.icon;

  return getIconUrl(milestoneData.slug, icon);
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const slug = context.params.slug as string;
  return {
    props: {
      slug,
    },
  };
};
