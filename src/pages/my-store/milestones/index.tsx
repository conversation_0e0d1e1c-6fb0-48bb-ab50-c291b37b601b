import { MilestonesGroupCard } from "../../../components/milestones";
import DashboardLayout from "../../../components/ui/layouts/dashboard";
import authContext from "@/contexts/auth-context";
import { getIconUrl, milestonesConfig } from "@/assets/js/utils/milestones";

const MilestonesPage = () => {
  const { store } = authContext.useContainer();

  return (
    <DashboardLayout
      title="Milestones"
      padding={false}
      breadCrumb={{ parent: { label: "Store settings", path: "/my-store" } }}
    >
      <div className="overflow-y-scroll h-full flex flex-col relative px-5 sm:px-6.25 lg:px-7.5 pb-10">
        <div className="pt-4.5 sm:pt-5 pb-20">
          <div className="grid gap-4.5 grid-cols-[repeat(auto-fit,minmax(270px,1fr))] xl:grid-cols-[repeat(auto-fit,minmax(350px,1fr))]">
            {milestonesConfig.map((milestone, index) => (
              <MilestonesGroupCard
                title={milestone.title}
                icon={getIconUrl(milestone.slug, "circle")}
                description={milestone.mainDescription}
                slug={milestone.slug}
                linkLabel={milestone.linkLabel}
                key={index}
              />
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default MilestonesPage;
