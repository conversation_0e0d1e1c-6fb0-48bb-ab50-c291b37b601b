import "../assets/styles/index.scss";
import authContext from "../contexts/auth-context";
import { Toaster } from "react-hot-toast";
import ProgressBar from "@badrap/bar-of-progress";
import Router from "next/router";
import { useEffect } from "react";
import Head from "next/head";
import ErrorBoundary from "@/components/error-boundary";
import useSearchParams from "@/components/hooks/useSearchParams";
import { useLocalObject } from "@/components/hooks/useLocalState";
import { ModalsProvider } from "@/contexts/modals-context";
import { ffmpegContext } from "@/components/hooks/useVideoTranscode";
import mixpanel from "mixpanel-browser";

const progress = new ProgressBar({
  size: 2,
  color: "#EF940F",
  className: "bar-of-progress",
  delay: 100,
});

Router.events.on("routeChangeStart", progress.start);
Router.events.on("routeChangeComplete", progress.finish);
Router.events.on("routeChangeError", progress.finish);

const CURRENT_VERSION = "new-storefront"; // Increment on each deploy

const MyApp = ({ Component, pageProps }) => {
  const paramKeys = ["utm_source", "utm_medium", "utm_campaign", "gclid", "fbp", "fbc", "ttclid", "ttp"];
  const params = useSearchParams(paramKeys);

  const [sourceAdStorage, setSourceAdStorage] = useLocalObject<{
    [key: string]: string | undefined;
  }>("sourceAd", {});

  //handles params from advertising sources
  useEffect(
    () => {
      const sourceAdObj = Object.fromEntries(paramKeys.map((key) => [key, params[key]]).filter(([, value]) => value));

      // Check if the sourceAdObj is not empty before setting it
      // This prevents overwriting the existing state with an empty object
      if (Object.keys(sourceAdObj).length > 0) {
        setSourceAdStorage(sourceAdObj);
      }
    },
    paramKeys.map((key) => params[key])
  );

  //hard reload the page to update the CSP, SW
  useEffect(() => {
    const stored = localStorage.getItem("APP_VERSION");

    if (stored !== CURRENT_VERSION) {
      localStorage.setItem("APP_VERSION", CURRENT_VERSION);
      window.location.href = window.location.pathname + "?reload=" + Date.now();
    }

    mixpanel.init("434ff6e2b32f7ef14336cd1a01d99d91", {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
  }, []);

  return (
    <ErrorBoundary>
      <authContext.Provider>
        <ffmpegContext.Provider>
          <ModalsProvider>
            <Head>
              {/* <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0" /> */}
              <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
            </Head>
            <Component {...pageProps} />
            <Toaster position="bottom-right" reverseOrder={false} />
            {/* <ReactTooltip /> */}
          </ModalsProvider>
        </ffmpegContext.Provider>
      </authContext.Provider>
    </ErrorBoundary>
  );
};

export default MyApp;
