import Router from "next/router";
import React, { useEffect, useRef, useState } from "react";
import React<PERSON>on<PERSON>tti from "react-confetti";
import { CheckRelatedKyc, GetStoreKYC, SubmitKYC } from "../../../api/store.kyc";
import { useFetcher, useRequest } from "../../../api/utils";
import { KYCInfo, KYC_STATUSES, Tier, ID_TYPES } from "../../../assets/interfaces";
import { actionIsAllowed, SCOPES } from "../../../assets/js/utils/permissions";
import scroll from "../../../assets/js/utils/scroll";
import { useListenerState } from "../../../components/hooks/useListener";
import { useModals } from "../../../components/hooks/useModals";
import useSteps from "../../../components/hooks/useSteps";
import {
  KYCAddressInfo,
  KYCBVNInfo,
  KYCBasicInfo,
  <PERSON><PERSON>CFooter,
  KYCIDInfo,
  KYCInfoSummary,
} from "../../../components/payments/invoices";
import CopyKycInfoConfirmationModal from "../../../components/payments/kyc/copy-kyc-confirmation-modal";
import KycOptions from "../../../components/payments/kyc/kyc-options";
import ManualKYCIDInfo from "../../../components/payments/kyc/manual-id-info";
import KYCStatus from "../../../components/payments/kyc/status";
import TierModal from "../../../components/payments/kyc/modals/tier";
import Badge, { BadgeColor } from "../../../components/ui/badge";
import { AppBtn } from "../../../components/ui/buttons";
import ErrorBox from "../../../components/ui/error";
import DashboardLayout from "../../../components/ui/layouts/dashboard";
import authContext from "../../../contexts/auth-context";
import useSearchParams from "@/components/hooks/useSearchParams";

const InvoicesKYC = () => {
  return (
    <DashboardLayout title="Get Verified" padding={false} bannerConfig={{ show: false }} showSupportBtn={true}>
      <KYCMain />
    </DashboardLayout>
  );
};

const KYCMain = () => {
  const { updateStore, store, userRole, user } = authContext.useContainer();
  const [kycInfo, setKycInfo] = useState<KYCInfo>(null);
  const submitKycRequest = useRequest(SubmitKYC);
  const { response, error, isLoading } = useFetcher(GetStoreKYC);
  const { step, isActive, stepIndex, next, previous, canNext, canPrevious, steps, changeStep } = useSteps(
    kycSteps.map((s) => s.key),
    getStep(kycInfo)
  );
  const stepsWrapperRef = useRef<HTMLDivElement>(null);

  const { isChowbotSetup } = useSearchParams(["isChowbotSetup"]);
  const { modals, toggleModal } = useModals(["tier", "copy_confirmation"]);
  const [tier] = useListenerState<Tier>("show-tier-modal", Tier.TIER_1, (tier) => {
    toggleModal("tier");
  });
  const settingUpChowbot = isChowbotSetup === "true";

  const canUpdateKyc = actionIsAllowed({
    userRole,
    permission: SCOPES.WALLETS.CAN_MANAGE_WALLET,
  });
  const [hasDocument, setHasDocument] = useState(false);

  useEffect(() => {
    setHasDocument(document !== undefined && document.body ? true : false);
  }, []);

  useEffect(() => {
    //Handles scrolling the page sections
    if (stepsWrapperRef.current) {
      const stepsWrapper = stepsWrapperRef.current;
      const wrapperScrollTop = stepsWrapper.scrollTop;

      scroll({
        e: stepsWrapper,
        time: 350,
        amount: stepsWrapper.clientHeight * stepIndex - wrapperScrollTop,
        start: stepsWrapper.scrollTop,
        pos: "top",
        shouldScroll: stepIndex < 2,
      });
    }
  }, [step, stepIndex]);

  useEffect(() => {
    const info: KYCInfo = response?.data ?? null;
    setKycInfo(info);

    if (response && (response?.data === null || info?.verification_method !== "MANUAL")) {
      Router.push("/payments/kyc");
    }

    if (info !== null) {
      // Make sure the identity information is correctly set based on what was sent
      if (!info.identity?.type && Router.query?.id_type) {
        // If we came from id-info.tsx with a selected ID type, ensure it's set
        info.identity = {
          ...info.identity,
          type: Router.query.id_type as unknown as ID_TYPES,
          number: Router.query.id_number as string,
        };
        setKycInfo({ ...info });
      }
      changeStep(steps[getStep(info)]);
    }
  }, [response]);

  useEffect(() => {
    if (submitKycRequest.isLoading) {
      changeStep("STATUS");
    }
  }, [submitKycRequest.isLoading]);

  const canGoNext = validateData(kycInfo)[kycSteps[stepIndex].key] && canNext;

  const submitKycInfo = async () => {
    const [res, error] = await submitKycRequest.makeRequest({});

    if (res) {
      setKycInfo(res?.data?.kyc);
      const storeData = res?.data?.store;

      //update state for the rest of the dashboard
      if (storeData) {
        updateStore({
          payments_enabled: storeData?.payments_enabled,
          wallet: storeData?.wallet,
          payment_options: storeData?.payment_options,
        });
      }
    }
  };

  if (!canUpdateKyc) {
    return (
      <ErrorBox
        title="Only owners can enable payments"
        message="We need BVN & ID of the owner of this store to enable payments, Please notify the owner of this store to enable payments."
      >
        <AppBtn size="sm" href="/dashboard" className="mt-5">
          Back to Dashboard
        </AppBtn>
      </ErrorBox>
    );
  }

  return (
    <>
      {isLoading && (
        <div className="h-full relative w-full overflow-hidden px-5 sm:px-6.25 lg:px-7.5 flex flex-col items-center justify-center">
          <div className="spinner spinner--md text-primary-500"></div>
        </div>
      )}

      {!isLoading && !error && (
        <>
          <div className="h-full relative w-full overflow-hidden" ref={stepsWrapperRef}>
            {/* <KYCBasicInfo
              isActive={isActive("BASIC")}
              next={next}
              kycInfo={kycInfo}
              setKycInfo={setKycInfo}
              user={user}
            />
            <KYCBVNInfo isActive={isActive("BVN")} kycInfo={kycInfo} setKycInfo={setKycInfo} next={next} /> */}
            <ManualKYCIDInfo kycInfo={kycInfo} next={next} setKycInfo={setKycInfo} isActive={isActive("ID")} />
            <KYCAddressInfo isActive={isActive("ADDRESS")} kycInfo={kycInfo} setKycInfo={setKycInfo} next={next} />
            <KYCInfoSummary kycInfo={kycInfo} submitKyc={submitKycInfo} previous={previous} />
            <KYCStatus
              kycInfo={kycInfo}
              isSubmitting={submitKycRequest.isLoading}
              error={submitKycRequest.error?.message}
              retry={submitKycInfo}
              submitResponse={submitKycRequest?.response}
              isChowbotSetup={settingUpChowbot}
            />
          </div>
          <KYCFooter
            {...{
              previous,
              next,
              canNext: canGoNext,
              canPrevious,
              step: stepIndex,
              steps: steps.length,
              isManual: true,
            }}
          />
          <TierModal show={modals.tier.show} toggle={() => toggleModal("tier")} tier={tier} />
        </>
      )}

      {hasDocument && (
        <ReactConfetti
          width={window.innerWidth}
          height={window.innerHeight}
          numberOfPieces={500}
          recycle={false}
          run={kycInfo?.status === KYC_STATUSES.APPROVED}
          style={{ zIndex: 9999 }}
        />
      )}
    </>
  );
};

export const validateData = (kycInfo: KYCInfo) => {
  return {
    BASIC: kycInfo !== null && kycInfo?.first_name && kycInfo?.last_name,
    BVN: Boolean(kycInfo?.bvn),
    ID: kycInfo?.identity?.number && kycInfo?.identity?.type && kycInfo?.identity?.url,
    ADDRESS:
      kycInfo?.address?.city && kycInfo?.address?.lga && kycInfo?.address?.address_line1 && kycInfo?.address?.state,
  };
};

const getStep = (kycInfo: KYCInfo) => {
  if (!validateData(kycInfo).ID) {
    return 0;
  }

  if (!validateData(kycInfo).ADDRESS) {
    return 1;
  }

  if (
    kycInfo?.status === KYC_STATUSES.APPROVED ||
    kycInfo?.status === KYC_STATUSES.PENDING ||
    kycInfo?.status === KYC_STATUSES.DENIED
  ) {
    return 3;
  }

  return 2;
};

const kycSteps = [
  {
    key: "ID",
    isSkipable: true,
  },
  {
    key: "ADDRESS",
    isSkipable: true,
  },
  {
    key: "SUMMARY",
    isSkipable: false,
  },
  {
    key: "STATUS",
    isSkipable: false,
  },
];

export default InvoicesKYC;
