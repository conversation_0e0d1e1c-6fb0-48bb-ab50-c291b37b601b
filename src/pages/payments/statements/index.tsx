import { CreatePaymentStatement, DownloadStatementPdf, GetPaymentStatements } from "@/api";
import { CreatePaymentStatementParams, PaymentStatementSearchParams } from "@/api/interfaces";
import { useFetcher, useRequest } from "@/api/utils";
import { PaymentStatement } from "@/assets/interfaces/invoices";
import ClearSearch from "@/components/clear-search";
import { useModals } from "@/components/hooks/useModals";
import usePagination from "@/components/hooks/usePagination";
import useSearchParams from "@/components/hooks/useSearchParams";
import StatementItem from "@/components/payments/statements/statements-item";
import PaymentContentState from "@/components/payments/statements/statements-content-state";
import StatementsSearch from "@/components/payments/statements/statements-search";
import Portal from "@/components/portal";
import SelectDateRange, { DateRange, DateRangeString } from "@/components/ui/form-elements/select-date-range";
import DashboardLayout from "@/components/ui/layouts/dashboard";
import Pagination from "@/components/ui/pagination";
import Table, { TableBody, TableHead, TableHeadItem } from "@/components/ui/table";
import { useState } from "react";
import authContext from "@/contexts/auth-context";
import { copyToClipboard, toAppUrl } from "@/assets/js/utils/functions";
import { toast } from "@/components/ui/toast";
import usePDFDownloads from "@/components/hooks/usePDFDownloads";
import useFluxState from "@/components/hooks/useFluxState";
import { SelectDropdown } from "@/components/ui/form-elements";
import WalletContext from "@/contexts/wallet-context";

const StatementsPage = () => {
  return (
    <DashboardLayout title="Statements">
      <StatementsPageMain />
    </DashboardLayout>
  );
};

export default StatementsPage;

const StatementsPageMain = () => {
  const { store } = authContext.useContainer();
  const [searchQuery, setSearchQuery] = useState("");
  const { search } = useSearchParams(["search"]);
  const PER_PAGE = 10;
  const { currentPage, perPage, setPerPage, goNext, goPrevious, setPage } = usePagination();
  const { modals, toggleModal } = useModals(["range"]);
  const createStatementReq = useRequest<CreatePaymentStatementParams>(CreatePaymentStatement);
  const getStatementsReq = useFetcher<PaymentStatementSearchParams>(GetPaymentStatements, {
    filter: { search: search ? search : "" },
    page: currentPage,
    per_page: PER_PAGE,
    sort: "DESC",
  });

  const [currentShowPinIndex, setCurrentShowPinIndex] = useState<number>(-1);
  // const [currentStatementIndex, setCurrentStatementIndex] = useState<number>(-1);
  const [statements, setStatements] = useFluxState(getStatementsReq?.response?.data?.data ?? []);
  const pageNotReady = statements.length === 0 || getStatementsReq.isLoading || getStatementsReq.error;
  const { isLoadingPdf, download } = usePDFDownloads({
    request: DownloadStatementPdf,
    data: null,
    filename: `Payment statement`,
  });

  const availableCurrencies = store?.wallets?.map((w) => w.currency) ?? [];
  const [selectedCurrency, setSelectedCurrency] = useState(availableCurrencies[0]);

  const handleSelectDateRange = async (range: DateRangeString) => {
    toggleModal("range");
    toast.promise(() => createStatement(range), {
      error: {
        message: "Error occurred while creating payment statement",
        title: "Error",
      },
      loading: {
        message: "Creating payment statement...",
        title: "Creating...",
      },
      success: {
        message: "Successfully payment statement",
        title: "Success",
      },
    });
  };

  const createStatement = async (range: DateRangeString): Promise<void> => {
    return new Promise(async (r, e) => {
      const [res, error] = await createStatementReq.makeRequest({
        start_date: range.startDate,
        end_date: range.endDate,
        currency: selectedCurrency,
      });
      if (res) {
        setStatements([res?.data, ...statements]);
        r();
        return;
      }
      e(error?.message);
    });
  };

  const handleItemAction = (action: "click" | "download" | "copy-link" | "toggle-pin", index: number) => {
    const statementItem = statements[index];
    const url = toAppUrl(`/statements/${statements[index].id}`);

    switch (action) {
      case "toggle-pin":
        if (currentShowPinIndex === index) {
          if (copyToClipboard(statementItem?.pin)) {
            toast.success({
              message: "Successfully copied pin to clipboard",
              title: "Successfull",
            });
          }
          setCurrentShowPinIndex(-1);
        } else setCurrentShowPinIndex(index);
        break;
      case "click":
        window.open(url, "_blank");
        break;

      case "copy-link":
        if (copyToClipboard(url)) {
          toast.success({
            message: "Successfully copied link to clipboard",
            title: "Successfull",
          });
        }
        break;
      case "download":
        const fileName = `statement-${new Date(statementItem?.period?.start_date).toDateString()}-${new Date(
          statementItem?.period?.end_date
        ).toDateString()}`;
        download({ id: statementItem?.id, pin: statementItem.pin }, fileName);
        break;
    }
  };

  return (
    <div className="w-full pb-10">
      <StatementsSearch openRangePicker={() => toggleModal("range")} {...{ searchQuery, setSearchQuery }} />
      <ClearSearch search={search} />
      {pageNotReady && (
        <PaymentContentState
          exportStatement={() => toggleModal("range")}
          request={getStatementsReq}
          isEmpty={statements.length === 0}
        />
      )}
      {!pageNotReady && (
        <>
          <Table>
            <TableHead>
              {/* <TableHeadItem>INVOICE ID</TableHeadItem> */}
              {/* <TableHeadItem>ID</TableHeadItem> */}
              <TableHeadItem>PERIOD</TableHeadItem>
              <TableHeadItem>Currency</TableHeadItem>
              <TableHeadItem>PIN</TableHeadItem>
              <TableHeadItem>Actions</TableHeadItem>
              <TableHeadItem>DATE CREATED</TableHeadItem>
            </TableHead>
            <TableBody>
              {statements.map((item, index) => {
                return (
                  <StatementItem
                    key={index}
                    item={item}
                    onAction={(a) => handleItemAction(a, index)}
                    isLoadingPdf={isLoadingPdf}
                    canShowPin={currentShowPinIndex === index}
                  ></StatementItem>
                );
              })}
            </TableBody>
          </Table>
          <Pagination
            data={getStatementsReq?.response?.data}
            {...{
              currentPage,
              setPage,
              goNext,
              length: statements.length,
              label: "statements",
              goPrevious,
              per_page: perPage,
              setPerPage,
            }}
          />
        </>
      )}
      <Portal>
        <SelectDateRange
          onComplete={(r) => handleSelectDateRange(r)}
          show={modals.range.show}
          toggle={() => toggleModal("range")}
        >
          {availableCurrencies.length > 1 && (
            <>
              <SelectDropdown
                label="Select Currency"
                options={availableCurrencies.map((c) => ({ value: c, text: c }))}
                value={selectedCurrency}
                onChange={(e) => setSelectedCurrency(e.target.value)}
                className="mb-2.5"
              />
              <hr className="w-full border-t border-grey-divider mb-2.5 h-px"></hr>
              {/* <h1 className="mb-1.5">Select Range</h1> */}
            </>
          )}
        </SelectDateRange>
      </Portal>
    </div>
  );
};
