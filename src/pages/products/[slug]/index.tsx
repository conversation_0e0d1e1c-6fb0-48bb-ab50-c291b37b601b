import classNames from "classnames";
import { GetServerSideProps } from "next";
import router from "next/router";
import React, { useEffect, useState } from "react";
import { EditItemParams } from "../../../api/interfaces/items.interface";
import { EditItems, GetItem, GetTieredPricing, GetVariantTemplates } from "../../../api/items";
import { useFetcher, useRequest } from "../../../api/utils";
import {
  InfoBlockInterface,
  PricingTierInterface,
  ProductItemInterface,
  VariantForm,
} from "../../../assets/interfaces";
import { reloadPage } from "../../../assets/js/utils/functions";
import { useModals } from "../../../components/hooks/useModals";
import useScreenSize, { screenSizes } from "../../../components/hooks/useScreenSize";
import Portal from "../../../components/portal";
import {
  DeleteProductModal,
  EditProductModal,
  ProductPageDetails,
  ProductPageMedias,
  ProductPageToolbar,
  ProductPageVariants,
} from "../../../components/products";
import { AppBtn } from "../../../components/ui/buttons";
import ContentState from "../../../components/ui/content-state";
import DataAccordion from "../../../components/ui/data-accordion";
import DashboardLayout from "../../../components/ui/layouts/dashboard";
import authContext from "../../../contexts/auth-context";
import { Product } from "../create";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import { GetStoreInfoBlocks } from "@/api";
import ManageBlocksInProductPage from "@/components/products/info-blocks/manage-blocks-in-product-page";
import { useStorefrontItems } from "@/components/hooks/useStoreItems";

interface ProductPageProps {
  slug?: string;
}
const ProductPage: React.FC<ProductPageProps> = ({ slug }) => {
  const [productName, setProductName] = useState("");

  return (
    <DashboardLayout
      title={productName ?? "Product"}
      padding={false}
      breadCrumb={{ parent: { label: "Products", path: "/products" } }}
    >
      <div className="h-full overflow-y-auto">
        <ProductPageMain slug={slug} setProductName={setProductName} />
      </div>
    </DashboardLayout>
  );
};

interface Props {
  slug: string;
  setProductName: (name: string) => void;
}

const ProductPageMain: React.FC<Props> = ({ slug, setProductName }) => {
  const { categories, subscription, userRole, storeId } = authContext.useContainer();

  const [infoBlocks, setInfoBlocks] = useState<InfoBlockInterface[]>([]);
  const [isDuplication, setIsDuplication] = useState(false);
  const [product, setProduct] = useState<ProductItemInterface>();
  const [pricingTiers, setPricingTiers] = useState<PricingTierInterface[]>([]);

  const { toggleModal, modals } = useModals(["edit", "delete", "variants", "duplicate"]);
  const { width } = useScreenSize();
  const isSmall = width < screenSizes.md;

  const { isLoading, error, response } = useFetcher(GetItem, { slug });
  const updateRequest = useRequest<EditItemParams>(EditItems);
  const getvariantTemplatesReq = useFetcher(GetVariantTemplates, {});
  const templates: Product["variants"][] = getvariantTemplatesReq.response?.data?.templates || [];
  const blocksReq = useFetcher(GetStoreInfoBlocks, { id: storeId });
  const pageNotReady = isLoading || error || !response?.data;

  const { items: storeItems, fetchItemsReq, getItem } = useStorefrontItems(storeId);
  const pricingTiersReq = useFetcher(GetTieredPricing, {
    filter: {},
    page: 1,
    per_page: Number.MAX_SAFE_INTEGER,
  });

  const canUploadVideos = actionIsAllowed({
    planPermission: SCOPES.PRODUCTS.UPLOAD_VIDEOS,
    plan: subscription?.plan?.type ?? "STARTER",
  });

  const canEditProducts = actionIsAllowed({
    userRole,
    permission: SCOPES.PRODUCTS.EDIT_PRODUCTS,
  });

  useEffect(() => {
    setProduct(response?.data);
    setProductName(response?.data?.name);
  }, [response]);

  useEffect(() => {
    if (blocksReq.response?.data?.info_blocks) {
      setInfoBlocks(blocksReq.response?.data?.info_blocks);
    }
  }, [blocksReq.response]);

  useEffect(() => {
    if (pricingTiersReq.response?.data) {
      setPricingTiers(pricingTiersReq.response?.data);
    }
  }, [pricingTiersReq.response]);

  const handleToggle = (key: string) => {
    if (key === "edit") {
      setIsDuplication(false);
      if (!canEditProducts) return;
    } else if (key === "duplicate") setIsDuplication(true);
    toggleModal(key as any);
  };

  return (
    <>
      {pageNotReady && (
        <ContentState
          isLoading={isLoading}
          loadingText="Loading product"
          errorTitle="Sorry we couldn't get this item"
          error={error}
          errorMessage="We couldn't load your item please reload page"
          errorAction={
            <AppBtn className="mt-5" size="md" onClick={reloadPage}>
              Reload page
            </AppBtn>
          }
        ></ContentState>
      )}
      {!pageNotReady && (
        <>
          <ProductPageToolbar
            updateAvailability={(state) => setProduct({ ...product, available: state })}
            editProduct={() => handleToggle("edit")}
            duplicateProduct={() => handleToggle("duplicate")}
            deleteProduct={() => toggleModal("delete")}
            product={product}
            canEditProducts={canEditProducts}
          />
          <div className="sm:px-6.25 lg:px-7.5 px-4">
            <div className="pt-5 sm:pt-6.25 lg:pt-7.5 pb-3.75">
              <div className="flex items-center gap-2">
                <h4 className={`text-black text-base sm:text-lg lg:text-xl font-bold font-display flex-shrink-0`}>
                  Product Medias
                </h4>
                <span className="text-xs sm:text-1xs text-dark mt-1 flex-shrink-0 inline-flex py-1 px-2 bg-grey-fields-100 rounded-5 font-medium">
                  Tap to change thumbnail
                </span>
              </div>

              <div className="mt-2.5 sm:mt-3.5 lg:mt-5 gap-5 overflow-x-hidden ">
                <ProductPageMedias updateProduct={(p) => setProduct(p)} updateRequest={updateRequest} item={product} />
              </div>
            </div>

            <div
              className={classNames("lg:grid border-t border-grey-border border-opacity-50", {
                "grid-cols-[45%,55%]": product?.variants?.options.length > 0,
                "grid-cols-2": !(product?.variants?.options.length > 0),
              })}
            >
              <div className="pt-3.75 sm:pt-5 lg:pt-7.5 lg:pr-4 pb-3.75">
                <DataAccordion deactivated={!isSmall} title="Product Details">
                  <div className="pt-5">{product && <ProductPageDetails data={product} />}</div>
                  {product && product.info_blocks.length > 0 && (
                    <ManageBlocksInProductPage
                      product={product}
                      setProduct={(product: ProductItemInterface) => setProduct(product)}
                      infoBlocks={product?.info_blocks}
                      blocksLoading={blocksReq.isLoading}
                      setInfoBlocks={setInfoBlocks}
                      blocksError={blocksReq.error}
                    />
                  )}
                </DataAccordion>
              </div>
              <div className="lg:border-l border-grey-border border-t border-opacity-50 lg:border-t-[0px] lg:pl-4 pt-5 relative max-h-[70vh] overflow-y-auto">
                <DataAccordion deactivated={true} title="Product Options" className="!pb-0">
                  <ProductPageVariants
                    subscription={subscription}
                    product={product}
                    updateProduct={setProduct}
                    canEditProducts
                  />
                </DataAccordion>
              </div>
            </div>
          </div>
        </>
      )}
      {product && (
        <Portal>
          <EditProductModal
            show={isDuplication ? modals.duplicate.show : modals.edit.show}
            toggle={() => toggleModal(isDuplication ? "duplicate" : "edit")}
            item={isDuplication ? { ...product, name: `${product?.name} (Duplicate)`, images: [] } : product}
            categories={categories}
            updateItemList={() => null}
            infoBlocks={infoBlocks}
            blocksLoading={blocksReq.isLoading}
            setInfoBlocks={setInfoBlocks}
            blocksError={blocksReq.error}
            isDuplication={isDuplication}
            updateItem={(item) => setProduct(item)}
            templates={templates}
            pricingTiers={pricingTiers}
            setPricingTiers={setPricingTiers}
            storeItems={storeItems}
            fetchItemsReq={fetchItemsReq}
            getItem={getItem}
          />
          <DeleteProductModal
            toggle={() => toggleModal("delete")}
            show={modals.delete.show}
            item={product}
            deleteItem={() => router.push("/products/")}
          />
        </Portal>
      )}
    </>
  );
};

export default ProductPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const slug = context.params.slug as string;
  return {
    props: {
      slug,
    },
  };
};
