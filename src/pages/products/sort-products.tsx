import DashboardLayout from "@/components/ui/layouts/dashboard";
import SortItem from "@/components/products/sort-products/sort-item";
import { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { GetSortItemsParams, UpdateSortItemParams } from "../../api/interfaces/items.interface";
import { GetSortItems, UpdateSortItems } from "../../api/items";
import { useFetcher, useRequest } from "../../api/utils";
import { ProductItemInterface } from "../../assets/interfaces";
import { paginateArray, reloadPage } from "../../assets/js/utils/functions";
import { AppBtn, RoundActionBtn } from "../../components/ui/buttons";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { SortableContext, rectSortingStrategy } from "@dnd-kit/sortable";
import authContext from "../../contexts/auth-context";
import usePagination from "../../components/hooks/usePagination";
import useScreenSize from "../../components/hooks/useScreenSize";
import ContentState from "../../components/ui/content-state";
import Pagination from "../../components/ui/pagination";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";

import ErrorBox from "../../components/ui/error";
import Dropdown, { DropdownItem } from "@/components/ui/dropdown-new";
import classNames from "classnames";
import { toast } from "@/components/ui/toast";
import useSearchParams from "@/components/hooks/useSearchParams";

const PER_PAGE = 15;

const SortProducts = () => {
  const { store, subscription } = authContext.useContainer();

  const { currentPage, perPage, setPerPage, goNext, goPrevious, setPage } = usePagination();
  const { page: pageFromParams } = useSearchParams(["page"]);
  const [items, setItems] = useState<Partial<ProductItemInterface>[]>([]);
  const [selected, setSelected] = useState<string[]>([]);
  const selectedRef = useRef<string[]>(selected);
  const [itemsChanged, setItemsChanged] = useState(false);

  const { response, error, isLoading, makeRequest } = useFetcher<GetSortItemsParams>(GetSortItems, {});
  const updateSortItemsRequest = useRequest<UpdateSortItemParams>(UpdateSortItems);

  const pointerSensor = useSensor(PointerSensor, {
    activationConstraint: {
      distance: 0.01,
    },
  });
  const mouseSensor = useSensor(MouseSensor);
  const touchSensor = useSensor(TouchSensor);
  const keyboardSensor = useSensor(KeyboardSensor);
  const sensors = useSensors(mouseSensor, touchSensor, keyboardSensor, pointerSensor);

  const canManageItems = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_ITEMS,
  });

  const paginatedItems = useMemo(() => {
    const data = paginateArray(items, currentPage, PER_PAGE);
    if (!itemsChanged) {
      setSelected([]);
    }
    return data;
  }, [items, currentPage]);

  const dropdownItems: DropdownItem[] = useMemo(
    () =>
      Array.from({ length: paginatedItems.totalPages }, (_, i) => i + 1)
        .filter((page) => page !== currentPage)
        .map((page) => ({
          text: "Page " + page,
          icon: <RoundActionBtn size="sm" icon="add_circle" />,
          onClick: () => moveToPage(page),
        })),
    [paginatedItems.totalPages, currentPage]
  );

  useEffect(() => {
    selectedRef.current = selected;
  }, [selected]);

  useEffect(() => {
    if (pageFromParams) setPage(parseInt(pageFromParams));
  }, [pageFromParams]);

  useEffect(() => {
    if (response?.items) {
      setItems(response?.items ?? []);
    }
  }, [response]);

  const handleCheckboxChange = useCallback((id: string) => {
    setSelected((prevSelected) => {
      if (prevSelected.includes(id)) {
        return prevSelected.filter((item) => item !== id);
      } else {
        return [...prevSelected, id];
      }
    });
  }, []);

  const handleUpdateSort = async () => {
    const sortedItems = items.map((item, index) => ({
      id: item.id,
      sort_index: paginatedItems.totalItems - index,
    }));
    const [res, error] = await updateSortItemsRequest.makeRequest({ items: sortedItems });

    if (res) {
      toast.success({
        title: "Sort Order Updated successfully",
        message: "The sort order has been updated successfully",
      });
      setItemsChanged(false);
    } else {
      toast.error({
        title: "Something went wrong",
        message: "There was an error updating the sort order",
      });
    }
  };

  const moveToPage = (page: number) => {
    // Split items into pages
    const currentSelected = selectedRef.current;
    const pages = splitItemsIntoPages(items, PER_PAGE);
    const currentPageIndex = currentPage - 1;
    const targetPageIndex = page - 1;

    let currentPageItems = pages[currentPageIndex] || [];
    let targetPageItems = pages[targetPageIndex] || [];

    let [selectedItems, updatedCurrentPageItems] = currentPageItems.reduce(
      ([selected, updated], item) =>
        currentSelected.includes(item.id) ? [[...selected, item], updated] : [selected, [...updated, item]],
      [[], []]
    );

    if (page > currentPage) {
      //remove & replace the selected items from the current page
      const removedItemsFromTargetPage = targetPageItems.splice(0, selectedItems.length, ...selectedItems);
      targetPageItems = [...removedItemsFromTargetPage, ...targetPageItems]; //add the removed items back in front of the same page - when regrouping happens they'll be automatically moved to a page before
    } else {
      const noOfItemsToMove = selectedItems.length;
      // Remove items from the end of the target page and replace with selected items
      const removedItemsFromTargetPage = targetPageItems.splice(
        targetPageItems.length - noOfItemsToMove,
        noOfItemsToMove,
        ...selectedItems
      );
      targetPageItems = [...targetPageItems, ...removedItemsFromTargetPage];
    }

    pages[currentPageIndex] = updatedCurrentPageItems;
    pages[targetPageIndex] = targetPageItems;

    setItems(pages.flat());
    setItemsChanged(true);
    setSelected([]);
    setPage(page);
  };

  const handleDragEnd = useCallback(
    (event: { active: any; over: any }) => {
      const { active, over } = event;
      if (!over || active.id === over.id) return;

      setItems((prevItems) => {
        const itemsCopy = [...prevItems];
        const activeIndex = itemsCopy.findIndex((item) => item.id === active.id);
        const overIndex = itemsCopy.findIndex((item) => item.id === over.id);

        const activeItem = itemsCopy[activeIndex];
        itemsCopy.splice(activeIndex, 1);
        itemsCopy.splice(overIndex, 0, activeItem);

        return itemsCopy;
      });
      setItemsChanged(true);
    },
    [setItems]
  );

  if (!canManageItems) {
    return (
      <ErrorBox title="Upgrade required" message="Upgrade to the basic or business plus plan to manage products">
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  if (isLoading || error || !response || items.length < 1) {
    return (
      <DashboardLayout
        title="Sort Products"
        padding={false}
        breadCrumb={{ parent: { label: "Products", path: "/products" } }}
      >
        <ContentState
          isLoading={isLoading}
          loadingText="Loading products"
          errorTitle="Couldn't load items"
          error={error}
          errorMessage="We couldn't load your items please click the button to retry"
          errorAction={
            <AppBtn size="md" onClick={reloadPage}>
              Reload Items
            </AppBtn>
          }
          isEmpty={items.length < 1}
          emptyIcon={
            <>
              {/* prettier-ignore */}
              <svg width="45%" viewBox="0 0 20 20" fill="none">
              <path d="M17.534 5.99169L10.0006 10.35L2.46729 5.99169C2.80062 5.37502 3.28395 4.83335 3.82562 4.53335L8.27562 2.06669C9.22562 1.53335 10.7756 1.53335 11.7256 2.06669L16.1756 4.53335C16.7173 4.83335 17.2006 5.37502 17.534 5.99169Z" fill="#F0F0F0" />
              <path d="M10.0005 10.35V18.3333C9.37552 18.3333 8.75052 18.2 8.27552 17.9333L3.82552 15.4666C2.81719 14.9083 1.99219 13.5083 1.99219 12.3583V7.64164C1.99219 7.1083 2.17552 6.52497 2.46719 5.99164L10.0005 10.35Z" fill="#B1B1B1" />
              <path d="M18.0088 7.64164V12.3583C18.0088 13.5083 17.1838 14.9083 16.1755 15.4666L11.7255 17.9333C11.2505 18.2 10.6255 18.3333 10.0005 18.3333V10.35L17.5338 5.99164C17.8255 6.52497 18.0088 7.1083 18.0088 7.64164Z" fill="#858484"/>
            </svg>
            </>
          }
          title="No Products to show"
          description="Create a new product"
        >
          <AppBtn size="md" className="max-w-[240px] m-auto" href={`/products/create`}>
            Create Product
          </AppBtn>
        </ContentState>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Sort Products"
      padding={false}
      breadCrumb={{ parent: { label: "Products", path: "/products" } }}
    >
      <div className="h-full overflow-y-auto relative">
        <div className="px-5 sm:px-6.25 lg:px-7.5 pt-5 sm:pt-7.5 lg:pt-8.75">
          <div className="flex justify-between items-center pb-5">
            <h4 className={`text-black text-[17px] sm:text-lg lg:text-xl font-bold font-display`}>
              {"Page"} {currentPage}
            </h4>
            <div className={`flex items-stretch`}>
              <div className={`ml-3 md:ml-3.5 flex gap-2`}>
                {selected.length > 0 && (
                  <>
                    <AppBtn color="dangerLight" size="md" onClick={() => setSelected([])}>
                      Clear Selection
                    </AppBtn>
                    <Dropdown items={dropdownItems} vPosition="BOTTOM" gap={false} size="lg" maxHeight="36vh">
                      {(isOpen) => (
                        <AppBtn className="z-[999] dropdown-toggle" disabled={updateSortItemsRequest.isLoading}>
                          <span>Move To Page</span>
                          {/* prettier-ignore */}
                          <svg viewBox="0 0 20 20" fill="none" className={classNames(`transition-transform ml-0.75 sm:ml-1 transform flex-shrink-0 w-4.5 sm:w-[22px]`, { "rotate-180": isOpen } )}>
                            <path d="M15 7.5L10 12.5L5 7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </AppBtn>
                      )}
                    </Dropdown>
                  </>
                )}
                {selected.length === 0 && (
                  <AppBtn
                    size="md"
                    onClick={handleUpdateSort}
                    disabled={!itemsChanged || updateSortItemsRequest.isLoading}
                  >
                    {updateSortItemsRequest.isLoading ? "Loading..." : "Save Changes"}
                  </AppBtn>
                )}
              </div>
            </div>
          </div>
          <div className="sticky top-0 bg-white z-50"></div>
          <div className="">
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <SortableContext items={paginatedItems.items} strategy={rectSortingStrategy}>
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4.5">
                  {paginatedItems.items.map((product, id) => (
                    <SortItem
                      name={product.name}
                      id={product.id}
                      checked={selected.includes(product.id)}
                      onCheck={() => handleCheckboxChange(product.id)}
                      price={product.price}
                      imageUrl={product.images[product.thumbnail]}
                      key={id}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          </div>
          <Pagination
            data={{
              total: paginatedItems.totalItems,
              total_pages: paginatedItems.totalPages,
              prev_page: currentPage > 1 ? currentPage - 1 : undefined,
              next_page: paginatedItems.hasNextPage,
            }}
            {...{
              currentPage,
              setPage,
              goNext,
              length: paginatedItems.items.length,
              label: "products",
              goPrevious,
              per_page: perPage,
              setPerPage,
            }}
          />
        </div>
      </div>
    </DashboardLayout>
  );
};

const splitItemsIntoPages = (items: Partial<ProductItemInterface>[], perPage) => {
  const pages: Partial<ProductItemInterface>[][] = [];
  for (let i = 0; i < items.length; i += perPage) {
    pages.push(items.slice(i, i + perPage));
  }
  return pages;
};

export default SortProducts;
