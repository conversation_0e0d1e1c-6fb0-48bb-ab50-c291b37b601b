import Portal from "@/components/portal";
import { Discounts, DiscountTypeModal } from "../../components/products/discounts";
import ProductLayout, { productPageIcons } from "../../components/ui/layouts/product";
import { useModals } from "@/components/hooks/useModals";
import { useRouter } from "next/router";

const DiscountsPage = () => {
  const { modals, toggleModal } = useModals(["discount_type"]);
  const router = useRouter();
  const layoutData = {
    title: "Discounts",
    featureInfo: {
      title: "Discounts",
      description: " Create & manage discounts to be applied to multiple products",
      icon: <div className="w-7 text-accent-yellow-500">{productPageIcons.discount}</div>,
    },
    pageIndex: 2,
    action: {
      placeholder: "Create Discount",
      onAction: () => toggleModal("discount_type")
    },
    onSearch: (q: string) => null,
    pageKey: "discounts",
  };

  return (
    <ProductLayout {...layoutData}>
      <Discounts />
      <Portal>
        <DiscountTypeModal
        show={modals?.discount_type?.show}
        toggle={() => toggleModal("discount_type")}
        action={(type: "normal" | "bulk") => {
          toggleModal("discount_type");
          if(type == "normal"){
            router.push("/products/create-discount");
          }
          if(type == "bulk"){
            router.push("/products/create-bulk-discount");
          }
        }}
        />
      </Portal>
    </ProductLayout>
  );
};

export default DiscountsPage;
