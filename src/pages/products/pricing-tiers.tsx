import { useState } from "react";
import ProductLayout, { productPageIcons } from "../../components/ui/layouts/product";
import { InfoBlocksMain } from "../../components/products";
import { useModals } from "@/components/hooks/useModals";
import authContext from "@/contexts/auth-context";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import PricingTiersMain from "@/components/products/pricing-tiers";
import { Layer } from "iconsax-react";

const PricingTiersPage = () => {
  //   const [showModal, setShowModal] = useState(false);
  const { modals, toggleModal } = useModals(["create", "edit", "delete", "assign_items", "assigned_items", "view"]);
  const { subscription } = authContext.useContainer();
  let canManage = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS,
  });

  const layoutData = {
    title: "Tiered Pricing",
    featureInfo: {
      title: "Tiered Pricing",
      description:
        "Give discounts to your customers based on the quantity of items they buy. Perfect for wholesale or bulk orders",
      icon: <div className="w-6 text-accent-orange-500">{<Layer variant="Bulk" size={24} />}</div>,
    },
    pageIndex: 7,
    action: {
      placeholder: "Add Pricing Tier",
      onAction: () => toggleModal("create"),
      disabled: !canManage,
    },
    onSearch: (q: string) => null,
    pageKey: "pricing-tiers",
  };

  return (
    <ProductLayout {...layoutData}>
      <PricingTiersMain modals={modals} toggleModal={toggleModal} />
    </ProductLayout>
  );
};

export default PricingTiersPage;
