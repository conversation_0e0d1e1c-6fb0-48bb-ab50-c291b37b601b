import { useEffect, useState } from "react";
import { subdomainStoreLink, toAppUrl } from "../../assets/js/utils/functions";
import useCopyClipboard from "../../components/hooks/useCopyClipboard";
import { AppBtn } from "../../components/ui/buttons";
import DashboardLayout from "../../components/ui/layouts/dashboard";
import authContext from "../../contexts/auth-context";
import Confetti from "react-confetti";
import EditStoreLinkModal from "../../components/store/edit-store-link-modal";
import { useModals } from "../../components/hooks/useModals";
import SuccessIcon from "../../assets/icons/statuses/success.svg";
import SuccessAnimation from "../../components/ui/success-animation";
import PreviewStoreModal from "@/components/setup/preview-store-modal";
import Portal from "@/components/portal";

const SetupSuccess = () => {
  return (
    <DashboardLayout
      title="Setup complete"
      sidebarLoader={{ store: false, navLinks: true, isOnboarding: true }}
      bannerConfig={{ show: false, content: false }}
    >
      <div className="mt-20">
        <SetupSuccessMain />
      </div>
    </DashboardLayout>
  );
};

const SetupSuccessMain = () => {
  const [hasDocument, setHasDocument] = useState(false);
  const { store, getNewToken, updateStore } = authContext.useContainer();
  const { modals, toggleModal } = useModals(["edit_store_link", "preview_store"]);
  const [copied, copy] = useCopyClipboard(subdomainStoreLink(store?.slug, true), {
    successDuration: 1000,
  });

  useEffect(() => {
    setHasDocument(window !== undefined);
    getNewToken(store.id);
  }, []);

  if (!store) {
    return null;
  }

  return (
    <>
      <div className="grid w-full h-full place-items-center">
        <div className="flex flex-col items-center w-full">
          <figure className="w-22.5 h-22.5 sm:w-25 sm:h-25 mb-3.5">
            <SuccessAnimation />
          </figure>
          <h2 className="text-center text-black text-2lg sm:text-[26px] lg:text-3xl mx-auto !leading-tight font-light">
            Say Hello 👋🏾 to
            <br />
            <b className="font-bold">Your Online Store</b>
          </h2>
          <div className="bg-grey-fields-100 rounded-xl mt-10 text-dark cursor-pointer max-w-md">
            <div className="px-6 sm:px-10 py-5 flex flex-col items-center">
              <span className="text-center block text-1xs sm:text-sm mb-4">
                This is the link to your store, Share it with customers and add it to your social profiles
              </span>
              <span className="text-primary-500 inline-flex items-center" onClick={() => copy()}>
                {/* prettier-ignore */}
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="mr-1.5 h-4 text-primary-500">
                  <path d="M7.39967 15.1666H4.59967C1.99301 15.1666 0.833008 14.0066 0.833008 11.4V8.59998C0.833008 5.99331 1.99301 4.83331 4.59967 4.83331H7.39967C10.0063 4.83331 11.1663 5.99331 11.1663 8.59998V11.4C11.1663 14.0066 10.0063 15.1666 7.39967 15.1666ZM4.59967 5.83331C2.53301 5.83331 1.83301 6.53331 1.83301 8.59998V11.4C1.83301 13.4666 2.53301 14.1666 4.59967 14.1666H7.39967C9.46634 14.1666 10.1663 13.4666 10.1663 11.4V8.59998C10.1663 6.53331 9.46634 5.83331 7.39967 5.83331H4.59967V5.83331Z" fill="currentColor"/>
                  <path d="M11.3997 11.1666H10.6663C10.393 11.1666 10.1663 10.94 10.1663 10.6666V8.59998C10.1663 6.53331 9.46634 5.83331 7.39967 5.83331H5.33301C5.05967 5.83331 4.83301 5.60665 4.83301 5.33331V4.59998C4.83301 1.99331 5.99301 0.833313 8.59967 0.833313H11.3997C14.0063 0.833313 15.1663 1.99331 15.1663 4.59998V7.39998C15.1663 10.0066 14.0063 11.1666 11.3997 11.1666ZM11.1663 10.1666H11.3997C13.4663 10.1666 14.1663 9.46665 14.1663 7.39998V4.59998C14.1663 2.53331 13.4663 1.83331 11.3997 1.83331H8.59967C6.53301 1.83331 5.83301 2.53331 5.83301 4.59998V4.83331H7.39967C10.0063 4.83331 11.1663 5.99331 11.1663 8.59998V10.1666Z" fill="currentColor"/>
                </svg>
                <span className="text-1xs sm:text-sm inline-block font-medium">
                  {copied ? "Copied!" : subdomainStoreLink(store?.slug, true)}
                </span>
              </span>
            </div>
            {store?.flags?.uses_chowbot && (
              <div className="border-t border-grey-border border-opacity-50 text-center px-6 sm:px-10 py-3.5">
                <div className="text-placeholder text-xs block !leading-tight">
                  This is your Catlog storefront link, not your chowbot link. Your chowbot link will be generated when
                  you finish setting up
                </div>
              </div>
            )}

            {!store?.flags?.uses_chowbot && (
              <div className="border-t border-grey-border border-opacity-50 text-center px-6 sm:px-10 py-3.5 flex items-center justify-center">
                <button
                  className="flex items-center text-primary-500 font-medium text-sm mt-1.5 outline-none"
                  onClick={() => toggleModal("preview_store")}
                >
                  Preview Your Store
                  {/* prettier-ignore */}
                  <svg className="w-2 sm:w-2.5 ml-1" viewBox="0 0 10 10" fill="none">
                <path d="M1.24264 0.24265V1.73818L7.16643 1.74348L0.71231 8.1976L1.77297 9.25826L8.22709 2.80414L8.23239 8.72793H9.72792V0.24265H1.24264Z" fill="#332089"/>
              </svg>
                </button>
              </div>
            )}
          </div>
          <div className="mt-5 flex items-center text-black-secondary text-sm">
            Customize your link:
            <button
              className="flex items-center text-primary-500 font-semibold ml-1 flex-shrink-0"
              onClick={() => toggleModal("edit_store_link")}
            >
              <span className="pt-0.5 inline-block">Edit Link</span>
              {/* prettier-ignore */}
              <svg width="14" viewBox="0 0 16 16" fill="none" className="ml-0.75">
              <path d="M8.83958 2.4L3.36624 8.19334C3.15958 8.41334 2.95958 8.84667 2.91958 9.14667L2.67291 11.3067C2.58624 12.0867 3.14624 12.62 3.91958 12.4867L6.06624 12.12C6.36624 12.0667 6.78624 11.8467 6.99291 11.62L12.4662 5.82667C13.4129 4.82667 13.8396 3.68667 12.3662 2.29334C10.8996 0.913335 9.78624 1.4 8.83958 2.4Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M7.92676 3.36664C8.21342 5.20664 9.70676 6.6133 11.5601 6.79997" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M2 14.6667H14" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            </button>
          </div>
          <AppBtn href="/setup/pick-plan" className="sm:max-w-md w-full mt-10" size="lg" isBlock>
            Continue
          </AppBtn>
        </div>
        {hasDocument && (
          <Confetti
            width={window.innerWidth}
            height={window.innerHeight}
            numberOfPieces={500}
            recycle={false}
            style={{ zIndex: 1000 }}
          />
        )}

        <EditStoreLinkModal
          show={modals?.edit_store_link.show}
          toggle={() => toggleModal("edit_store_link")}
          {...{ updateStore, store }}
        />
      </div>
      <Portal>
        <PreviewStoreModal
          show={modals?.preview_store.show}
          toggle={() => toggleModal("preview_store")}
          storeLink={subdomainStoreLink(store?.slug, true)}
        />
      </Portal>
    </>
  );
};

export default SetupSuccess;
