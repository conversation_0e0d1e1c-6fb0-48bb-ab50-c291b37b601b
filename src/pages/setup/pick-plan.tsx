import { useFetcher } from "@/api/utils";
import { SubscriptionsController } from "../../components/subscriptions";
import DashboardLayout from "../../components/ui/layouts/dashboard";
import { GetStoreCredits } from "@/api/credits-and-referrals";
import authContext from "@/contexts/auth-context";
import { toNaira } from "@/assets/js/utils/utils";
import { useModals } from "@/components/hooks/useModals";
import Portal from "@/components/portal";
import SubscriptionBenefits from "@/components/subscriptions/subscription-benefits";

const PlanPlan = () => {
  const { modals, toggleModal } = useModals(["subscription_benefits"]);
  const { store } = authContext.useContainer();
  const creditsReq = useFetcher(GetStoreCredits);
  const credits = creditsReq?.response?.data;

  return (
    <DashboardLayout
      title="Pick a plan"
      sidebarLoader={{ navLinks: true, store: false, isOnboarding: true }}
      bannerConfig={{ show: false, content: false }}
      padding={false}
      showSupportBtn
    >
      <div className="py-15 overflow-y-auto h-full w-full px-5 sm:px-6.25 lg:px-7.5">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-orange-500 m-auto flex items-center justify-center text-white flex-shrink-0">
          {/* prettier-ignore */}
          <svg className="w-[30px] sm:w-8" viewBox="0 0 30 30" fill="none">
            <path d="M23.75 18.75C20.9875 18.75 18.75 20.9875 18.75 23.75C18.75 24.6875 19.0125 25.575 19.475 26.325C20.3375 27.775 21.925 28.75 23.75 28.75C25.575 28.75 27.1625 27.775 28.025 26.325C28.4875 25.575 28.75 24.6875 28.75 23.75C28.75 20.9875 26.5125 18.75 23.75 18.75ZM26.3375 23.2125L23.675 25.675C23.5 25.8375 23.2625 25.925 23.0375 25.925C22.8 25.925 22.5625 25.8375 22.375 25.65L21.1375 24.4125C20.775 24.05 20.775 23.45 21.1375 23.0875C21.5 22.725 22.1 22.725 22.4625 23.0875L23.0625 23.6875L25.0625 21.8375C25.4375 21.4875 26.0375 21.5125 26.3875 21.8875C26.7375 22.2625 26.7125 22.85 26.3375 23.2125Z" fill="currentColor"/>
            <path d="M27.5 9.4375V10C27.5 10.6875 26.9375 11.25 26.25 11.25H3.75C3.0625 11.25 2.5 10.6875 2.5 10V9.425C2.5 6.5625 4.8125 4.25 7.675 4.25H22.3125C25.175 4.25 27.5 6.575 27.5 9.4375Z" fill="currentColor"/>
            <path d="M2.5 14.3748V20.5748C2.5 23.4373 4.8125 25.7498 7.675 25.7498H15.5C16.225 25.7498 16.85 25.1373 16.7875 24.4123C16.6125 22.4998 17.225 20.4248 18.925 18.7748C19.625 18.0873 20.4875 17.5623 21.425 17.2623C22.9875 16.7623 24.5 16.8248 25.8375 17.2748C26.65 17.5498 27.5 16.9623 27.5 16.0998V14.3623C27.5 13.6748 26.9375 13.1123 26.25 13.1123H3.75C3.0625 13.1248 2.5 13.6873 2.5 14.3748ZM10 21.5623H7.5C6.9875 21.5623 6.5625 21.1373 6.5625 20.6248C6.5625 20.1123 6.9875 19.6873 7.5 19.6873H10C10.5125 19.6873 10.9375 20.1123 10.9375 20.6248C10.9375 21.1373 10.5125 21.5623 10 21.5623Z" fill="currentColor"/>
          </svg>
        </figure>
        <div className="text-center w-full max-w-[500px] mx-auto">
          <h2 className="text-center font-light text-black text-2xl sm:text-[28px] lg:text-[32px] mx-auto mt-3.5 !leading-tight">
            Last step,
            <br />
            <b className="font-bold">Pick a plan</b>
          </h2>
          {/* {!store?.flags?.uses_chowbot && (
            <div className="text-sm text-dark mt-4.5 flex items-start bg-grey-fields-100 rounded-10 px-3 py-3 text-left -mb-2.5">
              <figure className="h-8 w-8 rounded-full bg-white shadow-pill flex items-center justify-center flex-shrink-0 mr-3 text-base">
                🎁
              </figure>
              <div>
                <div>
                  All plans are free for <b className="font-medium">7 days</b>, NO CARD REQUIRED!
                </div>
                <button
                  className="font-semibold text-1xs md:text-sm flex items-center uppercase text-primary-500 mt-1.5"
                  onClick={() => toggleModal("subscription_benefits")}
                >
                  What your subscription gives you
                  prettier-ignore
                  <svg className="w-3.75 ml-px mt-px" viewBox="0 0 15 16" fill="none">
                    <path d="M3.96484 11.5355L11.0359 4.46446" stroke="#332089" strokeWidth="1.7" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M11.0352 11.5355L11.0352 4.46446L3.96409 4.46445" stroke="#332089" strokeWidth="1.7" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </button>
                {credits && credits?.balance > 0 && (
                  <div className="mt-2.5 pt-2 border-t border-grey-border border-opacity-50">
                    You also have{" "}
                    <b className="font-medium">
                      {credits?.currency} {toNaira(credits?.balance)}
                    </b>{" "}
                    discount to redeem
                  </div>
                )}
              </div>
            </div>
          )} */}
        </div>

        {/* making a second check for context because it could be anything */}
        <SubscriptionsController redirect="/get-started" isSetup />
      </div>

      <Portal>
        <SubscriptionBenefits
          show={modals.subscription_benefits.show}
          toggle={() => toggleModal("subscription_benefits")}
        />
      </Portal>
    </DashboardLayout>
  );
};

export default PlanPlan;
