import { useFetcher, useRequest } from "@/api/utils";
import { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as yup from "yup";
import { GetCountries } from "@/api";
import ErrorLabel from "@/components/ui/error-label";
import { AppBtn } from "@/components/ui/buttons";
import { InputField, SelectDropdown, TextArea } from "@/components/ui/form-elements";
import { getFieldvalues, getLocalStorageWithExpiry } from "@/assets/js/utils/functions";
import SuccessAnimation from "@/components/ui/success-animation";
import { PushNotificationParams } from "@/api/interfaces/internals";
import { SendPushNotification } from "@/api/internals";
import { CountryInterface } from "@/assets/interfaces";
import { Login } from ".";

const PushNotification = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check if user is authenticated using getLocalStorageWithExpiry
    const token = getLocalStorageWithExpiry("admin_token");
    if (token) {
      setIsAuthenticated(true);
    }
  }, []);

  if (!isAuthenticated) {
    return <Login setIsAuthenticated={setIsAuthenticated} />;
  }
  return <SendPushNotificationForm />;
};

const SendPushNotificationForm: React.FC = () => {
  const { response } = useFetcher(GetCountries, {});
  const countries: CountryInterface[] = response?.data ?? [];
  const [error, setError] = useState<string>();
  const [sendingOption, setSendingOption] = useState<string>();
  const [country, setCountry] = useState<string>();
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [emailsFromCsv, setEmailsFromCsv] = useState<string[]>([]);
  const [csvError, setCsvError] = useState<string>();
  const { makeRequest, isLoading } = useRequest<PushNotificationParams>(SendPushNotification);
  const [success, setSuccess] = useState(false);
  const [sentNotifications, setSentNotifications] = useState<number>(0);
  const [successfulNotifications, setSuccessfulNotifications] = useState<number>(0);

  const form = useFormik({
    initialValues: {
      title: "",
      message: "",
      path: "",
      multiple_emails: "",
    },
    onSubmit: (values) => {
      let submitData: any = { ...values };

      // Handle multiple emails from CSV or text input
      if (sendingOption === "csv" && emailsFromCsv.length > 0) {
        submitData = { ...submitData, emails: emailsFromCsv };
      } else if (sendingOption === "multiple" && values.multiple_emails) {
        const emails = values.multiple_emails
          .split(/[,\n;]/)
          .map((email) => email.trim())
          .filter((email) => email.length > 0);
        submitData = { ...submitData, emails };
      }

      makeRequest(submitData).then(([res, err]) => {
        if (res) {
          setSuccess(true);
          setSentNotifications(res.data?.total_sent);
          setSuccessfulNotifications(res.data?.total_successful);
        } else setError(err.message);
      });
    },
    validationSchema,
  });

  const handleCsvUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setCsvFile(file);
    setCsvError("");

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string;
        const emails = parseCsvEmails(text);
        setEmailsFromCsv(emails);

        if (emails.length === 0) {
          setCsvError("No valid emails found in CSV file");
        }
      } catch (error) {
        setCsvError("Error reading CSV file");
      }
    };
    reader.readAsText(file);
  };

  const parseCsvEmails = (csvText: string): string[] => {
    const lines = csvText.split("\n");
    const emails: string[] = [];

    // Skip header row if it exists
    const startIndex = lines[0] && lines[0].toLowerCase().includes("email") ? 1 : 0;

    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Split by comma and find email-like values
      const columns = line.split(",").map((col) => col.replace(/"/g, "").trim());

      for (const column of columns) {
        if (isValidEmail(column)) {
          emails.push(column);
          break; // Take first valid email per row
        }
      }
    }

    return Array.from(new Set(emails)); // Remove duplicates
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const getEmailCount = (): number => {
    if (sendingOption === "csv") return emailsFromCsv.length;
    if (sendingOption === "multiple" && form.values.multiple_emails) {
      return form.values.multiple_emails
        .split(/[,\n;]/)
        .map((email) => email.trim())
        .filter((email) => email.length > 0).length;
    }
    return 0;
  };

  return (
    <div className="w-full">
      <div className="m-auto max-w-[500px] px-10 border border-grey-border rounded-2xl mt-20 ">
        <div className="py-10"></div>
        <div>
          <ErrorLabel error={error} />
          <h1 className="text-2xl text-center mb-5 capitalize">Send Push Notifications</h1>

          {!success && (
            <form className="py-5 pb-20 space-y-2.5" onSubmit={form.handleSubmit}>
              <SelectDropdown
                className="bg-white"
                label="Send to"
                options={[
                  { text: "All Users", value: "all" },
                  { text: "Single User", value: "single" },
                  { text: "Country", value: "country" },
                  { text: "Multiple Emails (Text)", value: "multiple" },
                  { text: "CSV Import", value: "csv" },
                ]}
                value={sendingOption}
                onChange={(e) => setSendingOption(e.target.value)}
              />

              {sendingOption === "single" && (
                <InputField label="User Email" type="email" {...getFieldvalues("user_email", form)} />
              )}

              {sendingOption === "country" && (
                <SelectDropdown
                  className="bg-white"
                  label="Select country"
                  options={countries.map((c) => ({ text: c.name, value: c.code }))}
                  value={country}
                  onChange={(e) => {
                    setCountry(e.target.value);
                    form.setFieldValue("country", e.target.value);
                  }}
                />
              )}

              {sendingOption === "multiple" && (
                <div>
                  <TextArea
                    label="Multiple Emails"
                    placeholder="Enter emails separated by commas, semicolons, or new lines&#10;<EMAIL>, <EMAIL>&#10;<EMAIL>"
                    rows={6}
                    {...getFieldvalues("multiple_emails", form)}
                  />
                  {form.values.multiple_emails && (
                    <p className="text-sm text-gray-600 mt-1">{getEmailCount()} email(s) detected</p>
                  )}
                </div>
              )}

              {sendingOption === "csv" && (
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <label className="block text-sm font-medium text-gray-700">Upload CSV File</label>
                    <a
                      href="/example-emails.csv"
                      download
                      className="text-xs text-blue-600 hover:text-blue-800 underline"
                    >
                      Download Example CSV
                    </a>
                  </div>
                  <input
                    type="file"
                    accept=".csv"
                    onChange={handleCsvUpload}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    CSV should contain emails in any column. Headers are optional.
                  </p>
                  {csvError && <p className="text-red-500 text-sm mt-1">{csvError}</p>}
                  {emailsFromCsv.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-green-600">✓ {emailsFromCsv.length} valid email(s) found in CSV</p>
                      <details className="mt-2">
                        <summary className="text-sm text-gray-600 cursor-pointer">
                          Preview emails ({Math.min(5, emailsFromCsv.length)} shown)
                        </summary>
                        <div className="mt-1 p-2 bg-gray-50 rounded text-xs">
                          {emailsFromCsv.slice(0, 5).map((email, index) => (
                            <div key={index}>{email}</div>
                          ))}
                          {emailsFromCsv.length > 5 && (
                            <div className="text-gray-500">... and {emailsFromCsv.length - 5} more</div>
                          )}
                        </div>
                      </details>
                    </div>
                  )}
                </div>
              )}

              <InputField label="Title" {...getFieldvalues("title", form)} />
              <InputField label="Message" {...getFieldvalues("message", form)} />
              <InputField label="Path" {...getFieldvalues("path", form)} />

              <AppBtn disabled={isLoading} isBlock type="submit">
                {isLoading ? "Sending Notifications..." : "Send Notifications"}
              </AppBtn>
            </form>
          )}
          {success && (
            <div className="flex flex-col items-center gap-5">
              <SuccessAnimation />
              <h1 className="text-1xl text-black-muted text-center mb-5">
                {successfulNotifications} of {sentNotifications} Notification Sent Successfully
              </h1>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
export default PushNotification;

const validationSchema = yup.object().shape({
  title: yup.string().required("Title is required"),
  message: yup.string().required("Message is required"),
  path: yup.string().required("Path is required"),
  user_email: yup.string().when("sendingOption", {
    is: "single",
    then: yup.string().email("Please provide a valid email").required("User Email is required"),
  }),
  country: yup.string().when("sendingOption", {
    is: "country",
    then: yup.string().required("Country is required"),
  }),
  multiple_emails: yup.string().when("sendingOption", {
    is: "multiple",
    then: yup.string().required("Please enter at least one email address"),
  }),
});
