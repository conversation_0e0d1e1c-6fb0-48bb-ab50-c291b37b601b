import { CURRENCIES } from "@/assets/interfaces";
import React from "react";

const paymentsIconsConfig = {
  [CURRENCIES.NGN]: [
    {
      min_value: 100_000_000,
      icon: "shield",
    },
    {
      min_value: 10_000_000,
      icon: "hexagon",
    },
    {
      min_value: 1_000_000,
      icon: "star",
    },
    {
      min_value: 100_000,
      icon: "circle",
    },
  ],
  [CURRENCIES.GHC]: [
    {
      min_value: 1_000_000,
      icon: "shield",
    },
    {
      min_value: 100_000,
      icon: "hexagon",
    },
    {
      min_value: 10_000,
      icon: "star",
    },
    {
      min_value: 1_000,
      icon: "circle",
    },
  ],
  [CURRENCIES.ZAR]: [
    {
      min_value: 1_000_000,
      icon: "shield",
    },
    {
      min_value: 100_000,
      icon: "hexagon",
    },
    {
      min_value: 10_000,
      icon: "star",
    },
    {
      min_value: 1_000,
      icon: "circle",
    },
  ],
  [CURRENCIES.KES]: [
    {
      min_value: 10_000_000,
      icon: "shield",
    },
    {
      min_value: 1_000_000,
      icon: "hexagon",
    },
    {
      min_value: 100_000,
      icon: "star",
    },
    {
      min_value: 10_000,
      icon: "circle",
    },
  ],
};

export const milestonesConfig = [
  {
    slug: "store-visits",
    title: "Store Visits",
    mainDescription: "Milestones for total number of visits to your store",
    description: (data: string | 0) => `Achieved when your store gets ${data} visits`,
    icons_config: [
      {
        min_value: 100_000,
        icon: "shield",
      },
      {
        min_value: 10_000,
        icon: "hexagon",
      },
      {
        min_value: 1_000,
        icon: "star",
      },
      {
        min_value: 100,
        icon: "circle",
      },
    ],
    linkLabel: "View Milestones",
    key: "store_visits",
    responseKey: "total_store_visits",
  },
  {
    slug: "payments",
    title: "Payments Processed",
    mainDescription: "Milestones for total value of payments processed",
    description: (data: string | 0) => `Achieved when you process ${data} worth of Payments.`,
    linkLabel: "View Milestones",
    key: "order_volumes",
    responseKey: "total_payments_volume",
    icons_config: paymentsIconsConfig,
  },
  {
    slug: "orders",
    title: "Orders",
    mainDescription: "Milestones for total number of orders processed",
    description: (data: string | 0) => `Achieved when you process ${data} orders`,
    linkLabel: "View Milestones",
    key: "orders_count",
    responseKey: "total_orders",
    icons_config: [
      {
        min_value: 10_000,
        icon: "shield",
      },
      {
        min_value: 1_000,
        icon: "hexagon",
      },
      {
        min_value: 100,
        icon: "star",
      },
      {
        min_value: 10,
        icon: "circle",
      },
    ],
  },
  {
    slug: "order-volumes",
    title: "Orders Volume",
    mainDescription: "Milestones for total value of orders processed",
    description: (data: string | 0) => `Achieved when you process ${data} worth of Payments.`,
    linkLabel: "View Milestones",
    key: "order_volumes",
    responseKey: "total_orders_volume",
    icons_config: paymentsIconsConfig,
  },
];

export const getIconUrl = (milestone: string, icon: string) =>
  `https://res.cloudinary.com/catlog/image/upload/w_200/v1744322640/Badges/${milestone}/${icon}.png`;
