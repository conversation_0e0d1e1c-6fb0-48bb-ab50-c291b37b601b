import { COUNTRIES, CountryInterface, KYC_STATUSES } from ".";
import { DELIVERY_PROVIDERS, DeliveryFormAddress, IDeliveryAddress } from "./deliveries";
import { Category, ProductItemInterface } from "./products";
import { Image as FileImage } from "./index";
import { Plan, SubscriptionInterface } from "./subscriptions";

export enum StorefrontVersion {
  V1 = "V1",
  V2 = "V2",
}

export enum StoreRoles {
  OWNER = "OWNER",
  OPERATOR = "OPERATOR",
  ADMIN = "ADMIN",
  STAFF = "STAFF",
}

export enum CURRENCIES {
  GHC = "GHS",
  NGN = "NGN",
  USD = "USD",
  EUR = "EUR",
  GBP = "GBP",
  ZAR = "ZAR",
  KES = "KES",
  CAD = "CAD",
}

export enum INPUT_TYPE {
  TEXT = "text",
  NUMBER = "number",
  RADIO = "radio",
  DROPDOWN = "dropdown",
  TEXTAREA = "textarea",
}

export interface StoreConfiguration {
  view_modes: {
    grid: boolean;
    card: boolean;
    horizontal: boolean;
    default: string;
  };
  hours?: { [key: string]: string } | null;
  fb_pixel?: string;
  ga_id?: string;
  custom_message?: string;
  enquiry_message?: string;
  direct_checkout_enabled?: boolean;
  sort_by_latest_products: boolean;
  show_unavailable_products: boolean;
  customer_pickup_enabled?: boolean;
  whatsapp_checkout_enabled?: boolean;
  require_delivery_info?: boolean;
  color?: string;
  payment_validates_order?: boolean;
  confirm_order_before_payment?: boolean;
  average_delivery_timeline?: string;
  pickup_address?: any;
  send_menu_on_initiation?: boolean;
  menu_images?: string[];
  require_geolocation?: boolean;
  custom_order_success_message?: string;
  require_emails?: boolean;
  payment_timeout?: number;
  auto_customer_check_in?: {
    enabled: boolean;
    days: number;
    message: string;
  };
  custom_checkout_form?: {
    name: string;
    type: INPUT_TYPE;
    is_required: boolean;
    is_enabled?: boolean;
    label?: string;
    options?: { value: string; price?: number }[];
  }[];
  collect_order_notes: boolean;
  pass_chowbot_fee_to_deliveries: boolean;
  top_category_cards?: boolean;
  small_top_category_cards?: boolean;
  bottom_category_cards?: boolean;
  store_welcome_message?: string;
}

export interface StorePaymentOptions {
  [key: string]: {
    enabled: boolean;
    type: string;
  }[];
}

export interface StoreCurrencySettings {
  default: CURRENCIES;
  products: CURRENCIES;
  storefront: CURRENCIES[];
  storefront_default: CURRENCIES;
  rates: { [key: string]: number };
}

export interface BranchItem {
  label: string;
  is_head_branch?: boolean;
  store_id: string;
  store?: {
    name: string;
    logo: string;
    slug: string;
  };
}

export interface Branches {
  id?: string;
  name: string;
  branches: BranchItem[];
}

export interface ChowdeckConfig {
  sync_to_chowdeck: boolean;
  sync_from_chowdeck: boolean;
  auto_delivery: boolean;
  pickup_address?: string;
}

export interface StoreAdditionalDetails {
  logo: string;
  description: string;
  business_type: string;
  // monthly_orders: string;
  business_category: string;
  social_media_platform: string;
  social_media_username: string;
}

export interface StoreInterface {
  delivery_locations?: string;
  total_visits: number;
  socials?: {
    twitter?: string;
    instagram?: string;
    facebook?: string;
    snapchat?: string;
    whatsapp?: string;
    tiktok?: string;
  };
  hero_image?: string;
  logo?: string;
  store_menu?: string;
  country: CountryInterface;
  state?: string;
  address?: string;
  slug: string;
  name: string;
  phone: string;
  secondary_phone?: string;
  description: string;
  owner?: string;
  categories?: Category[];
  createdAt: string;
  updatedAt: string;
  created_at?: string;
  updated_at?: string;
  item_count?: number;
  items_count?: number;
  plan?: Plan;
  configuration: StoreConfiguration;
  id: string;
  delivery_areas: DeliveryArea[] | string[];
  checkout_channels?: CheckoutChannels;
  owners?: {
    user: string;
    role: StoreRoles;
    email: string;
    name: string;
  }[];
  subscription?: SubscriptionInterface | any;
  onboarding_steps?: {
    products_added?: boolean;
    link_added?: boolean;
    test_payment_made?: boolean;
    security_pin_added?: boolean;
    has_taken_first_order_with_payment?: boolean;
  };
  disabled?: boolean;
  payments_enabled?: boolean;
  kyc_approved?: boolean;
  payment_options: StorePaymentOptions;
  primary_slug?: string;
  slugs?: string[];
  currencies?: StoreCurrencySettings;
  maintenance_mode?: boolean;
  deliveries_enabled?: boolean;
  business_category?: {
    name: string;
    type: string;
    monthly_orders: string;
    product_types: string[];
  };
  extra_info?: {
    delivery_timeline?: string;
    production_timeline?: string;
    refund_policy?: string;
    images_label?: string;
    images?: string[];
  };
  wallet?: string;
  pickup_address?: IDeliveryAddress;
  branches?: string;
  flags?: {
    uses_chowbot?: boolean;
    storefront_version?: StorefrontVersion;
  };
  public_access_tokens: {
    chowdeck?: {
      reference?: string;
    };
  };
  third_party_configs?: {
    chowdeck?: ChowdeckConfig;
  };
  delivery_providers: DELIVERY_PROVIDERS[];
  meta?: {
    instagram_item_upload_count?: number;
  };
  wallets?: { currency: CURRENCIES; id: string }[];
  info_blocks: InfoBlockInterface[];
  about_us?: {
    content: string;
    images: string[];
  };
  testimonials: StoreTestimonial[];
  faqs: StoreFaq[];
  domains?: Array<{
    certificate_issued: boolean;
    verified: boolean;
    id: string;
    domain: string;
    store_id: string;
    verification_code: string;
    created_at: string;
    updated_at: string;
    v?: number;
    verified_at?: string | null;
  }>;
}

export interface DeliveryArea {
  id: string;
  name: string;
  fee: number;
}

export interface CheckoutChannels {
  whatsapp: WhatsappCheckoutOption[];
  instagram?: InstagramCheckoutOption;
}

export interface WhatsappCheckoutOption {
  id: string;
  label: string;
  phone?: string;
  type: "WHATSAPP";
  primary?: boolean;
}

export interface InstagramCheckoutOption {
  id: string;
  username: string;
  type: "INSTAGRAM";
  enabled?: boolean;
}

export interface TeamMember {
  id?: string;
  email?: string;
  name?: string;
  role: string;
  user?: string;
  status?: "PENDING" | "ACCEPTED";
}

export enum ID_TYPES {
  "INTERNATIONAL_PASSPORT" = "INTERNATIONAL_PASSPORT",
  "NIN" = "NIN",
  "DRIVERS_LICENSE" = "DRIVERS_LICENSE",
  "VOTERS_CARD" = "VOTERS_CARD",
  "GHANA_CARD" = "GHANA_CARD",
}

export interface KycStore {
  name: string;
  id: string;
  phone: string;
  slug: string;
}

export interface KYCInfo {
  first_name: string;
  last_name: string;
  phone: string;
  address: {
    address_line1?: string;
    lga?: string;
    state?: string;
    city?: string;
  };
  entity: string;
  bvn: string;
  dob?: string;
  bvn_verified_at?: Date;
  identity: {
    type?: ID_TYPES;
    number?: string;
    url?: string;
    filename?: string;
    verified_at?: Date;
    vnin?: string;
    selfie?: string;
  };
  status: KYC_STATUSES;
  store: string | KycStore;
  created_at: string;
  createdAt: string;
  updated_at: string;
  updatedAt: string;
  id: string;
  bvn_token?: string;
  country?: COUNTRIES;
  verification_method: "INSTANT" | "MANUAL";
  rejection_message?: string;
  proposed_bvn?: {
    bvn: string;
    name: string;
    phone: string;
  };
  phone_verified?: boolean;
}

export enum Tier {
  TIER_1 = "TIER_1",
  TIER_2 = "TIER_2",
  TIER_3 = "TIER_3",
}

export interface ReferredUsers {
  has_claimed: boolean;
  user: { name: string; date_joined: string };
}

export interface Referrals {
  id: string;
  owner: string;
  referral_code: string;
  referrals: ReferredUsers[];
}

export interface ChowdeckItem {
  id: number;
  group_id: number;
  rank: number;
  name: string;
  popular_name: string;
  description: string;
  in_stock: boolean;
  is_published: boolean;
  is_active: boolean;
  price: number;
  currency: string;
  price_description: string;
  created_at: string;
  updated_at: string;
  container_type_id: any;
  menu_group_ids: any;
  reference: string;
  volume_per_portion: number;
  maximum_quantity: any;
  maximum_quantity_as_side: number;
  size_description: any;
  menu_sub_category_id: any;
  listing_price_factor: string;
  container_name: any;
  container_price: any;
  container_description: any;
  container_volume: any;
  tags: ChowdeckTag[];
  category: ChowdeckCategory;
  images: Image[];
}

export interface ChowdeckTag {
  name: string;
  id: number;
}

export interface ChowdeckCategory {
  id: number;
  name: string;
  is_published: boolean;
  is_general: boolean;
  rank?: number;
  reference?: string;
  food_count?: number;
}

export interface Image {
  path: string;
  rank: number;
}

export interface InfoBlockInterface {
  id?: string;
  title: string;
  content_type: "TEXT" | "IMAGES";
  text_content: string;
  image_content: string[];
  store?: string;
  tag?: string;
  items?: string[];
  is_visible: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface HighlightVideo {
  url: string;
  active: boolean;
  hidden?: boolean;
  thumbnail: string;
  products: string[] | ProductItemInterface[];
}

export interface HighlightInterface {
  id: string;
  title: string;
  videos: HighlightVideo[];
  store: string | StoreInterface;
  slug: string;
  created_at: string;
  updated_at: string;
  active: boolean;
}

export interface YearWrappedResponse {
  store: {
    name: string;
    id: string;
    country: string;
    currency: string;
  };
  catlog_credits_earned: number;
  created_at: string;
  grading: number;
  metaphor: string;
  month_with_highest_orders: number;
  no_of_customers: number;
  no_of_fulfilled_deliveries: number;
  no_of_orders_for_location: number;
  no_of_referrals: number;
  no_of_store_visits: number;
  other_top_customers: Array<{
    id: string;
    name: string;
    orders_count: number;
  }>;
  other_top_products: Array<{
    id: string;
    name: string;
    thumbnail: string;
  }>;
  percentile: number;
  store_currency: string;
  top_customer: {
    id: string;
    name: string;
    phone: string;
    orders_count: number;
  };
  top_orders_location: string;
  top_product: {
    id: string;
    name: string;
    thumbnail: number;
    orders_count: number;
  };
  top_product_orders: number;
  total_order_count: number;
  total_order_volume: number;
  total_payments_count: number;
  total_payments_volume: number;
  updated_at: string;
  no_of_repeat_customers: number;
  id: string;
  is_shared?: boolean;
}
export interface StoreTestimonial {
  id: string;
  customer_name: string;
  content: string;
  source: string;
  created_at: string;
  is_visible: boolean;
}

export interface StoreFaq {
  id: string;
  question: string;
  answer: string;
  created_at: string;
  is_visible: boolean;
}

export interface StoreAboutDetails {
  about_us: {
    content: string;
    images: string[];
  };
  testimonials: StoreTestimonial[];
}
export interface UpdateStoreFaqDetails {
  faqs: StoreFaq[];
}
