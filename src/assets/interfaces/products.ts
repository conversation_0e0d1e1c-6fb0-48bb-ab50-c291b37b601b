import { InfoBlockInterface } from "./stores";

export interface VariantItem {
  id?: string;
  image?: string;
  price: number;
  discount_price?: number;
  is_available: boolean;
  values?: { [key: string]: string };
  quantity?: number;
  available_to_purchase?: boolean;
  original_price?: number;
}

export interface VariantForm {
  is_template?: boolean;
  type: string;
  options: VariantItem[];
}

export interface CustomItemInterface {
  id: string;
  name: string;
  price: string;
}

export interface ProductVideo {
  name: string;
  url: string;
  thumbnail: string;
}

export interface ProductItemInterface {
  name: string;
  thumbnail: number;
  thumbnail_type: string;
  views?: number;
  price: number;
  tags: {
    name: string;
    emoji: string;
    id: string;
  }[];
  category: {
    name: string;
    emoji: string;
    id: string;
  };
  available: boolean;
  createdAt?: string;
  created_at?: string;
  updated_at?: string;
  description: string;
  discount_price?: number;
  id: string;
  images: string[];
  videos: { name: string; url: string; thumbnail: string }[];
  price_unit?: string;
  slug?: string;
  store: string;
  updatedAt?: string;
  variants?: VariantForm;
  quantity?: number;
  is_always_available?: boolean;
  is_deleted?: boolean;
  qunatity?: number;
  minimum_order_quantity?: number;
  cost_price?: number;
  is_featured?: boolean;
  sort_index?: number;
  total_orders?: number;
  expiry_date?: string;
  info_blocks?: InfoBlockInterface[];
  tiered_pricing?: PricingTierInterface;
  original_price?: number;
}

export interface DiscountItemInterface {
  id: string;
  label: string;
  active: boolean;
  percentage: number;
  discount_cap: number;
  start_date: string;
  end_date: string;
  items: string[];
}

export interface CouponItemInterface {
  type: "percentage" | "fixed";
  id: string;
  coupon_code: string;
  active: boolean;
  end_date: string;
  percentage: number;
  discount_cap: number;
  discount_amount: number;
  quantity: number;
  orders: string[];
  minimum_order_amount?: number;
}

export type Category = {
  id?: string;
  emoji: string;
  name: string;
  items_count?: number;
};

export interface StrippedItem {
  id: string;
  name: string;
  image: string;
  price: number;
  variants?: VariantForm;
  is_always_available?: boolean;
  weight?: number;
}

export interface VariantStrippedItem {
  item: StrippedItem;
  variant_id?: string;
  weight?: number;
}

export interface BulkUpdateFormItem {
  name: string;
  id: string;
  quantity?: number;
  price?: number;
  old_price?: number;
  image: string;
  is_always_available: boolean;
  variants: VariantForm;
}
export interface BulkDiscountTierInterface {
  minimum_quantity: number;
  discount_percentage: number;
}

export interface PricingTierItemInterface {
  discount_percentage: number;
  minimum_quantity: number;
}
export interface PricingTierInterface {
  id?: string;
  label: string;
  items?: string[];
  tiers: PricingTierItemInterface[];
  active: boolean;
  created_at?: string;
  updated_at?: string;
}
