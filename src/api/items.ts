import { paramsFromObject } from "../assets/js/utils/functions";
import {
  CreateCouponParams,
  CreateCustomItemParams,
  CreateDiscountParams,
  CreateItemsParams,
  DeleteCouponParams,
  DeleteDiscountParams,
  DeleteItemParams,
  EditItemParams,
  EditItemQuantityParams,
  GetAllItemsParams,
  GetCustomItemsParams,
  GetDiscountItemParams,
  GetItemsParams,
  GetStoreItemsParams,
  ImportItemsParams,
  BulkUpdateItemsParams,
  PaginateSearchParams,
  UpdateCouponParams,
  UpdateSortItemParams,
  UpdateCustomItemParams,
  UpdateDiscountParams,
  ExportItemsAsCsvParams,
  CreateHighlightParams,
  UpdateHighlightParams,
  DeleteHighlightParams,
  GetHighlightParams,
  CreateBulkDiscountParams,
  UpdateBulkDiscountParams,
  // Tiered Pricing imports
  CreateTieredPricingParams,
  UpdateTieredPricingParams,
  AssignItemsToTieredPricingParams,
  RemoveItemsParams,
  CalculatePriceParams,
  GetTieredPricingParams,
  DeleteTieredPricingParams,
} from "./interfaces/items.interface";
import { request } from "./utils";

const CreateItems = (data: CreateItemsParams) => {
  return request("items", "post", { data });
};

const ImportItems = (data: ImportItemsParams) => {
  return request("items/import-items", "post", { data });
};

const GetAllItems = (data: GetAllItemsParams) => {
  const params = paramsFromObject(data);
  return request(`items/all?${params}`, "get");
};

const GetItems = (data: GetItemsParams) => {
  const params = paramsFromObject(data);
  return request(`items?${params}`, "get");
};

const GetSortItems = (data: GetItemsParams) => {
  const params = paramsFromObject(data);
  return request(`items/paginate-by-sort-index?${params}`, "get");
};

const UpdateSortItems = (data: UpdateSortItemParams) => {
  return request("items/sort-indexes", "put", { data: data.items });
};

const GetItem = ({ slug }) => {
  return request(`items/${slug}`, "get");
};

const EditItems = (data: EditItemParams) => {
  return request(`items/${data.id}`, "put", { data: data.item });
};

const BulkUpdateItems = (data: BulkUpdateItemsParams) => {
  return request(`items/bulk-update`, "put", { data });
};

const UpdateFeaturedItem = (data: EditItemParams) => {
  return request(`items/featured/${data.id}`, "put", { data: data.item });
};

const EditItemQuantities = (data: EditItemQuantityParams) => {
  return request("items/quantities", "put", { data });
};

const DeleteItem = (data: DeleteItemParams) => {
  return request(`items/${data.id}`, "delete");
};

const GetStoreItems = (data: GetStoreItemsParams) => {
  const params = paramsFromObject(data);
  return request(`items/public?${params}`, "get");
};

const CreateDiscount = (data: CreateDiscountParams) => {
  return request("discounts", "post", { data });
};

const GetDiscounts = (data: PaginateSearchParams) => {
  const params = paramsFromObject(data);
  return request(`discounts?${params}`, "get");
};

const UpdateDiscount = (data: UpdateDiscountParams) => {
  return request("discounts", "put", { data });
};

const CreateBulkDiscount = (data: CreateBulkDiscountParams) => {
  return request("discounts", "post", { data });
};

const UpdateBulkDiscount = (data: UpdateBulkDiscountParams) => {
  return request("discounts", "put", { data });
};

const DeleteDiscount = (data: DeleteDiscountParams) => {
  return request(`discounts/${data.id}`, "delete");
};

const GetDiscountItems = (data: GetDiscountItemParams) => {
  return request(`discounts/${data.id}/items`, "get");
};

const GetCoupons = (data: PaginateSearchParams) => {
  const params = paramsFromObject(data);
  return request(`coupons?${params}`, "get");
};

const GetCouponByCode = (data: { code: string }) => {
  return request(`coupons/${data.code}`, "get");
};

const CreateCoupon = (data: CreateCouponParams) => {
  return request("coupons", "post", { data });
};

const UpdateCoupon = (data: UpdateCouponParams) => {
  return request("coupons", "put", { data });
};

const DeleteCoupon = (data: DeleteCouponParams) => {
  return request(`coupons/${data.id}`, "delete");
};

//  CUSTOM ITEMS

const GetCustomItems = (data: GetCustomItemsParams) => {
  const params = paramsFromObject(data);
  return request(`custom-items?${params}`, "get");
};
const CreateCustomItem = (data: CreateCustomItemParams) => {
  return request("custom-items", "post", { data });
};

const UpdateCustomItem = (data: UpdateCustomItemParams) => {
  const id = data.id;
  delete data.id;
  return request(`custom-items/${id}`, "put", { data });
};

const DeleteCustomItem = ({ id }) => {
  return request(`custom-items/${id}`, "delete");
};

const ConvertImagesToStrings = (data: { images: string[] }) => {
  return request("utils/images-to-strings", "post", { data });
};

const ExtractMenuItemsFromImage = (data: { menu_image: string }) => {
  return request("utils/extract-menu-items", "post", { data });
};

const GetVariantTemplates = () => {
  return request("items/variant-templates", "get");
};

const ExportItemsAsCsv = async (data: ExportItemsAsCsvParams) => {
  const params = paramsFromObject({ items: data.items.length > 0 ? data.items.join(",") : "all" });
  const BASE_URL = process.env.NEXT_PUBLIC_API_URL;
  const localToken = localStorage === undefined ? null : localStorage.token;
  return window.fetch(`${BASE_URL}/item-files/export?${params}`, {
    method: "get",
    headers: {
      Authorization: localToken ? `Bearer ${localToken}` : undefined,
      "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    },
  });
};

const GetHighlights = (data: GetHighlightParams) => {
  const params = paramsFromObject(data);
  return request(`item-highlights?${params}`, "get");
};

const CreateHighlight = (data: CreateHighlightParams) => {
  return request("item-highlights", "post", { data });
};

const UpdateHighlight = (data: UpdateHighlightParams) => {
  const { id, ...rest } = data;
  return request(`item-highlights/${id}`, "put", { data: rest });
};

const DeleteHighlight = (data: DeleteHighlightParams) => {
  return request(`item-highlights/${data.id}`, "delete");
};

// Tiered Pricing APIs
const CreateTieredPricing = (data: CreateTieredPricingParams) => {
  return request("tiered-pricing", "post", { data });
};

const GetTieredPricing = (data: GetTieredPricingParams) => {
  const params = paramsFromObject(data);
  return request(`tiered-pricing?${params}`, "get");
};

const GetActiveTieredPricing = () => {
  return request("tiered-pricing/active", "get");
};

const GetTieredPricingById = (id: string) => {
  return request(`tiered-pricing/${id}`, "get");
};

const UpdateTieredPricing = (data: UpdateTieredPricingParams) => {
  const { id, ...rest } = data;
  return request(`tiered-pricing/${id}`, "put", { data: rest });
};

const DeleteTieredPricing = (data: DeleteTieredPricingParams) => {
  return request(`tiered-pricing/${data.id}`, "delete");
};

const AssignItems = (data: AssignItemsToTieredPricingParams) => {
  const { id, ...rest } = data;
  return request(`tiered-pricing/${id}/assign-items`, "post", { data: rest });
};

const RemoveItems = (data: RemoveItemsParams) => {
  const { id, ...rest } = data;
  return request(`tiered-pricing/${id}/remove-items`, "post", { data: rest });
};

const GetAssignedItems = (id: string) => {
  return request(`tiered-pricing/${id}/items`, "get");
};

const CalculatePrice = (data: CalculatePriceParams) => {
  return request("tiered-pricing/calculate-price", "post", { data });
};

export {
  ConvertImagesToStrings,
  CreateCoupon,
  CreateCustomItem,
  CreateDiscount,
  CreateItems,
  DeleteCoupon,
  DeleteCustomItem,
  DeleteDiscount,
  DeleteItem,
  EditItemQuantities,
  EditItems,
  GetAllItems,
  GetCouponByCode,
  GetCoupons,
  GetCustomItems,
  GetDiscountItems,
  GetDiscounts,
  GetItem,
  GetItems,
  GetSortItems,
  GetStoreItems,
  UpdateSortItems,
  ImportItems,
  UpdateCoupon,
  UpdateCustomItem,
  UpdateDiscount,
  UpdateFeaturedItem,
  ExtractMenuItemsFromImage,
  GetVariantTemplates,
  BulkUpdateItems,
  ExportItemsAsCsv,
  GetHighlights,
  CreateHighlight,
  UpdateHighlight,
  DeleteHighlight,
  CreateBulkDiscount,
  UpdateBulkDiscount,
  // Tiered Pricing
  CreateTieredPricing,
  GetTieredPricing,
  GetActiveTieredPricing,
  GetTieredPricingById,
  UpdateTieredPricing,
  DeleteTieredPricing,
  AssignItems,
  RemoveItems,
  GetAssignedItems,
  CalculatePrice,
};
