import { AffiliateInterface, AffiliateStatisticsInterface } from "../../assets/interfaces/affiliates.interface";

export interface GetAffiliatesParams {
  page?: number;
  per_page?: number;
  sort?: string;
  filter?: {
    search?: string;
  };
}

export interface GetAffiliateParams {
  id: string;
}

export interface GetAffiliateOrdersParams {
  id: string;
  page?: number;
  per_page?: number;
  sort?: string;
  filter?: {
    from?: string;
    to?: string;
    status?: string;
  };
}

export interface GetAffiliateCustomersParams {
  id: string;
  page?: number;
  per_page?: number;
  sort?: string;
  filter?: {
    search?: string;
  };
}

export interface CreateAffiliateParams {
  name: string;
  email?: string;
  phone?: string;
  type: string;
}

export interface UpdateAffiliateParams {
  id: string;
  name?: string;
  email?: string;
  phone?: string;
  type?: string;
}

export interface DeleteAffiliateParams {
  id: string;
}
