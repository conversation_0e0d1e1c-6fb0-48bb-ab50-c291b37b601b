enum CURRENCIES {
  NGN = "NGN",
  GHC = "GHC",
  ZAR = "ZAR",
  KES = "KES",
  USD = "USD",
  GBP = "GBP",
  EUR = "EUR",
}

interface OrderVolumes {
  [CURRENCIES.NGN]: number[];
  [CURRENCIES.GHC]: number[];
  [CURRENCIES.ZAR]: number[];
  [CURRENCIES.KES]: number[];
  [CURRENCIES.USD]: number[];
  [CURRENCIES.GBP]: number[];
  [CURRENCIES.EUR]: number[];
}

interface Milestones {
  orders_count: number[];
  store_visits: number[];
  order_volumes: OrderVolumes;
}

export const milestonesData: Milestones = {
  orders_count: [10, 50, 100, 500, 1_000, 5_000, 10_000, 50_000],
  store_visits: [100, 500, 1_000, 5_000, 10_000, 50_000, 100_000],
  order_volumes: {
    [CURRENCIES.NGN]: [100_000, 500_000, 1_000_000, 5_000_000, 10_000_000, 50_000_000, 100_000_000],
    [CURRENCIES.GHC]: [5_000, 10_000, 50_000, 100_000, 500_000, 1_000_000, 5_000_000, 10_000_000],
    [CURRENCIES.ZAR]: [5_000, 10_000, 50_000, 100_000, 500_000, 1_000_000, 5_000_000],
    [CURRENCIES.KES]: [10_000, 50_000, 100_000, 500_000, 1_000_000, 5_000_000, 10_000_000],
    [CURRENCIES.USD]: [1_000, 5_000, 10_000, 50_000, 100_000, 500_000, 1_000_000],
    [CURRENCIES.GBP]: [1_000, 5_000, 10_000, 50_000, 100_000, 500_000, 1_000_000],
    [CURRENCIES.EUR]: [1_000, 5_000, 10_000, 50_000, 100_000, 500_000, 1_000_000],
  },
};
