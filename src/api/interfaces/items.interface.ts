import { ProductCreateMethod } from "@/components/hooks/useProductController";
import { HighlightVideo, SortItemInterface, BulkDiscountTierInterface, VariantForm } from "../../assets/interfaces";

export interface CreateItemsParams {
  store: string;
  items: {
    name: string;
    images: string[];
    videos?: { name: string; url: string; thumbnail: string }[];
    thumbnail: number;
    thumbnail_type?: string;
    price_unit?: string;
    discount_price?: string;
    description: string;
    tags?: string[];
    category?: string;
    price: Number;
    slug?: string;
    meta?: any;
    upload_source?: ProductCreateMethod;
    info_blocks?: string[];
  }[];
}

export interface ImportItemsParams {
  store_id: string;
  items: string[];
}
export interface GetAllItemsParams {
  type?: "custom" | "storefront";
}
export interface GetItemsParams {
  filter: {
    store?: string;
    search?: string;
    videosOnly?: boolean;
  };
  page?: number;
  per_page?: number;
  sort?: string;
}

export interface GetSortItemsParams {
  page?: number;
  per_page?: number;
  sort?: string;
}

export interface EditItemParams {
  id: string;
  item: {
    name?: string;
    images?: string[];
    thumbnail?: number;
    price_unit?: string;
    discount_price?: number;
    description?: string;
    tags?: string[];
    category?: string;
    price?: Number;
    slug?: string;
    available?: boolean;
    quantity?: number;
    is_always_available?: boolean;
  };
}
export interface BulkUpdateItemsParams {
  items: {
    id: string;
    price?: number;
    quantity?: number;
    is_always_available?: boolean;
    variants?: VariantForm;
    old_price?: number;
  }[];
}

export interface ItemQuantityParams {
  id: string;
  is_always_available?: boolean;
  quantity?: number;
  variants?: VariantForm;
}

export interface EditItemQuantityParams {
  items: ItemQuantityParams[];
}

export interface DeleteItemParams {
  id: string;
}

export interface PaginateSearchParams {
  page?: number;
  per_page?: number;
  sort?: string;
  filter: {
    search?: string;
  };
}

export interface GetStoreItemsParams {
  page?: number;
  per_page?: number;
  sort?: string;
  filter: {
    store: string;
    tags?: string[];
    category?: string;
    search?: string;
  };
  showUnavailableItems?: boolean;
  separateFeaturedItems?: boolean;
  fromDashboard?: boolean;
}

export interface CreateDiscountParams {
  label: string;
  active: boolean;
  percentage: number;
  discount_cap?: number;
  start_date: string;
  end_date: string;
  items: string[];
}
export interface UpdateDiscountParams {
  id: string;
  label?: string;
  active?: boolean;
  percentage?: number;
  discount_cap?: number;
  start_date?: string;
  end_date?: string;
  items?: string[];
}

export interface CreateBulkDiscountParams {
  label: string;
  active: boolean;
  percentage: number;
  discount_cap?: number;
  start_date: string;
  end_date: string;
  items: string[];
  tiers: BulkDiscountTierInterface[];
}

export interface UpdateBulkDiscountParams {
  id: string;
  label?: string;
  active?: boolean;
  percentage?: number;
  discount_cap?: number;
  start_date?: string;
  end_date?: string;
  items: string[];
  tiers: BulkDiscountTierInterface[];
}

export interface DeleteDiscountParams {
  id: string;
}

export interface GetDiscountItemParams {
  id: string;
}

export interface CreateCouponParams {
  coupon_code: string;
  type: string;
  active: boolean;
  end_date: string;
  percentage: number | string;
  price_cap: number | string;
  discount_amount: number | string;
  minimum_order_amount?: number;
}

export interface UpdateCouponParams {
  id: string;
  coupon_code?: string;
  type?: string;
  active?: boolean;
  end_date?: string;
  percentage?: number | string;
  price_cap?: number | string;
  discount_amount?: number | string;
  minimum_order_amount?: number;
}

export interface UpdateSortItemParams {
  items: SortItemInterface[];
}

export interface DeleteCouponParams {
  id: string;
}

export interface CreateCustomItemParams {
  name: string;
  price: number;
  weight?: number;
}
export interface UpdateCustomItemParams {
  id: string;
  name?: string;
  price?: number;
  weight?: number;
}
export interface GetCustomItemsParams {
  filter: {
    store?: string;
    search?: string;
  };
  page?: number;
  per_page?: number;
  sort?: string;
}

export interface GetHighlightParams {
  filter: {
    search?: string;
  };
  page?: number;
  per_page?: number;
  sort?: string;
}

export interface ExportItemsAsCsvParams {
  items: string[];
}

export interface CreateHighlightParams {
  title: string;
  videos: HighlightVideo[];
}

export interface UpdateHighlightParams extends Partial<CreateHighlightParams> {
  id: string;
  active?: boolean;
}

export interface DeleteHighlightParams {
  id: string;
}

// Tiered Pricing Interfaces
export interface PricingTierItemInterface {
  minimum_quantity: number;
  discount_percentage: number;
}

export interface CreateTieredPricingParams {
  label: string;
  active?: boolean;
  tiers: PricingTierItemInterface[];
  items?: string[];
}

export interface UpdateTieredPricingParams {
  id: string;
  label?: string;
  active?: boolean;
  tiers?: PricingTierItemInterface[];
  items?: string[];
}

export interface AssignItemsToTieredPricingParams {
  id: string;
  items: string[];
}

export interface RemoveItemsParams {
  id: string;
  items: string[];
}

export interface CalculatePriceParams {
  base_price: number;
  quantity: number;
  tiered_pricing_id: string;
}

export interface GetTieredPricingParams {
  filter?: {
    active?: boolean;
  };
  page?: number;
  per_page?: number;
}

export interface DeleteTieredPricingParams {
  id: string;
}
