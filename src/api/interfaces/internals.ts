import { CURRENCIES } from "@/assets/interfaces";

export interface InternalLoginParams {
  username: string;
  password: string;
}

export interface GetStoresParams {
  filter?: {
    country?: string;
    plan?: string;
    search?: string;
  };
  pagination: {
    page?: number;
    per_page?: number;
    sort?: string;
  };
}

export interface GetkycParams {
  filter?: {
    verification_method?: string;
    status?: string;
    search?: string;
  };
  pagination: {
    page?: number;
    per_page?: number;
    sort?: string;
  };
}

export interface GetWalletRequestParams {
  filter?: {
    status?: string;
    search?: string;
  };
  pagination: {
    page?: number;
    per_page?: number;
    sort?: string;
  };
}

export interface GetUsersParams {
  pagination: {
    page?: number;
    per_page?: number;
    sort?: string;
  };
  filter?: {
    search?: string;
  };
}

export interface GetTxsParams {
  pagination: {
    page?: number;
    per_page?: number;
    sort?: string;
  };
}

export interface CreatePlanParams {
  name: string;
  type: string;
  country: string;
  amount: number;
  interval: number;
  interval_text: string;
}

export interface UpdatePlanParams {
  id: string;
  amount: number;
  name?: string;
}

export interface ValidateWithdrawalsProps {
  references: string[];
  start_date: string;
  currency: CURRENCIES;
}

export interface PushNotificationParams {
  title: string;
  message: string;
  path: string;
  user_id?: string;
  country?: string;
  emails?: string[];
}
