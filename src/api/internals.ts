import { paramsFromObject } from "../assets/js/utils/functions";
import {
  CreatePlanParams,
  GetStoresParams,
  GetTxsParams,
  GetUsersParams,
  InternalLoginParams,
  PushNotificationParams,
  UpdatePlanParams,
  ValidateWithdrawalsProps,
} from "./interfaces/internals";
import { request } from "./utils";

const InternalLogin = (data: InternalLoginParams) => {
  return request("users/login/internal", "post", { data });
};

const GetStores = (data: GetStoresParams) => {
  const params = paramsFromObject(data);
  return request(`stores?${params}`, "get", {}, "admin");
};

const CreatePlan = (data: CreatePlanParams) => {
  return request(`plans`, "post", { data }, "admin");
};

const UpdatePlan = (data: UpdatePlanParams) => {
  const id = data.id;
  delete data.id;
  return request(`plans/${id}`, "put", { data }, "admin");
};

const GetAnalyticsSummary = () => {
  return request(`analytics/internal/stores`, "get", {}, "admin");
};

const GetUsers = (data: GetUsersParams) => {
  const params = paramsFromObject(data);
  return request(`users?${params}`, "get", {}, "admin");
};

const SendPushNotification = (data: PushNotificationParams) => {
  // Transform data to match API expectations
  const apiData = { ...data };

  // If emails array is provided, send it as emails parameter
  if (data.emails && data.emails.length > 0) {
    apiData.emails = data.emails;
  }

  return request("users/notify-users", "post", { data: apiData }, "admin");
};

const GetAdminKyc = (data: GetUsersParams) => {
  const params = paramsFromObject(data);
  return request(`kyc/admin?${params}`, "get", {}, "admin");
};

const GetAdminWalletRequests = (data: GetUsersParams) => {
  const params = paramsFromObject(data);
  return request(`wallets/admin/requests?${params}`, "get", {}, "admin");
};

const GetExpiringSubscriptions = (data: GetUsersParams) => {
  const params = paramsFromObject(data);
  return request(`subscriptions/expiring?${params}`, "get", {}, "admin");
};

const CheckTransactions = (data: GetTxsParams) => {
  const params = paramsFromObject(data);
  return request(`transactions/admin?${params}`, "get", {}, "admin");
};

const ApproveKyc = (data: { id: string }) => {
  return request(`kyc/admin/${data.id}/approve`, "post", { data }, "admin");
};

const ApproveBvn = (data: { id: string }) => {
  return request(`kyc/admin/${data.id}/approve-bvn`, "post", { data }, "admin");
};

const ApproveWalletRequest = (data: { id: string }) => {
  return request(`wallets/admin/requests/${data.id}/approve`, "post", { data }, "admin");
};

const RejectKyc = (data: { id: string; message: string }) => {
  return request(`kyc/admin/${data.id}/reject`, "post", { data }, "admin");
};

const RejectWalletRequest = (data: { id: string; message: string }) => {
  return request(`wallets/admin/requests/${data.id}/reject`, "post", { data }, "admin");
};

const DownloadPhoneCSV = async (data: { country: string }) => {
  const BASE_URL = process.env.NEXT_PUBLIC_API_URL;
  const adminToken = localStorage === undefined ? null : localStorage.admin_token;

  try {
    const res = await fetch(`${BASE_URL}/users/csv/phone/${data.country}`, {
      headers: { Authorization: `Bearer ${adminToken}` },
    });
    return res;
  } catch (err) {
    return err;
  }
};

const ValidateWithdrawals = (data: ValidateWithdrawalsProps) => {
  return request(`transactions/validate-withdrawals`, "post", { data }, "admin");
};

const GetUserById = (data: { id: string }) => {
  return request(`users/${data.id}`, "get", {}, "admin");
};

const MarkUserAsQualified = (data: { id: string }) => {
  return request(`users/${data.id}/mark-as-qualified`, "put", { data }, "admin");
};

const GetWithdrawalRequest = (data: { id: string }) => {
  return request(`wallets/admin/withdrawal-requests/${data.id}`, "get", {}, "admin");
};

const MarkWithdrawalRequestAsSuccessful = (data: { id: string }) => {
  return request(`wallets/admin/withdrawal-requests/${data.id}/mark-successful`, "post", { data: null }, "admin");
};

const MarkWithdrawalRequestAsFailed = (data: { id: string }) => {
  return request(`wallets/admin/withdrawal-requests/${data.id}/mark-failed`, "post", { data: null }, "admin");
};

export {
  InternalLogin,
  GetStores,
  GetAnalyticsSummary,
  GetUsers,
  GetAdminKyc,
  GetAdminWalletRequests,
  ApproveKyc,
  ApproveWalletRequest,
  RejectKyc,
  RejectWalletRequest,
  GetExpiringSubscriptions,
  ApproveBvn,
  DownloadPhoneCSV,
  CreatePlan,
  UpdatePlan,
  CheckTransactions,
  ValidateWithdrawals,
  SendPushNotification,
  GetUserById,
  MarkUserAsQualified,
  GetWithdrawalRequest,
  MarkWithdrawalRequestAsSuccessful,
  MarkWithdrawalRequestAsFailed,
};
