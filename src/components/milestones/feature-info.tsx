import { ReactElement, ReactNode, useEffect, useState } from "react";

interface Props {
  data: { title: string; description: string; icon?: ReactElement };
  pageKey?: string;
}
const FeatureInfo: React.FC<Props> = ({ data: { title, description, icon }, pageKey }) => {
  return (
    <>
      <div className="flex items-start justify-between py-5 px-5 sm:px-6.25 lg:px-7.5 border-b border-grey-border border-opacity-50 gap-4 leading-4 relative">
        <div className="flex">
          <div className="bg-grey-fields-100 rounded-full mr-4 flex items-center justify-center h-11.25 w-11.25 sm:h-12.5 sm:w-12.5 flex-shrink-0">
            {icon}
          </div>
          <div className="pr-0 sm:pr-0">
            <h3 className=" font-bold font-display text-base sm:text-lg">{title} </h3>
            <span className="text-xs sm:text-sm text-dark"> {description} </span>
          </div>
        </div>
      </div>
    </>
  );
};
export default FeatureInfo;
