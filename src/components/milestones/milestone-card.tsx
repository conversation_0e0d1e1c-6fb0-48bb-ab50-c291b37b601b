import React, { useState, useEffect } from "react";
import Badge from "../ui/badge";
import { millify } from "@/assets/js/utils/functions";

interface Props {
  icon: string;
  title: string;
  description: string;
  progress: number;
  target: number;
  active: boolean;
  isLoading?: boolean;
}

const MilestoneCard: React.FC<Props> = ({ icon, title, description, progress, target, isLoading, active }) => {
  const width = Math.min((progress / target) * 100, 100);

  return (
    <figure className="w-full rounded-10 bg-white p-3.75 md:p-4 border border-grey-border border-opacity-50 relative overflow-hidden">
      <div className="milestone-bg absolute z-10 h-full w-full top-0 left-0"></div>
      <div className="flex flex-col items-center relative z-10">
        <div className="">
          <div className={`rounded-full`}>
            <img
              src={icon}
              alt={title}
              className="w-16 h-16 sm:w-22.5 sm:h-22.5"
              style={{ filter: !active ? "saturate(0%)" : "saturate(100%)" }}
            />
          </div>
        </div>
        <div className="mt-3.75 flex flex-col items-center text-center  gap-y-1.5">
          <h6 className="text-base md:text-lg font-semibold font-display text-black">{title}</h6>
          <p className="text-1xs md:text-sm mb-[10px] mx-6.25 md:mx-2 lg:mx-0 flex-1 text-dark">{description} </p>
          {!isLoading ? (
            <div
              className="flex w-full h-2 bg-grey-border rounded-full overflow-hidden"
              role="progressbar"
              aria-valuenow={progress}
              aria-valuemin={0}
              aria-valuemax={100}
            >
              <div
                className="flex flex-col justify-center rounded-full overflow-hidden bg-green-500 text-xs text-white text-center whitespace-nowrap transition-all duration-500"
                style={{ width: `${width}%` }}
              ></div>
            </div>
          ) : (
            <div className=" rounded-10 w-full bg-white flex items-center h-3">
              <h6 className="rounded-xl h-2 w-full bg-grey-loader animate-pulse"></h6>
            </div>
          )}
        </div>
      </div>
      <Badge
        color="dark"
        greyBg={false}
        className="bg-white p-3 border border-grey-border rounded-full flex items-center justify-center absolute right-2 top-2 text-black-muted"
        text={`${!active ? millify(progress, 0) : millify(target, 0)}/${millify(target, 0)}`}
      />
    </figure>
  );
};

export default MilestoneCard;
