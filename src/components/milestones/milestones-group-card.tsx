import Link from "next/link";
import Router from "next/router";
import React from "react";

interface Props {
  slug: string;
  icon?: string;
  title?: string;
  description?: string;
  linkLabel?: string;
}

const MilestonesGroupCard: React.FC<Props> = ({ slug, icon, title, description, linkLabel }) => {
  return (
    <div
      onClick={() => Router.push(`/my-store/milestones/${slug}`)}
      className="w-full cursor-pointer mx-auto rounded-10 bg-grey-fields-200 p-3.75 md:p-4 border border-grey-border border-opacity-50 overflow-hidden md:max-w-2xl"
    >
      <div className="flex flex-col">
        <div className="">
          {/* Icon/Medal Design */}
          <div className="rounded-full">
            <img src={icon} alt={title} className="w-16 h-16 sm:w-22.5 sm:h-22.5" />
          </div>
        </div>
        <div className="mt-3.75 flex flex-col">
          <h6 className="text-base md:text-lg font-semibold font-display text-black mb-0.5">{title}</h6>
          <p className="text-1xs md:text-sm mb-5 flex-1 text-dark">{description} </p>
          <Link href={`/my-store/milestones/${slug}`}>
            <a className="text-1xs font-semibold text-primary-500">
              <span>{linkLabel}</span>
              {/* prettier-ignore */}
              <svg viewBox="0 0 12 13" fill="none" className="inline-block ml-1.25 mb-0.5 w-[9px] md:w-[11px]">
                <path d="M1 6.5L11 6.5" stroke="#332098" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M6 11.5L11 6.5L6 1.5" stroke="#332098" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </a>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MilestonesGroupCard;
