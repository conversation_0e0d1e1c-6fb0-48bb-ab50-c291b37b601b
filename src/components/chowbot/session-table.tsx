import { GetChowbotSessions } from "@/api/chowbot";
import { GetChowbotSessionsParams } from "@/api/interfaces/chowbot.interface";
import { useFetcher, useRequest } from "@/api/utils";
import { ChowbotSession, SESSION_STATUSES } from "@/assets/interfaces/chowbot";
import { useEffect, useState } from "react";
import ClearSearch from "../clear-search";
import useFluxState from "../hooks/useFluxState";
import { useModals } from "../hooks/useModals";
import usePagination from "../hooks/usePagination";
import useSearchParams from "../hooks/useSearchParams";
import Portal from "../portal";
import AppSearchBar from "../ui/app-search-bar";
import Pagination from "../ui/pagination";
import Table, { TableBody, TableHead, TableHeadItem } from "../ui/table";
import ChowbotContentState from "./content-state";
import SessionDetailsModal from "./session-details";
import SessionItem from "./session-item";
import useScreenSize from "../hooks/useScreenSize";
import { SessionItemMobile } from "./session-item-mobile";
import SessionsHeader from "./sessions-header";
import CustomerDetailsModal from "../orders/modals/customer";

interface Props {}
const ChowbotSessionTable: React.FC<Props> = ({}) => {
  const { currentPage, perPage, setPerPage, goNext, goPrevious, setPage } = usePagination();
  const { search = null, from = null, to = null, status = null } = useSearchParams(["search", "status", "from", "to"]);

  const getSessionsReq = useFetcher<GetChowbotSessionsParams>(GetChowbotSessions, {
    filter: (() => {
      const s = new URLSearchParams(window.location.search).get("search");
      const filter = {};

      if (from) {
        filter["from"] = from;
      }

      if (to) {
        filter["to"] = to;
      }

      if (s ?? search) {
        filter["search"] = s ?? search;
      }

      if (status) {
        filter["status"] = status;
      }

      return filter;
    })(),
    page: currentPage,
    per_page: perPage,
    sort: "DESC",
  });

  const { response, error, isLoading, makeRequest } = getSessionsReq;
  const sessions = response?.data?.data ?? [];
  const pageNotReady = isLoading || error || !response?.data || sessions.length < 1;

  const { modals, toggleModal } = useModals(["details", "customer"]);

  const [searchQuery, setSearchQuery] = useState(search ?? "");
  const [selectedSesssion, setSelectedSession] = useState<ChowbotSession>(null);

  const { width, isSmall } = useScreenSize();

  useEffect(() => {
    if (search !== searchQuery) {
      setSearchQuery(search);
    }
  }, [search]);

  const handleClick = (index: number) => {
    setSelectedSession(sessions[index]);
    toggleModal("details");
  };
  return (
    <div className="w-full pb-10 mt-5">
      <SessionsHeader {...{ searchQuery, setSearchQuery }} />

      <ClearSearch search={search} />
      {pageNotReady && <ChowbotContentState isEmpty={sessions?.length === 0} request={getSessionsReq} />}
      {!pageNotReady && (
        <>
          {!isSmall && (
            <Table className="">
              <TableHead>
                {/* <TableHeadItem>INVOICE ID</TableHeadItem> */}
                <TableHeadItem>CUSTOMER</TableHeadItem>
                <TableHeadItem>DATE STARTED</TableHeadItem>
                <TableHeadItem>STATUS</TableHeadItem>
                <TableHeadItem>ORDER</TableHeadItem>
                <TableHeadItem>DATE ENDED</TableHeadItem>
              </TableHead>
              <TableBody>
                {sessions.map((session, index) => {
                  return <SessionItem session={session} key={index} onClick={() => handleClick(index)} />;
                })}
              </TableBody>
            </Table>
          )}
          {isSmall && (
            <ul className="block md:hidden">
              {sessions.map((session, index) => {
                return (
                  <SessionItemMobile key={index} index={index} session={session} onClick={() => handleClick(index)} />
                );
              })}
            </ul>
          )}
          <Pagination
            data={response?.data ?? {}}
            {...{
              currentPage,
              setPage,
              goNext,
              length: sessions.length,
              label: "sessions",
              goPrevious,
              per_page: perPage,
              setPerPage,
            }}
          />
        </>
      )}

      <Portal>
        {selectedSesssion && (
          <SessionDetailsModal
            toggleCustomerModal={() => toggleModal("customer")}
            show={modals.details.show}
            toggle={() => toggleModal("details")}
            session={selectedSesssion}
          />
        )}
        {selectedSesssion && selectedSesssion?.customer && (
          <CustomerDetailsModal
            show={modals.customer.show}
            toggle={() => toggleModal("customer")}
            customer={selectedSesssion?.customer}
          />
        )}
      </Portal>
    </div>
  );
};
export default ChowbotSessionTable;
