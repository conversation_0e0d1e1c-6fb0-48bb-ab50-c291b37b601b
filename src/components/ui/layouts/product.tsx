import router from "next/router";
import React, { ReactElement, useRef, useState } from "react";
import useClickOutside from "../../hooks/useClickOutside";
import useFluxState from "../../hooks/useFluxState";
import useScreenSize from "../../hooks/useScreenSize";
import useSearchParams from "../../hooks/useSearchParams";
import useTabs from "../../hooks/useTabs";
import AppSearchBar from "../app-search-bar";
import { AppBtn, RoundActionBtn } from "../buttons";
import FeatureInfo from "../feature-info";
import Tabs from "../tabs";
import DashboardLayout from "./dashboard";
import classNames from "classnames";
import Dropdown, { DropdownItem } from "../dropdown-new";

interface Props {
  title: string;
  featureInfo: {
    title: string;
    description: string;
    icon?: ReactElement;
  };
  pageIndex: number;
  onSearch: (query: string) => void;
  action: {
    placeholder?: string;
    onAction?: () => void;
    custom?: React.ReactElement;
    href?: string;
    disabled?: boolean;
  };
  actions?: DropdownItem[];
  pageKey: string;
}

const ProductLayout: React.FC<Props> = ({ title, children, featureInfo, action, actions, pageIndex, pageKey }) => {
  const { search } = useSearchParams(["search"]);
  const [searchQuery, setSearchQuery] = useFluxState(search ?? "");
  const [fullViewOnMobile, setFullViewOnMobile] = useState(false);
  const { isSmall } = useScreenSize();
  const searchBar = useRef(null);
  useClickOutside(searchBar, () => {
    setFullViewOnMobile(false);
  });

  const tabs = useTabs(
    [
      "Products",
      "Other Products",
      "Discounts",
      "Coupons",
      "Categories",
      "Product Highlights",
      "Info Blocks",
      "Tiered Pricing",
    ],
    pageIndex
  );
  const pages = [
    "/products/",
    "/products/other-products",
    "/products/discounts",
    "/products/coupons",
    "/products/categories",
    "/products/product-highlights",
    "/products/info-blocks",
    "/products/pricing-tiers",
    "/products/sort-products",
  ];
  const switchTab = (tab: number) => router.push(`${pages[tab]}`);

  return (
    <DashboardLayout title={"Products"} padding={false}>
      <div className="h-full overflow-y-auto relative">
        <FeatureInfo data={featureInfo} pageKey={pageKey} />
        <div className="px-5 sm:px-6.25 lg:px-7.5 pt-5 sm:pt-7.5 lg:pt-8.75">
          <div className="flex justify-between items-center pb-5">
            <h4
              className={`text-black text-[17px] sm:text-lg lg:text-xl font-bold font-display ${
                fullViewOnMobile && "md:block hidden"
              }`}
            >
              {title}
            </h4>
            <div ref={searchBar} className={`flex items-stretch ${fullViewOnMobile ? " flex-1 md:flex-none" : ""}`}>
              <AppSearchBar
                {...{
                  placeholder: `Search ${title.toLowerCase()}`,
                  searchQuery,
                  setSearchQuery,
                  fullViewOnMobile,
                  setFullViewOnMobile,
                }}
              />
              <div className={`ml-3 md:ml-3.5 ${fullViewOnMobile ? "hidden sm:block" : "block"}`}>
                {action && actions?.length > 0 ? (
                  <Actions actions={actions} />
                ) : (
                  <>
                    {!isSmall && (
                      <AppBtn size="md" disabled={action.disabled} onClick={action.onAction} href={action.href}>
                        {action.placeholder}
                      </AppBtn>
                    )}
                    {isSmall && (
                      <AppBtn
                        disabled={action.disabled}
                        size="md"
                        href={action.href}
                        onClick={action.onAction}
                        className="!rounded-full !p-0 !h-10 !w-10"
                      >
                        {/* prettier-ignore */}
                        <svg width="50%" viewBox="0 0 17 17" fill="none" >
                      <path d="M4.25 8.5h8.5M8.5 12.75v-8.5" stroke="#fff" strokeWidth={1.5} strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                      </AppBtn>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="sticky top-0 bg-white z-50">
            <Tabs tabs={tabs.tabs} switchTab={switchTab} active={tabs.active} />
          </div>
          <div className="mt-7.5">{children}</div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ProductLayout;

export const productPageIcons = {
  category: /* prettier-ignore */
    <svg className="w-full" viewBox="0 0 28 28" fill="none" >
      <path d="M8.44683 2.33337H6.23016C3.67516 2.33337 2.3335 3.67504 2.3335 6.21837V8.43504C2.3335 10.9784 3.67516 12.32 6.2185 12.32H8.43516C10.9785 12.32 12.3202 10.9784 12.3202 8.43504V6.21837C12.3318 3.67504 10.9902 2.33337 8.44683 2.33337Z" fill="currentColor" />
      <path opacity="0.4" d="M21.7818 2.33337H19.5652C17.0218 2.33337 15.6802 3.67504 15.6802 6.21837V8.43504C15.6802 10.9784 17.0218 12.32 19.5652 12.32H21.7818C24.3252 12.32 25.6668 10.9784 25.6668 8.43504V6.21837C25.6668 3.67504 24.3252 2.33337 21.7818 2.33337Z" fill="currentColor" />
      <path d="M21.7818 15.6683H19.5652C17.0218 15.6683 15.6802 17.01 15.6802 19.5533V21.77C15.6802 24.3133 17.0218 25.655 19.5652 25.655H21.7818C24.3252 25.655 25.6668 24.3133 25.6668 21.77V19.5533C25.6668 17.01 24.3252 15.6683 21.7818 15.6683Z" fill="currentColor" />
      <path opacity="0.4" d="M8.44683 15.6683H6.23016C3.67516 15.6683 2.3335 17.01 2.3335 19.5533V21.77C2.3335 24.325 3.67516 25.6667 6.2185 25.6667H8.43516C10.9785 25.6667 12.3202 24.325 12.3202 21.7817V19.565C12.3318 17.01 10.9902 15.6683 8.44683 15.6683Z" fill="currentColor" />
    </svg>,
  coupon: /* prettier-ignore */
    <svg className="w-full"  viewBox="0 0 28 28" fill="none" >
      <path d="M24.8502 12.6466C25.3052 12.6466 25.6668 12.285 25.6668 11.83V10.745C25.6668 5.96163 24.2085 4.5033 19.4252 4.5033H8.57516C3.79183 4.5033 2.3335 5.96163 2.3335 10.745V11.2933C2.3335 11.7483 2.69516 12.11 3.15016 12.11C4.20016 12.11 5.05183 12.9616 5.05183 14.0116C5.05183 15.0616 4.20016 15.9016 3.15016 15.9016C2.69516 15.9016 2.3335 16.2633 2.3335 16.7183V17.2666C2.3335 22.05 3.79183 23.5083 8.57516 23.5083H19.4252C24.2085 23.5083 25.6668 22.05 25.6668 17.2666C25.6668 16.8116 25.3052 16.45 24.8502 16.45C23.8002 16.45 22.9485 15.5983 22.9485 14.5483C22.9485 13.4983 23.8002 12.6466 24.8502 12.6466ZM10.5002 10.36C11.1418 10.36 11.6668 10.885 11.6668 11.5266C11.6668 12.1683 11.1535 12.6933 10.5002 12.6933C9.8585 12.6933 9.3335 12.1683 9.3335 11.5266C9.3335 10.885 9.84683 10.36 10.5002 10.36ZM17.5002 18.5266C16.8468 18.5266 16.3218 18.0016 16.3218 17.36C16.3218 16.7183 16.8468 16.1933 17.4885 16.1933C18.1302 16.1933 18.6552 16.7183 18.6552 17.36C18.6552 18.0016 18.1535 18.5266 17.5002 18.5266ZM18.5502 11.06L10.6985 18.9116C10.5235 19.0866 10.3018 19.1683 10.0802 19.1683C9.8585 19.1683 9.63683 19.0866 9.46183 18.9116C9.1235 18.5733 9.1235 18.0133 9.46183 17.675L17.3135 9.8233C17.6518 9.48496 18.2118 9.48496 18.5502 9.8233C18.8885 10.1616 18.8885 10.7216 18.5502 11.06Z" fill="currentColor" />
    </svg>,
  discount: /* prettier-ignore */
    <svg className="w-full"  viewBox="0 0 28 28" fill="none">
      <path d="M14.0002 2.33337C7.56016 2.33337 2.3335 7.56004 2.3335 14C2.3335 20.44 7.56016 25.6667 14.0002 25.6667C20.4402 25.6667 25.6668 20.44 25.6668 14C25.6668 7.56004 20.4402 2.33337 14.0002 2.33337ZM10.1852 8.93671C11.1302 8.93671 11.9118 9.70671 11.9118 10.6634C11.9118 11.6084 11.1418 12.39 10.1852 12.39C9.24016 12.39 8.4585 11.62 8.4585 10.6634C8.4585 9.70671 9.2285 8.93671 10.1852 8.93671ZM10.3252 18.4334C10.1502 18.6084 9.9285 18.69 9.70683 18.69C9.48516 18.69 9.2635 18.6084 9.0885 18.4334C8.75016 18.095 8.75016 17.535 9.0885 17.1967L16.7302 9.55504C17.0685 9.21671 17.6285 9.21671 17.9668 9.55504C18.3052 9.89337 18.3052 10.4534 17.9668 10.7917L10.3252 18.4334ZM17.8152 19.0634C16.8702 19.0634 16.0885 18.2934 16.0885 17.3367C16.0885 16.3917 16.8585 15.61 17.8152 15.61C18.7602 15.61 19.5418 16.38 19.5418 17.3367C19.5418 18.2934 18.7718 19.0634 17.8152 19.0634Z" fill="currentColor" />
    </svg>,
  otherProducts: /* prettier-ignore */
    <svg className="w-full"  viewBox="0 0 28 28" fill="none" >
      <path opacity="0.4" d="M18.8883 10.3366C18.4333 10.3366 18.0716 9.97497 18.0716 9.51997V8.02663C18.0716 6.8833 17.5816 5.78663 16.7416 5.01663C15.8783 4.23497 14.7699 3.8733 13.6033 3.9783C11.6433 4.16497 9.92826 6.15997 9.92826 8.23663V9.28663C9.92826 9.74163 9.56659 10.1033 9.11159 10.1033C8.65659 10.1033 8.29492 9.74163 8.29492 9.28663V8.23663C8.29492 5.31997 10.6516 2.62497 13.4399 2.35663C15.0616 2.20497 16.6249 2.7183 17.8266 3.81497C19.0166 4.8883 19.6933 6.4283 19.6933 8.02663V9.51997C19.6933 9.97497 19.3316 10.3366 18.8883 10.3366Z" fill="currentColor" />
      <path d="M23.2867 10.4534C22.3067 9.36838 20.6967 8.84338 18.34 8.84338H9.66003C7.30336 8.84338 5.69336 9.36838 4.71336 10.4534C3.5817 11.7134 3.6167 13.3934 3.74503 14.5601L4.5617 21.0584C4.8067 23.3334 5.72836 25.6667 10.745 25.6667H17.255C22.2717 25.6667 23.1934 23.3334 23.4384 21.0701L24.255 14.5484C24.3834 13.3934 24.4184 11.7134 23.2867 10.4534ZM14 21.6767C11.5617 21.6767 9.57836 19.6934 9.57836 17.2551C9.57836 14.8167 11.5617 12.8334 14 12.8334C16.4384 12.8334 18.4217 14.8167 18.4217 17.2551C18.4217 19.6934 16.4384 21.6767 14 21.6767Z" fill="currentColor" />
      <path opacity="0.4" d="M14 21.6767C16.4421 21.6767 18.4217 19.6971 18.4217 17.255C18.4217 14.813 16.4421 12.8334 14 12.8334C11.558 12.8334 9.57837 14.813 9.57837 17.255C9.57837 19.6971 11.558 21.6767 14 21.6767Z" fill="currentColor" />
      <path d="M13.3349 19.4133C13.1133 19.4133 12.8916 19.3316 12.7166 19.1566L11.5616 18.0016C11.2233 17.6633 11.2233 17.1033 11.5616 16.7649C11.8999 16.4266 12.4599 16.4266 12.7983 16.7649L13.3583 17.3249L15.2249 15.5983C15.5749 15.2716 16.1349 15.2949 16.4616 15.6449C16.7883 15.9949 16.7649 16.5549 16.4149 16.8816L13.9299 19.1799C13.7549 19.3316 13.5449 19.4133 13.3349 19.4133Z" fill="currentColor" />
    </svg>,
  storeFrontProducts:  /* prettier-ignore */
  <svg className="w-full" viewBox="0 0 20 20" fill="none">
    <path d="M17.8081 9.49163V14.4833C17.8081 16.7833 15.9414 18.65 13.6414 18.65H6.35807C4.05807 18.65 2.19141 16.7833 2.19141 14.4833V9.54997C2.82474 10.2333 3.72474 10.625 4.69974 10.625C5.74974 10.625 6.75807 10.1 7.39141 9.2583C7.95807 10.1 8.92474 10.625 9.99974 10.625C11.0664 10.625 12.0164 10.125 12.5914 9.29163C13.2331 10.1166 14.2247 10.625 15.2581 10.625C16.2664 10.625 17.1831 10.2166 17.8081 9.49163Z" fill="currentColor" />
    <path d="M12.4921 1.04163H7.49211L6.87545 7.17496C6.82545 7.74163 6.90878 8.27496 7.11711 8.75829C7.60045 9.89163 8.73378 10.625 10.0004 10.625C11.2838 10.625 12.3921 9.90829 12.8921 8.76663C13.0421 8.40829 13.1338 7.99163 13.1421 7.56663V7.40829L12.4921 1.04163Z" fill="currentColor" />
    <path d="M12.4921 1.04163H7.49211L6.87545 7.17496C6.82545 7.74163 6.90878 8.27496 7.11711 8.75829C7.60045 9.89163 8.73378 10.625 10.0004 10.625C11.2838 10.625 12.3921 9.90829 12.8921 8.76663C13.0421 8.40829 13.1338 7.99163 13.1421 7.56663V7.40829L12.4921 1.04163Z" fill="white" fillOpacity="0.71" />
    <path d="M18.6338 6.89163L18.3921 4.58329C18.0421 2.06663 16.9005 1.04163 14.4588 1.04163H11.2588L11.8755 7.29163C11.8838 7.37496 11.8921 7.46663 11.8921 7.62496C11.9421 8.05829 12.0755 8.45829 12.2755 8.81663C12.8755 9.91663 14.0421 10.625 15.2588 10.625C16.3671 10.625 17.3671 10.1333 17.9921 9.26663C18.4921 8.59996 18.7171 7.75829 18.6338 6.89163Z" fill="currentColor" />
    <path d="M18.6338 6.89163L18.3921 4.58329C18.0421 2.06663 16.9005 1.04163 14.4588 1.04163H11.2588L11.8755 7.29163C11.8838 7.37496 11.8921 7.46663 11.8921 7.62496C11.9421 8.05829 12.0755 8.45829 12.2755 8.81663C12.8755 9.91663 14.0421 10.625 15.2588 10.625C16.3671 10.625 17.3671 10.1333 17.9921 9.26663C18.4921 8.59996 18.7171 7.75829 18.6338 6.89163Z" fill="white" fillOpacity="0.3" />
    <path d="M5.49138 1.04163C3.04138 1.04163 1.90804 2.06663 1.54971 4.60829L1.32471 6.89996C1.24137 7.79163 1.48304 8.65829 2.00804 9.33329C2.64137 10.1583 3.61637 10.625 4.69971 10.625C5.91637 10.625 7.08304 9.91663 7.67471 8.83329C7.89137 8.45829 8.03304 8.02496 8.07471 7.57496L8.72471 1.04996H5.49138V1.04163Z" fill="currentColor" />
    <path d="M5.49138 1.04163C3.04138 1.04163 1.90804 2.06663 1.54971 4.60829L1.32471 6.89996C1.24137 7.79163 1.48304 8.65829 2.00804 9.33329C2.64137 10.1583 3.61637 10.625 4.69971 10.625C5.91637 10.625 7.08304 9.91663 7.67471 8.83329C7.89137 8.45829 8.03304 8.02496 8.07471 7.57496L8.72471 1.04996H5.49138V1.04163Z" fill="white" fillOpacity="0.3" />
    <path d="M9.45794 13.8834C8.39961 13.9917 7.59961 14.8917 7.59961 15.9584V18.65H12.3913V16.25C12.3996 14.5084 11.3746 13.6834 9.45794 13.8834Z" fill="currentColor" />
    <path d="M9.45794 13.8834C8.39961 13.9917 7.59961 14.8917 7.59961 15.9584V18.65H12.3913V16.25C12.3996 14.5084 11.3746 13.6834 9.45794 13.8834Z" fill="white" fillOpacity="0.7" />
  </svg>,
  productOptions: /* prettier-ignore */
  <svg className="w-full" viewBox="0 0 35 35" fill="none"  >
    <path opacity="0.4" d="M30.9604 11.1125H22.8812C22.3271 11.1125 21.875 10.6604 21.875 10.0917C21.875 9.5375 22.3271 9.08542 22.8812 9.08542H30.9604C31.5146 9.08542 31.9667 9.5375 31.9667 10.0917C31.9667 10.6604 31.5146 11.1125 30.9604 11.1125Z" fill="currentColor"/>
    <path opacity="0.4" d="M9.42095 11.1125H4.0397C3.48553 11.1125 3.03345 10.6604 3.03345 10.1063C3.03345 9.55209 3.48553 9.10001 4.0397 9.10001H9.42095C9.97511 9.10001 10.4272 9.55209 10.4272 10.1063C10.4272 10.6604 9.97511 11.1125 9.42095 11.1125Z" fill="currentColor"/>
    <path d="M14.8021 15.8083C17.9593 15.8083 20.5188 13.2489 20.5188 10.0917C20.5188 6.93444 17.9593 4.375 14.8021 4.375C11.6449 4.375 9.08545 6.93444 9.08545 10.0917C9.08545 13.2489 11.6449 15.8083 14.8021 15.8083Z" fill="currentColor"/>
    <path opacity="0.4" d="M30.9605 25.9146H25.5792C25.0251 25.9146 24.573 25.4625 24.573 24.9083C24.573 24.3542 25.0251 23.9021 25.5792 23.9021H30.9605C31.5147 23.9021 31.9667 24.3542 31.9667 24.9083C31.9667 25.4625 31.5147 25.9146 30.9605 25.9146Z" fill="currentColor"/>
    <path opacity="0.4" d="M12.1189 25.9146H4.0397C3.48553 25.9146 3.03345 25.4625 3.03345 24.9083C3.03345 24.3542 3.48553 23.9021 4.0397 23.9021H12.1189C12.673 23.9021 13.1251 24.3542 13.1251 24.9083C13.1251 25.4625 12.673 25.9146 12.1189 25.9146Z" fill="currentColor"/>
    <path d="M20.1979 30.625C23.3551 30.625 25.9145 28.0655 25.9145 24.9083C25.9145 21.7511 23.3551 19.1917 20.1979 19.1917C17.0406 19.1917 14.4812 21.7511 14.4812 24.9083C14.4812 28.0655 17.0406 30.625 20.1979 30.625Z" fill="currentColor"/>
  </svg>,
  productHighlights: /* prettier-ignore */
  <svg className="w-full" viewBox="0 0 24 24" fill="none">
    <path opacity="0.4" d="M8.67 14H4C2.9 14 2 14.9 2 16V22H8.67V14Z" fill="currentColor"/>
    <path d="M13.3302 10H10.6602C9.56016 10 8.66016 10.9 8.66016 12V22H15.3302V12C15.3302 10.9 14.4402 10 13.3302 10Z" fill="currentColor"/>
    <path opacity="0.4" d="M20.0001 17H15.3301V22H22.0001V19C22.0001 17.9 21.1001 17 20.0001 17Z" fill="currentColor"/>
    <path d="M15.0097 4.84999C15.3197 4.53999 15.4397 4.16999 15.3397 3.84999C15.2397 3.52999 14.9297 3.3 14.4897 3.23L13.5297 3.06999C13.4897 3.05999 13.3997 2.99998 13.3797 2.95998L12.8497 1.89998C12.4497 1.08998 11.5397 1.08998 11.1397 1.89998L10.6097 2.95998C10.5897 2.99998 10.4997 3.05999 10.4597 3.06999L9.49968 3.23C9.05968 3.3 8.75969 3.52999 8.64969 3.84999C8.54969 4.16999 8.66968 4.53999 8.97968 4.84999L9.71969 5.59999C9.74969 5.62999 9.78968 5.75 9.77968 5.79L9.56968 6.70998C9.40968 7.38998 9.66968 7.69997 9.83968 7.82997C10.0097 7.94997 10.3797 8.10999 10.9897 7.74999L11.8897 7.21999C11.9297 7.18999 12.0597 7.18999 12.0997 7.21999L12.9997 7.74999C13.2797 7.91999 13.5097 7.96999 13.6897 7.96999C13.8997 7.96999 14.0497 7.88997 14.1397 7.82997C14.3097 7.70997 14.5697 7.39998 14.4097 6.70998L14.1997 5.79C14.1897 5.74 14.2197 5.62999 14.2597 5.59999L15.0097 4.84999Z" fill="currentColor"/>
  </svg>,
};

interface ProductsActionsProps {
  // href?: string;
  // onClick?: VoidFunction;
  // placeholder: string;
  actions?: DropdownItem[];
}

const Actions: React.FC<ProductsActionsProps> = ({ actions }) => {
  return (
    <Dropdown items={actions} vPosition="BOTTOM" gap={false} size="lg">
      {(isOpen) => (
        <AppBtn className="z-[999] dropdown-toggle">
          <span>Actions</span>
          {/* prettier-ignore */}
          <svg viewBox="0 0 20 20" fill="none" className={classNames(`transition-transform ml-0.75 sm:ml-1 transform flex-shrink-0 w-4.5 sm:w-[22px]`, { 'rotate-180': isOpen})}>
            <path d="M15 7.5L10 12.5L5 7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </AppBtn>
      )}
    </Dropdown>
  );
};
