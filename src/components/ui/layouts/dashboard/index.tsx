import { ReactNode, useCallback, useEffect, useState } from "react";
import authContext from "../../../../contexts/auth-context";
import QuickActions from "./quick-actions";
import useSearchParams from "../../../hooks/useSearchParams";
import SEOTags from "../../../seo-tags";
import { SubscribeToPlanModal } from "../../../subscriptions";
import DashboardLoader from "./loader";
import DashboardSideBar from "./sidebar";
import Link from "next/link";
import useScreenSize from "../../../hooks/useScreenSize";
import Portal from "../../../portal";
import DashboardBanner from "./dashboard-banner";
import { AppBtn } from "../../buttons";
import { WHATSAPP_LINK } from "../../../../assets/js/utils/constants";
import EnablePushModal from "./enable-push-modal";
import NewStoreAnnouncementModal from "./announcement-modals/new-store-announcement-modal";
import CustomDomainsAnnouncementModal from "./announcement-modals/custom-domains-announcement-modal";
import MobileComingSoonAnnouncementModal from "./announcement-modals/mobile-app-coming-soon-announcement";

interface DashboardLayoutProps {
  sidebarLoader?: {
    store: boolean;
    navLinks: boolean;
    isOnboarding?: boolean;
  };
  HeaderAddon?: React.ReactElement;
  children?: React.ReactNode;
  title: string;
  showBanner?: boolean;
  padding?: boolean;
  bannerConfig?: {
    show: boolean;
    content?: ReactNode | null;
  };
  breadCrumb?: {
    parent: {
      label: string;
      path: string;
    };
  };
  showSupportBtn?: boolean;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  sidebarLoader,
  title,
  children,
  padding = true,
  bannerConfig,
  HeaderAddon,
  breadCrumb,
  showSupportBtn = false,
}) => {
  const [navIsOpen, setNavIsOpen] = useState(false);
  const {
    fetchError,
    pageNotReady,
    storeLink,
    subscription,
    user,
    appModals,
    toggleAppModals,
    store,
    isSwitchingStore,
  } = authContext.useContainer();
  const { renew_plan } = useSearchParams(["renew_plan"]);
  const showRenewalModal = appModals.renewal.show;
  const toggleRenewalModal = () => toggleAppModals("renewal");
  const { isSmall } = useScreenSize();

  const appBodyRef = useCallback((node: HTMLDivElement) => {
    let lastScrollTop = 0;
    let isManuallyScrolling = false;
    let isInitialBottomScroll = true;
    let isInitialTopScroll = true;

    if (node) {
      const scrollBody: HTMLDivElement = node.querySelector(".app-body .overflow-y-auto");
      const dashboardBanner: HTMLDivElement = document.querySelector(".dashboard-banner");
      const mobileBreadcrumb: HTMLDivElement = document.querySelector(".mobile-breadcrumb");

      if (scrollBody) {
        scrollBody.addEventListener(
          "scroll",
          function () {
            var st = scrollBody.scrollTop;
            const ignore = isManuallyScrolling;
            isManuallyScrolling = false;

            if (ignore) return;

            if (st > lastScrollTop) {
              // console.log("Scrolling down");
              dashboardBanner.style.display = "none";
              mobileBreadcrumb.style.display = "none";

              if (isInitialBottomScroll) {
                scrollBody.scrollTop = st + 5;
                isManuallyScrolling = true;
                isInitialBottomScroll = false;
                isInitialTopScroll = true;
              }
            } else {
              // console.log("Scrolling up");
              dashboardBanner.style.display = "block";
              mobileBreadcrumb.style.display = "block";

              if (isInitialTopScroll) {
                scrollBody.scrollTop = st - 5;
                isManuallyScrolling = true;
                isInitialBottomScroll = true;
                isInitialTopScroll = false;
              }
            }

            lastScrollTop = st <= 0 ? 0 : st; // For Mobile or negative scrolling
          },
          false
        );
      }
    }
  }, []);

  useEffect(() => {
    if (renew_plan) {
      toggleRenewalModal();
    }
  }, [renew_plan]);

  const handleRefresh = async () => {
    window.location.reload();
  };

  if (pageNotReady || fetchError) {
    return <DashboardLoader fetchError={fetchError} />;
  }

  const computedSidebarLoader = {
    store: sidebarLoader?.store || isSwitchingStore,
    navLinks: sidebarLoader?.navLinks || isSwitchingStore,
    isOnboarding: sidebarLoader?.isOnboarding || isSwitchingStore,
  };

  return (
    <>
      <SEOTags tags={seoTags} />
      <main className="w-full fixed inset-0 bg-grey-bg overflow-hidden">
        <div className="h-full w-full grid grid-cols-1 md:grid-cols-dashboard-md lg:grid-cols-dashboard-lg overflow-hidden">
          <DashboardSideBar loader={computedSidebarLoader} />
          <div
            className="bg-white h-full w-full border-l border-grey-border border-opacity-50 overflow-y-auto flex flex-col transition-all ease-out duration-300"
            ref={appBodyRef}
          >
            <>
              {/* <div className="h-12.5 bg-accent-yellow-pastel w-full"></div> */}
              <div className="bg-white dashboard-banner">
                <DashboardBanner {...bannerConfig} {...{ store, subscription, user, toggleRenewalModal }} />
              </div>
              <header className="flex items-center justify-between h-16 sm:h-18 px-5 sm:px-6.25 lg:px-7.5 bg-white border-b border-grey-border border-opacity-50">
                <div className="flex items-center">
                  <div className="flex md:hidden mr-1 sm:mr-2">
                    <input
                      type="checkbox"
                      id="nav-toggle-checkbox"
                      onChange={(e) => setNavIsOpen(e.target.checked)}
                      checked={navIsOpen}
                      className="hidden"
                    />
                    <label className="nav-toggle cursor-pointer" htmlFor="nav-toggle-checkbox">
                      <span className="nav-toggle-inner"></span>
                    </label>
                    <div
                      className={`fixed bg-black bg-opacity-50 inset-0 z-[100000] ease-out transition-opacity transform ${
                        navIsOpen ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-full"
                      }`}
                    >
                      <div
                        onClick={() => setNavIsOpen(false)}
                        className={`flex items-start h-full transform transition-all duration-300 ease-out cursor-pointer ${
                          navIsOpen ? "translate-x-0 opacity-100" : "-translate-x-full opacity-0"
                        }`}
                      >
                        <div
                          className="h-full flex-1 w-full max-w-[320px] cursor-default overflow-hidden"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <DashboardSideBar mobile={true} loader={computedSidebarLoader} />
                        </div>
                        <button
                          type="button"
                          onClick={() => setNavIsOpen(false)}
                          className={`no-outline h-6.25 w-6.25 rounded-full bg-white border border-placeholder ml-4 mt-2 transition-all text-placeholder hover:border-primary-500 hover:text-primary-300 flex items-center justify-center`}
                        >
                          {/* prettier-ignore */}
                          <svg width="50%" viewBox="0 0 19.528 19.529">
                            <g transform="translate(-314.611 -73.746)">
                              <line y2="25.617" transform="translate(333.432 74.454) rotate(45)" fill="none" stroke="currentColor" strokeWidth="2" />
                              <line y2="25.617" transform="translate(315.318 74.454) rotate(-45)" fill="none" stroke="currentColor" strokeWidth="2" />
                            </g>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                  {(!breadCrumb || isSmall) && (
                    <h4 className="text-black text-base md:text-lg font-bold font-display overflow-hidden overflow-ellipsis mr-3.5">
                      {title}
                    </h4>
                  )}
                  {breadCrumb && !isSmall && (
                    <h4 className="hidden sm:flex items-center space-x-2.5 sm:space-x-3 font-display text-sm sm:text-base max-w-[180px] sm:max-w-[280px] lg:max-w-[300px] overflow-hidden">
                      <Link href={breadCrumb.parent.path}>
                        <a href="" className="text-black font-bold !leading-none flex-shrink-0 inline-block py-2">
                          {breadCrumb.parent.label}
                        </a>
                      </Link>
                      {/* prettier-ignore */}
                      <svg viewBox="0 0 6 11" fill="none" className="mt-1 flex-shrink-0 w-1.5 sm:w-1.75">
                        <path d="M1 1.5L5 5.5L1 9.5" stroke="#AAAAAA" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <span className="text-dark !leading-none whitespace-nowrap overflow-ellipsis flex-1 overflow-hidden inline-block py-2">
                        {title}
                      </span>
                    </h4>
                  )}
                </div>
                {HeaderAddon && !showSupportBtn && HeaderAddon}
                {!sidebarLoader?.navLinks && !HeaderAddon && !showSupportBtn && (
                  <QuickActions {...{ storeLink, user, store }} />
                )}
                {showSupportBtn && (
                  <AppBtn color="success" size="sm" href={WHATSAPP_LINK}>
                    {/* prettier-ignore */}
                    <svg width="18" viewBox="0 0 24 24" fill="none" className="mr-1">
                      <path d="M16.6 13.9996C16.4 13.8996 15.1 13.2996 14.9 13.1996C14.7 13.0996 14.5 13.0996 14.3 13.2996C14.1 13.4996 13.7 14.0996 13.5 14.2996C13.4 14.4996 13.2 14.4996 13 14.3996C12.3 14.0996 11.6 13.6996 11 13.1996C10.5 12.6996 10 12.0996 9.6 11.4996C9.5 11.2996 9.6 11.0996 9.7 10.9996C9.8 10.8996 9.9 10.6996 10.1 10.5996C10.2 10.4996 10.3 10.2996 10.3 10.1996C10.4 10.0996 10.4 9.89961 10.3 9.79961C10.2 9.69961 9.7 8.49961 9.5 7.99961C9.4 7.29961 9.2 7.29961 9 7.29961C8.9 7.29961 8.7 7.29961 8.5 7.29961C8.3 7.29961 8 7.49961 7.9 7.59961C7.3 8.19961 7 8.89961 7 9.69961C7.1 10.5996 7.4 11.4996 8 12.2996C9.1 13.8996 10.5 15.1996 12.2 15.9996C12.7 16.1996 13.1 16.3996 13.6 16.4996C14.1 16.6996 14.6 16.6996 15.2 16.5996C15.9 16.4996 16.5 15.9996 16.9 15.3996C17.1 14.9996 17.1 14.5996 17 14.1996C17 14.1996 16.8 14.0996 16.6 13.9996ZM19.1 4.89961C15.2 0.999609 8.9 0.999609 5 4.89961C1.8 8.09961 1.2 12.9996 3.4 16.8996L2 21.9996L7.3 20.5996C8.8 21.3996 10.4 21.7996 12 21.7996C17.5 21.7996 21.9 17.3996 21.9 11.8996C22 9.29961 20.9 6.79961 19.1 4.89961ZM16.4 18.8996C15.1 19.6996 13.6 20.1996 12 20.1996C10.5 20.1996 9.1 19.7996 7.8 19.0996L7.5 18.8996L4.4 19.6996L5.2 16.6996L5 16.3996C2.6 12.3996 3.8 7.39961 7.7 4.89961C11.6 2.39961 16.6 3.69961 19 7.49961C21.4 11.3996 20.3 16.4996 16.4 18.8996Z" fill="currentColor"/>
                    </svg>
                    Need Help?
                  </AppBtn>
                )}
              </header>
              <div className="mobile-breadcrumb">
                {breadCrumb && isSmall && (
                  <div className="py-3.5 border-b border-grey-border border-opacity-50 px-5 sm:px-6.25 lg:px-7.5 leading-none">
                    <Link href={breadCrumb.parent.path}>
                      <a className="flex items-center sm:hidden text-dark hover:text-primary-500 ease-out transition-all duration-200 font-semibold max-w-full overflow-hidden overflow-ellipsis whitespace-nowrap leading-none text-sm">
                        {/* prettier-ignore */}
                        <svg height="18" viewBox="0 0 24 24" fill="none" className="mr-2">
                          <path d="M9.57 5.92993L3.5 11.9999L9.57 18.0699" stroke="currentColor" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M20.5 12H3.67004" stroke="currentColor" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        {breadCrumb.parent.label}
                      </a>
                    </Link>
                  </div>
                )}
              </div>
              <div className={`flex-1 overflow-hidden relative app-body ${padding ? "px-5 sm:px-6.25 lg:px-7.5" : ""}`}>
                {!isSwitchingStore ? (
                  children
                ) : (
                  <div className="pt-[160px]">
                    <div>
                      <div className="spinner text-primary-500 m-auto mb-5"></div>
                      <span className="text-center w-full block text-primary-700 font-semibold text-sm">
                        Switching stores
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </>
          </div>
          <Portal>
            {subscription?.status === "INACTIVE" && subscription?.plan?.type !== "STARTER" && (
              <SubscribeToPlanModal
                show={showRenewalModal}
                toggle={toggleRenewalModal}
                plan={subscription?.plan}
                renew
              />
            )}
            <EnablePushModal />
            {/* <WrappedAnnouncementModal /> */}
            {/* <NewStoreAnnouncementModal />
            <CustomDomainsAnnouncementModal /> */}
            <MobileComingSoonAnnouncementModal />
          </Portal>
        </div>
      </main>
    </>
  );
};

DashboardLayout.defaultProps = {
  bannerConfig: {
    show: true,
    content: null,
  },
};

const seoTags = {
  title: "Dashboard - Catlog",
  description: "Manage your catlog store, products, analytics and much more.",
  pageUrl: "/",
  image: "https://res.cloudinary.com/catlog/image/upload/v1674314911/seo-banners/Dashboard_Banner.png",
};

export default DashboardLayout;
