import React, { useEffect, useState } from "react";
import { AppBtn } from "../../../buttons";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalProps } from "../../../modal";
import authContext from "@/contexts/auth-context";
import dayjs from "dayjs";
import { useRouter } from "next/router";
import Confetti from "react-confetti";
import { toAppUrl } from "@/assets/js/utils/functions";
import StoreArrow from "@/assets/icons/storebanner-arrow.svg";

const NewStoreAnnouncementModal: React.FC = () => {
  const router = useRouter();
  const [showModal, setShowModal] = useState(false);
  const { store, storeLink, user } = authContext.useContainer();

  const shouldShowModal =
    showModal &&
    Boolean(store?.subscription) &&
    dayjs().isBefore(dayjs("2025-06-15")) &&
    dayjs(user?.created_at).isBefore(dayjs("2025-05-26"));

  useEffect(() => {
    setTimeout(() => {
      const hasShown = !!sessionStorage.getItem("shown-newstore-modal");

      if (!hasShown) {
        setShowModal(true);
      }
    }, 3000);
  }, []);

  const closeModal = () => {
    setShowModal(false);
    sessionStorage.setItem("shown-newstore-modal", "true");
  };

  return (
    <>
      <Modal {...{ show: shouldShowModal, toggle: closeModal }} title="" className="z-[1000]" showHeader={false}>
        <ModalBody noPadding className="rounded-t-10 overflow-hidden">
          <figure className="w-full sticky top-0">
            <img
              src="/images/new-store.png"
              alt="Banner Announcing Catlog's New Storefront"
              className="object-cover w-full h-[300px]"
            />
          </figure>
          <div className="p-5 sm:p-6.25 flex-1 overflow-auto flex-col flex items-center max-w-[450px] mx-auto">
            <h2 className="font-bold font-display text-2xl sm:text-3xl text-center !leading-[1.05] text-[#191555]">
              Your New Storefront is Live!
            </h2>
            <p className="text-dark text-sm text-center mt-3.5 mb-3.5 max-w-[345px] mx-auto">
              We’ve given your storefront a fresh new look with a cleaner design and improved layout, making it easier
              for your customers to shop from you.
              <div className="absolute bottom-22.5 md:bottom-24">
                <StoreArrow />
              </div>
            </p>
          </div>
          <Confetti width={1000} height={1000} numberOfPieces={500} recycle={false} style={{ zIndex: 200 }} />
        </ModalBody>
        <ModalFooter>
          <AppBtn
            onClick={() => window.open(storeLink, "_blank")}
            size="lg"
            className="flex-1 flex items-center gap-1.5"
          >
            Learn More
            {/* prettier ignore */}
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M3.75 9H14.25M14.25 9L9 3.75M14.25 9L9 14.25"
                stroke="currentColor"
                strokeWidth="1.6"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </AppBtn>
        </ModalFooter>
      </Modal>
    </>
  );
};

interface Props extends ModalProps {}

export default NewStoreAnnouncementModal;
