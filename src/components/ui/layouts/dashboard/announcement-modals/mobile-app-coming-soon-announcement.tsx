import React, { useEffect, useState } from "react";
import { AppBtn } from "../../../buttons";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalProps } from "../../../modal";
import authContext from "@/contexts/auth-context";
import dayjs from "dayjs";
import { useRouter } from "next/router";
import Confetti from "react-confetti";

const link = `https://forms.gle/qs9HC1tQ4LezVwzp7`;

const MobileComingSoonAnnouncementModal: React.FC = () => {
  const router = useRouter();
  const { store, storeLink, user } = authContext.useContainer();
  const [showModal, setShowModal] = useState(false);

  const shouldShowModal =
    showModal && Boolean(store?.subscription) && dayjs(user?.created_at).isBefore(dayjs("2025-08-06"));
  dayjs().isBefore(dayjs("2025-08-15"));

  useEffect(() => {
    const shouldShow = !localStorage.getItem("shown-mobile-app-coming-soon-modal");
    const hasShownThisSession = !!sessionStorage.getItem("shown-mobile-app-coming-soon-modal");

    setTimeout(() => {
      if (shouldShow && !hasShownThisSession) {
        setShowModal(true);
      } else {
        setShowModal(false);
      }
    }, 3000);
  }, []);

  const closeModal = (saveToLocalStorage = false) => {
    setShowModal(false);
    sessionStorage.setItem("shown-mobile-app-coming-soon-modal", "true");
    if (saveToLocalStorage) {
      localStorage.setItem("shown-mobile-app-coming-soon-modal", "true");
    }
  };

  return (
    <>
      <Modal
        {...{ show: shouldShowModal, toggle: closeModal }}
        size="midi"
        title=""
        className="z-[1000]"
        showHeader={false}
      >
        <ModalBody noPadding className="rounded-t-10 overflow-hidden">
          <figure className="w-full sticky top-0">
            <img
              src="/images/app-coming-soon.jpg"
              alt="Banner Announcing the New Catlog Mobile App"
              className="w-full"
            />
          </figure>
          <div className="p-5 sm:p-6.25 flex-1 overflow-auto flex-col flex items-center max-w-[450px] mx-auto">
            <h2 className="font-bold font-display text-xl sm:text-2xl text-center !leading-[1.05] text-black">
              The Catlog app is coming soon! 🎉
            </h2>
            <p className="text-dark text-sm text-center mt-3.5 mb-3.5 max-w-[400px] mx-auto">
              The mobile app is almost here — to help you manage your business more easily, wherever you are.
            </p>
          </div>
          <Confetti width={1000} height={1000} numberOfPieces={500} recycle={false} style={{ zIndex: 200 }} />
        </ModalBody>
        <ModalFooter>
          <AppBtn
            onClick={() => {
              window.open(link, "_blank");
              closeModal(true);
            }}
            size="lg"
            className="flex-1 flex items-center gap-1.5"
          >
            Be the first to know
          </AppBtn>
        </ModalFooter>
      </Modal>
    </>
  );
};

interface Props extends ModalProps {}

export default MobileComingSoonAnnouncementModal;
