import React, { useEffect, useState } from "react";
import { AppBtn } from "../../../buttons";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalProps } from "../../../modal";
import authContext from "@/contexts/auth-context";
import dayjs from "dayjs";
import { useRouter } from "next/router";
import Confetti from "react-confetti";
import { toAppUrl } from "@/assets/js/utils/functions";
import StoreArrow from "@/assets/icons/storebanner-arrow.svg";

const link = `/my-store/configurations?tab=custom_domains`;
const CustomDomainsAnnouncementModal: React.FC = () => {
  const router = useRouter();
  const [showModal, setShowModal] = useState(false);
  const { store, storeLink, user } = authContext.useContainer();

  const shouldShowModal =
    showModal &&
    Boolean(store?.subscription) &&
    dayjs().isBefore(dayjs("2025-07-31")) &&
    dayjs(user?.created_at).isBefore(dayjs("2025-07-20"));

  useEffect(() => {
    setTimeout(() => {
      const hasShown = !!localStorage.getItem("shown-custom-domains-modal");

      if (!hasShown) {
        setShowModal(true);
      }
    }, 3000);
  }, []);

  const closeModal = () => {
    setShowModal(false);
    localStorage.setItem("shown-custom-domains-modal", "true");
  };

  return (
    <>
      <Modal
        {...{ show: shouldShowModal, toggle: closeModal }}
        size="midi"
        title=""
        className="z-[1000]"
        showHeader={false}
      >
        <ModalBody noPadding className="rounded-t-10 overflow-hidden">
          <figure className="w-full sticky top-0">
            <img src="/images/custom-dom.jpg" alt="Banner Announcing Catlog's Custom Domains" className="w-full" />
          </figure>
          <div className="p-5 sm:p-6.25 flex-1 overflow-auto flex-col flex items-center max-w-[450px] mx-auto">
            <h2 className="font-bold font-display text-xl sm:text-2xl text-center !leading-[1.05] text-black">
              Introducing Custom Domains! 🎉
            </h2>
            <p className="text-dark text-sm text-center mt-3.5 mb-3.5 max-w-[400px] mx-auto">
              You can now buy or connect an existing domain name to your Catlog store - to help you look more
              professional.
            </p>
          </div>
          <Confetti width={1000} height={1000} numberOfPieces={500} recycle={false} style={{ zIndex: 200 }} />
        </ModalBody>
        <ModalFooter>
          <AppBtn
            onClick={() => {
              closeModal();
              router.push(link);
            }}
            size="lg"
            className="flex-1 flex items-center gap-1.5"
          >
            Check it out
            {/* prettier ignore */}
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M3.75 9H14.25M14.25 9L9 3.75M14.25 9L9 14.25"
                stroke="currentColor"
                strokeWidth="1.6"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </AppBtn>
        </ModalFooter>
      </Modal>
    </>
  );
};

interface Props extends ModalProps {}

export default CustomDomainsAnnouncementModal;
