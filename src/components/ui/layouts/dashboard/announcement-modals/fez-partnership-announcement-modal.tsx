import React, { useEffect, useState } from "react";
import { AppBtn } from "../../../buttons";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalProps } from "../../../modal";
import authContext from "@/contexts/auth-context";
import dayjs from "dayjs";
import { useRouter } from "next/router";
import { COUNTRIES } from "@/assets/interfaces";
import Confetti from "react-confetti";
import { useModals } from "@/components/hooks/useModals";
import { useAppendScript } from "@/components/hooks/useAppendScript";

const FezPartnershipAnnouncementModal: React.FC = () => {
  const router = useRouter();
  const [showModal, setShowModal] = useState(false);
  const { store } = authContext.useContainer();

  const { modals, toggleModal } = useModals(["explainer"]);

  const shouldShowModal =
    showModal &&
    Boolean(store?.subscription) &&
    dayjs().isBefore(dayjs("2024-12-03")) &&
    store.country?.code === COUNTRIES.NG;

  useEffect(() => {
    setTimeout(() => {
      const hasShown = !!sessionStorage.getItem("shown-fez-modal");

      if (!hasShown) {
        setShowModal(true);
      }
    }, 3000);
  }, []);

  const closeModal = () => {
    setShowModal(false);
    sessionStorage.setItem("shown-fez-modal", "true");
  };

  const goToDeliveries = () => {
    closeModal();
    router.push("/deliveries");
  };

  return (
    <>
      <Modal
        {...{ show: shouldShowModal, toggle: closeModal }}
        title=""
        size="midi"
        className="z-[1000]"
        showHeader={false}
      >
        <ModalBody noPadding className="rounded-t-10 overflow-hidden">
          <figure className="w-full sticky top-0">
            <img src="/images/catlog-fez-cover.png" alt="Banner Announcing Catlog Deliveries" className="w-full" />
          </figure>
          <div className="p-5 sm:p-6.25 flex-1 overflow-auto flex-col flex items-center max-w-[450px] mx-auto">
            <h2 className="text-black font-bold font-display text-2lg text-center leading-tight">
              Deliver for <span className="text-primary-500">NGN 2,000</span> in-state, <br />
              <span className="text-primary-500">NGN 3,500</span> interstate anywhere <br /> in Nigeria 🇳🇬
            </h2>
            <p className="text-dark text-sm text-center mt-1">
              Process orders through your store and book deliveries <br /> with <b className="font-semibold">Fez</b> on
              Catlog to enjoy these flat rates
            </p>
            <div className="mt-3 75 inline-flex items-center bg-primary-400 bg-opacity-10 rounded-30 px-3 py-2 text-primary-400 text-sm font-medium">
              {/* prettier-ignore */}
              <svg viewBox="0 0 20 20" fill="none" className="w-5 mr-2">
              <path d="M6.66699 1.66663V4.16663" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M13.333 1.66663V4.16663" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M2.91699 7.57495H17.0837" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M18.3337 15.8333C18.3337 16.4583 18.1587 17.05 17.8503 17.55C17.2753 18.5167 16.217 19.1667 15.0003 19.1667C14.1587 19.1667 13.392 18.8583 12.8087 18.3333C12.5503 18.1167 12.3253 17.85 12.1503 17.55C11.842 17.05 11.667 16.4583 11.667 15.8333C11.667 13.9917 13.1587 12.5 15.0003 12.5C16.0003 12.5 16.892 12.9417 17.5003 13.6333C18.017 14.225 18.3337 14.9917 18.3337 15.8333Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M13.7002 15.8333L14.5252 16.6583L16.3002 15.0166" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M17.5 7.08329V13.6333C16.8917 12.9416 16 12.5 15 12.5C13.1583 12.5 11.6667 13.9916 11.6667 15.8333C11.6667 16.4583 11.8417 17.05 12.15 17.55C12.325 17.85 12.55 18.1166 12.8083 18.3333H6.66667C3.75 18.3333 2.5 16.6666 2.5 14.1666V7.08329C2.5 4.58329 3.75 2.91663 6.66667 2.91663H13.3333C16.25 2.91663 17.5 4.58329 17.5 7.08329Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M9.99607 11.4167H10.0036" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M6.91209 11.4167H6.91957" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M6.91209 13.9167H6.91957" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
              Offer valid Nov 29 - Dec 2. Don&apos;t miss out!
            </div>
          </div>
          <Confetti width={1000} height={1000} numberOfPieces={500} recycle={false} style={{ zIndex: 200 }} />
        </ModalBody>
        <ModalFooter>
          <div className="flex items-center w-full space-x-3.75">
            <AppBtn isBlock size="lg" color="neutral" className="flex-1" onClick={() => toggleModal("explainer")}>
              Learn More
            </AppBtn>
            <AppBtn isBlock onClick={goToDeliveries} size="lg" className="flex-1">
              Start Shipping
            </AppBtn>
          </div>
        </ModalFooter>
      </Modal>
      <FezPartnershipExplainerModal show={modals.explainer.show} toggle={() => toggleModal("explainer")} />
    </>
  );
};

interface Props extends ModalProps {}

export const FezPartnershipExplainerModal: React.FC<Props> = ({ show, toggle }) => {
  return (
    <Modal {...{ show, toggle }} title="How to enjoy discount">
      <ModalBody>
        <div className="text-dark text-sm">
          We've partnered with Fez to bring you discounted delivery rates for your orders. Enjoy flat rates of{" "}
          <b className="font-medium">NGN 2,000</b> for same state deliveries and{" "}
          <b className="font-medium">NGN 3,500</b> for interstate deliveries anywhere in Nigeria. This offer is valid
          from November 29th to December 2nd.
          <br />
          <br />
          <h4 className="font-display font-bold text-black text-base">To Take Advantage of this Discount</h4>
          <ul className="space-y-3 max-w-md mx-auto mt-3 ml-0">
            {steps.map((step, index) => (
              <li className="flex items-start" key={index}>
                <figure className="h-4.5 w-4.5 bg-accent-green-500 flex items-center justify-center text-white rounded-full flex-shrink-0 mr-2 mt-0.5">
                  {/* prettier-ignore */}
                  <svg width="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="24" height="24" rx="12" fill="#39B588"/>
                    <path d="M10.2951 16.5C10.055 16.5 9.82691 16.3995 9.65883 16.2238L6.26113 12.6702C5.91296 12.3061 5.91296 11.7033 6.26113 11.3392C6.6093 10.9751 7.18559 10.9751 7.53377 11.3392L10.2951 14.2272L16.4662 7.77311C16.8144 7.40896 17.3907 7.40896 17.7389 7.77311C18.087 8.13725 18.087 8.73997 17.7389 9.10412L10.9315 16.2238C10.7634 16.3995 10.5353 16.5 10.2951 16.5Z" fill="white"/>
                  </svg>
                </figure>
                <span className="inline-block text-dark text-sm max-w-sm">{step}</span>
              </li>
            ))}
          </ul>
          <br />
          <h4 className="font-display font-bold text-black text-base">Here's a video to guide you</h4>
          <br />
          <InstagramEmbed />
          <br />
          <br />
        </div>
      </ModalBody>
    </Modal>
  );
};

const steps = [
  "1. Take orders and payments on your Catlog store",
  "2. Go to the order page and click in 'Request Delivery' on the order you want to ship",
  "3. Select Fez delivery as 'How you'll like to deliver'",
  "4. Fill in the rest of the information and enjoy the flat rates 🎉",
];

const InstagramEmbed = () => {
  useAppendScript("https://www.instagram.com/embed.js", "IG_EMBED_SCRIPT");

  return (
    <blockquote
      className="instagram-media"
      data-instgrm-permalink="https://www.instagram.com/reel/DCzUlQ9uev0/?utm_source=ig_embed&amp;utm_campaign=loading"
      data-instgrm-version="14"
      style={{
        background: "#FFF",
        border: 0,
        borderRadius: "3px",
        boxShadow: "0 0 1px 0 rgba(0,0,0,0.5), 0 1px 10px 0 rgba(0,0,0,0.15)",
        margin: "1px",
        maxWidth: "540px",
        minWidth: "326px",
        padding: 0,
        width: "calc(100% - 2px)",
      }}
    >
      <div style={{ padding: "16px" }}>
        <a
          href="https://www.instagram.com/reel/DCzUlQ9uev0/?utm_source=ig_embed&amp;utm_campaign=loading"
          style={{
            background: "#FFFFFF",
            lineHeight: 0,
            padding: "0 0",
            textAlign: "center",
            textDecoration: "none",
            width: "100%",
          }}
          target="_blank"
          rel="noopener noreferrer"
        >
          <div style={{ display: "flex", flexDirection: "row", alignItems: "center" }}>
            <div
              style={{
                backgroundColor: "#F4F4F4",
                borderRadius: "50%",
                flexGrow: 0,
                height: "40px",
                marginRight: "14px",
                width: "40px",
              }}
            />
            <div style={{ display: "flex", flexDirection: "column", flexGrow: 1, justifyContent: "center" }}>
              <div
                style={{
                  backgroundColor: "#F4F4F4",
                  borderRadius: "4px",
                  flexGrow: 0,
                  height: "14px",
                  marginBottom: "6px",
                  width: "100px",
                }}
              />
              <div
                style={{
                  backgroundColor: "#F4F4F4",
                  borderRadius: "4px",
                  flexGrow: 0,
                  height: "14px",
                  width: "60px",
                }}
              />
            </div>
          </div>
          <div style={{ padding: "19% 0" }}></div>
          <div style={{ display: "block", height: "50px", margin: "0 auto 12px", width: "50px" }}>
            <svg width="50px" height="50px" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
              {/* SVG Path */}
              <path fill="#000000" d="M556.869,30.41..." />
            </svg>
          </div>
          <div style={{ paddingTop: "8px" }}>
            <div
              style={{
                color: "#3897f0",
                fontFamily: "Arial, sans-serif",
                fontSize: "14px",
                fontStyle: "normal",
                fontWeight: 550,
                lineHeight: "18px",
              }}
            >
              View this post on Instagram
            </div>
          </div>
          <div style={{ padding: "12.5% 0" }}></div>
          {/* Additional flex elements */}
        </a>
        <p
          style={{
            color: "#c9c8cd",
            fontFamily: "Arial, sans-serif",
            fontSize: "14px",
            lineHeight: "17px",
            marginBottom: 0,
            marginTop: "8px",
            overflow: "hidden",
            padding: "8px 0 7px",
            textAlign: "center",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          <a
            href="https://www.instagram.com/reel/DCzUlQ9uev0/?utm_source=ig_embed&amp;utm_campaign=loading"
            style={{
              color: "#c9c8cd",
              fontFamily: "Arial, sans-serif",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: "normal",
              lineHeight: "17px",
              textDecoration: "none",
            }}
            target="_blank"
            rel="noopener noreferrer"
          >
            A post shared by Catlog - Sell easily on Social media 🛍️ (@catlogshop)
          </a>
        </p>
      </div>
    </blockquote>
  );
};

export default FezPartnershipAnnouncementModal;
