import React, { useEffect, useState } from "react";
import { AppBtn } from "../../../buttons";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "../../../modal";
import authContext from "@/contexts/auth-context";
import dayjs from "dayjs";
import { useRequest } from "@/api/utils";
import { GetCouriers } from "@/api/deliveries";
import { useRouter } from "next/router";
import { COUNTRIES } from "@/assets/interfaces";

const PaymentsAnnouncementModalGh: React.FC = () => {
  const router = useRouter();
  const [showModal, setShowModal] = useState(false);
  const { store } = authContext.useContainer();

  const shouldShowModal =
    showModal &&
    dayjs(store?.created_at).isBefore(dayjs().subtract(1, "weeks")) &&
    // !store?.payments_enabled &&
    dayjs().isBefore(dayjs("2023-10-20")) &&
    store.country?.code === COUNTRIES.GH;

  useEffect(() => {
    setTimeout(() => {
      const hasShown = !!localStorage.getItem("shown-payments-gh-modal");

      if (!hasShown) {
        setShowModal(true);
      }
    }, 15000);
  }, []);

  const closeModal = () => {
    setShowModal(false);
    localStorage.setItem("shown-payments-gh-modal", "true");
  };

  const goToPayments = () => {
    closeModal();
    router.push("/payments/invoices");
  };

  return (
    <Modal
      {...{ show: shouldShowModal, toggle: closeModal }}
      title=""
      size="midi"
      className="z-[1000]"
      showHeader={false}
    >
      <ModalBody noPadding className="rounded-t-10 overflow-hidden">
        <figure className="w-full sticky top-0">
          <img
            src="/images/payments-banner-gh.png"
            alt="Banner Announcing Catlog Payments In Ghana"
            className="w-full"
          />
        </figure>
        <div className="p-5 sm:p-6.25 flex-1 overflow-auto">
          <p className="text-black-secondary">
            Agoo! Exciting news!! We've made it really easy to collect payments.
            <br />
            <br />
            You can now effortlessly accept MoMo and card payments, both on your storefront and through invoice links.
            Enjoy the convenience and simplicity of collecting payments on Catlog!
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock onClick={goToPayments} size="lg">
          Enable Payments
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default PaymentsAnnouncementModalGh;
