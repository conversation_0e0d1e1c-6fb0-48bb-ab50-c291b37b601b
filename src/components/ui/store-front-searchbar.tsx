import { createBrowserHistory } from "history";
import React from "react";

interface Props {
  fullViewOnMobile: boolean;
  setFullViewOnMobile: (state: boolean) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  placeholder?: string;
}

const StoreFrontSearchBar: React.FC<Props> = ({
  fullViewOnMobile,
  setFullViewOnMobile,
  searchQuery,
  setSearchQuery,
  placeholder = "Search items",
}) => {
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    const history = createBrowserHistory();
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set("search", searchQuery);

    history.push({
      pathname: location.pathname,
      search: `?${searchParams.toString()}`,
    });

    setFullViewOnMobile(false);
  };

  return (
    <form className="flex relative flex-1 justify-end md:flex-none" onSubmit={handleSearch}>
      <input
        className={`h-10 border border-primary-100 bg-primary-50 md:w-[240px] lg:w-[280px] rounded-lg text-sm no-outline text-dark flex items-center ring-0 focus:ring-2 focus:ring-purple-100 transition-all ease-in-out placeholder-placeholder pt-1 leading-3 overflow-hidden ${
          fullViewOnMobile ? "w-full px-2.5 border opacity-1" : "w-0 px-0 border-0 md:border md:px-2.5"
        }`}
        placeholder={placeholder}
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />
      <button
        className={`items-center justify-center h-7.5 w-7.5 rounded-[8px] bg-grey-input absolute top-1.25 right-1.5 text-dark transition-all ease-in-out hover:bg-primary-300 duration-100 hover:text-white ${
          fullViewOnMobile ? "flex" : "hidden md:flex"
        }`}
      >
        {/* prettier-ignore */}
        <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
          <path d="M6.875 11.875C9.63642 11.875 11.875 9.63642 11.875 6.875C11.875 4.11358 9.63642 1.875 6.875 1.875C4.11358 1.875 1.875 4.11358 1.875 6.875C1.875 9.63642 4.11358 11.875 6.875 11.875Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M13.125 13.125L10.4062 10.4062" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      </button>
      <button
        className={`items-center justify-center h-9 w-9 rounded-[8px] bg-grey-input text-dark transition-all ease-in-out hover:bg-primary-300 duration-100 hover:text-white ${
          fullViewOnMobile ? "hidden" : "flex md:hidden"
        }`}
        onClick={() => setFullViewOnMobile(true)}
        type="button"
      >
        {/* prettier-ignore */}
        <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
          <path d="M6.875 11.875C9.63642 11.875 11.875 9.63642 11.875 6.875C11.875 4.11358 9.63642 1.875 6.875 1.875C4.11358 1.875 1.875 4.11358 1.875 6.875C1.875 9.63642 4.11358 11.875 6.875 11.875Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M13.125 13.125L10.4062 10.4062" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      </button>
    </form>
  );
};

export default StoreFrontSearchBar;
