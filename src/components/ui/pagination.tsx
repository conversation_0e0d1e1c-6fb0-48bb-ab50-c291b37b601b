import { create<PERSON><PERSON>erHistory } from "history";
import React, { useEffect, useState } from "react";
import useSearchParams from "../hooks/useSearchParams";
import { RoundActionBtn } from "./buttons";
import Dropdown, { DropdownItem } from "./dropdown-new";

interface PaginationProps {
  data: {
    prev_page?: number;
    next_page?: boolean;
    total: number;
    total_pages?: number;
  };
  currentPage: number;
  length: number;
  per_page: number;
  label: string;
  goNext: VoidFunction;
  goPrevious: VoidFunction;
  setPage: (page: number) => void;
  setPerPage: (perPage: number) => void;
  storageKey?: string; // Optional key for localStorage
}

const storageKey = "pagination_per_page";
const Pagination: React.FC<PaginationProps> = (props) => {
  const {
    currentPage,
    per_page,
    length,
    label,
    goNext,
    setPage,
    goPrevious,
    setPerPage,
    storageKey = "pagination_per_page",

    data: { total, prev_page, next_page, total_pages },
  } = props;

  const handlePerPageChange = (newPerPage: number) => {
    // setLocalPerPage(newPerPage);
    // localStorage.setItem(storageKey, newPerPage.toString());
    // setPerPage(newPerPage);
  };

  const perPageOptions: DropdownItem[] = [
    { text: "10 per page", onClick: () => handlePerPageChange(10) },
    { text: "25 per page", onClick: () => handlePerPageChange(25) },
    { text: "50 per page", onClick: () => handlePerPageChange(50) },
    { text: "75 per page", onClick: () => handlePerPageChange(75) },
    { text: "100 per page", onClick: () => handlePerPageChange(100) },
  ];

  const getPages = (maxVisbilePages: number) => {
    const isFirstSection = currentPage <= maxVisbilePages - 1;
    const isMidSection = currentPage > maxVisbilePages - 1 && currentPage <= total_pages - (maxVisbilePages - 1);
    const isLastSection = currentPage > total_pages - (maxVisbilePages - 1);
    const isMany = total_pages > maxVisbilePages + 1;
    const pages = [{ text: "1", value: 1, isActive: currentPage === 1, isPage: true }];

    const addPage = (text: string, value: number, isActive: boolean, isPage: boolean) =>
      pages.push({ text, value, isActive, isPage });
    const addElipses = () => addPage("...", null, false, false);

    if (isMany)
      for (let i = 1; i < maxVisbilePages - 1; i++) {
        const isLastLoop = i === maxVisbilePages - 2;
        if (isFirstSection) {
          const value = i + 1;
          addPage(value.toString(), value, currentPage === i + 1, true);
          if (isLastLoop) addElipses();
        } else if (isMidSection) {
          if (i === 1) addElipses();
          const value = currentPage - 2 + i;
          addPage(value.toString(), value, currentPage === value, true);
          if (isLastLoop) addElipses();
        } else if (isLastSection) {
          if (i === 1) addElipses();
          const value = total_pages - (maxVisbilePages - (i + 1));
          addPage(value.toString(), value, currentPage === value, true);
        }
        if (isLastLoop) addPage(total_pages.toString(), total_pages, currentPage === total_pages, true);
      }
    else
      for (let i = 2; i <= total_pages; i++) {
        addPage(i.toString(), i, currentPage === i, true);
      }
    return pages;
  };

  return (
    <div className="mb-12.5">
      <div className="mt-2.5 flex sm:items-center justify-between flex-wrap flex-col sm:flex-row">
        <div className="flex items-center gap-2.5 mt-2.5">
          <div className="text-sm text-dark inline-block flex-shrink-0">
            Showing{" "}
            <b className="text-black-secondary font-semibold">
              {per_page * (currentPage - 1) + 1} - {per_page * (currentPage - 1) + length}
            </b>{" "}
            of <b className="text-black-secondary font-semibold">{total}</b> {label}
          </div>
          {/* Per Page Dropdown */}
          <div className="flex items-center gap-2 pl-2.5 border-l border-grey-border">
            {/* <span className="text-sm text-dark">Show:</span> */}
            <Dropdown items={perPageOptions} size="sm" className="min-w-[120px]">
              {(show) => (
                <div className="dropdown-toggle inline-flex items-center gap-2 px-3 py-1.5 text-sm border border-grey-divider rounded-md hover:bg-gray-50 cursor-pointer bg-grey-fields-100">
                  <span className="text-black-secondary text-xs">{per_page}</span>
                  <svg
                    className={`w-4 h-4 text-black-muted transition-transform duration-200 ${
                      show ? "rotate-120" : "rotate-0"
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              )}
            </Dropdown>
          </div>
        </div>
        <div className="flex items-center gap-4 mt-2.5 sm:mt-0">
          {total_pages > 1 && (
            <div className="hidden sm:flex">
              <ActionBtns
                prev_page={prev_page}
                next_page={next_page}
                goNext={goNext}
                goPrevious={goPrevious}
                setPage={setPage}
                getPages={getPages}
              />
            </div>
          )}
        </div>
      </div>
      <div className="mt-2.5 flex sm:hidden justify-end">
        <ActionBtns
          prev_page={prev_page}
          next_page={next_page}
          goNext={goNext}
          goPrevious={goPrevious}
          setPage={setPage}
          getPages={getPages}
        />
      </div>
    </div>
  );
};

interface ActionBtnsProps {
  prev_page: number;
  next_page: boolean;
  goNext: VoidFunction;
  goPrevious: VoidFunction;
  setPage: (page: number) => void;
  getPages: (maxVisbilePages: number) => { text: string; value: number; isActive: boolean; isPage: boolean }[];
}

const ActionBtns: React.FC<ActionBtnsProps> = (props) => {
  const { prev_page, next_page, goNext, goPrevious, setPage, getPages } = props;

  return (
    <div className="flex gap-2.5 items-center mt-2.5 ml-auto">
      {prev_page && (
        <RoundActionBtn size="sm" onClick={goPrevious}>
          {/* prettier-ignore */}
          <svg width="14" viewBox="0 0 15 14" fill="none">
        <path d="M13.5 7H1.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
        <path d="M7.5 1L1.5 7L7.5 13" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
      </svg>
        </RoundActionBtn>
      )}
      {getPages(5).map(({ value, isActive, isPage, text }, idx) =>
        isPage ? (
          <button
            key={idx}
            onClick={() => setPage(value)}
            className={`text-sm h-8 w-8 bg-grey-fields-200 rounded-full flex items-center justify-center ${
              isActive ? "w-8 bg-primary-500 text-white font-semibold block" : "text-black-muted font-medium"
            }`}
            disabled={isActive || !isPage}
          >
            {text}
          </button>
        ) : (
          <span className="h-8" key={idx}>
            {text}
          </span>
        )
      )}
      {next_page && (
        <RoundActionBtn onClick={goNext} size="sm">
          {/* prettier-ignore */}
          <svg width="14" viewBox="0 0 15 14" fill="none" >
        <path d="M1.5 7L13.5 7M13.5 7L7.5 13M13.5 7L7.5 1" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
        </RoundActionBtn>
      )}
    </div>
  );
};

export default Pagination;
