import React from "react";
import { InfoCircle } from "iconsax-react";
import cx from "classnames";

interface Props {
  message: string;
  className?: string;
}

const InfoCard: React.FC<Props> = ({ message, className }) => {
  return (
    <div className={cx("flex items-center bg-grey-fields-100 rounded-10 px-2.5 py-2 space-x-2", className)}>
      <figure className="h-8 w-8 rounded-full bg-white text-accent-yellow-500 flex items-center justify-center flex-shrink-0">
        <InfoCircle size={20} variant="Bold" />
      </figure>
      <span className="inline-block text-1xs font-medium text-dark">{message}</span>
    </div>
  );
};

export default InfoCard;
