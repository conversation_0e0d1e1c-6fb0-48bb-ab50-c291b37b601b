import classNames from "classnames";
import router from "next/router";
import React, { useEffect } from "react";
import { useModals } from "../../hooks/useModals";
import WalletDropdown from "./wallet-dropdown";
import useTabs from "@/components/hooks/useTabs";
import Wallet from "./wallet";
import CreditsAndReferrals from "./credits-and-referral";
import WalletContext from "@/contexts/wallet-context";
import CatlogCreditsDropdown from "./catlog-credits-dropdown";

interface IProps {}

const WalletWrapper: React.FC<IProps> = ({}) => {
  const { getWalletsReq, canOwnWallet, getCreditsReq } = WalletContext.useContainer();
  const { tabs, switchTab, switchByKey, acitveKey, active, activeValue } = useTabs(["Wallet", "Catlog Credits"], 0);

  const { response, error, isLoading, makeRequest } = getWalletsReq;
  const { modals, toggleModal } = useModals([
    "history",
    "make_withdrawal",
    "make_conversion",
    "referred_users",
    "credits_history",
    "catlog_credits",
    "credits_to_cash",
    "bank_accounts",
    "international_payments",
    "rates",
  ]);

  const showWalletOptions = !(isLoading || (error && error.statusCode > 499));
  const showCreditsOptions = !(
    getCreditsReq?.isLoading ||
    (getCreditsReq?.error && getCreditsReq?.error.statusCode > 499)
  );

  useEffect(() => {
    if (!canOwnWallet) {
      switchByKey("catlog_credits");
    }
  }, [canOwnWallet]);

  return (
    <div className="w-full bg-grey-fields-100 px-5 py-4.5 lg:py-7.5">
      <div>
        <div className="flex items-center justify-between text-black">
          {canOwnWallet ? (
            <div className="flex items-center space-x-2.5">
              {tabs.map((t, i) => (
                <WalletTabButton label={t} isActive={active === i} key={i} onClick={() => switchTab(i)} />
              ))}
            </div>
          ) : (
            <span className="text-base font-bold font-display">Credit Wallet</span>
          )}
          {acitveKey === "wallet" && showWalletOptions && (
            <WalletDropdown
              // withdrawFunds={() => toggleModal("make_withdrawal")}
              showHistory={() => toggleModal("history")}
              showRates={() => toggleModal("rates")}
            />
          )}

          {acitveKey === "catlog_credits" && showCreditsOptions && (
            <CatlogCreditsDropdown
              showHistory={() => toggleModal("credits_history")}
              showCreditExplainer={() => toggleModal("catlog_credits")}
              showReferredUsers={() => toggleModal("referred_users")}
            />
          )}
        </div>
      </div>

      {canOwnWallet && (
        <div className={acitveKey === "wallet" ? "block" : "hidden"}>
          <Wallet
            {...{
              modals,
              toggleModal,
            }}
          />
        </div>
      )}
      <div className={acitveKey === "catlog_credits" ? "block" : "hidden"}>
        <CreditsAndReferrals
          {...{
            modals,
            toggleModal,
          }}
        />
      </div>
    </div>
  );
};

interface TabButtonProps {
  isActive: boolean;
  label: string;
  onClick: VoidFunction;
  icon?: React.ReactElement;
}

export const WalletTabButton: React.FC<TabButtonProps> = (props) => {
  const { isActive, label, icon, onClick } = props;

  return (
    <button
      className={classNames(
        "flex items-center space-x-0 py-2 px-3 rounded-30 text-1xs border font-medium transition-all ease-out duration-200",
        {
          "bg-primary-500 text-white border-primary-500 shadow-pill": isActive,
          "bg-white border-grey-border border-opacity-50": !isActive,
        }
      )}
      onClick={onClick}
    >
      {icon && <div className={classNames({ "text-accent-500": !isActive })}>{icon}</div>}
      {label}
    </button>
  );
};

export default WalletWrapper;
