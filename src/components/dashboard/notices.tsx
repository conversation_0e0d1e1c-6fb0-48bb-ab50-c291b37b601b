import React, { useMemo } from "react";
import useNotice from "../hooks/useNotice";
import { COUNTRIES, Referrals, User } from "@/assets/interfaces";
import dayjs from "dayjs";
import { useModals } from "@/components/hooks/useModals";
import Portal from "../portal";
import ReferralModal from "./referral-modal";
import { toNaira } from "@/assets/js/utils/utils";
import WalletContext from "@/contexts/wallet-context";
import { GetStoreReferrals } from "@/api/credits-and-referrals";
import { useFetcher } from "@/api/utils";
import { useListenerState } from "../hooks/useListener";
import useReferrals from "../hooks/useReferrals";
import { HowToWinModal } from "@/pages/referral-leaderboard";
import Link from "next/link";
import ValDiscountsExplainerModal from "./val-discount-modal";
import { FezPartnershipExplainerModal } from "../ui/layouts/dashboard/announcement-modals/fez-partnership-announcement-modal";

interface Props {
  user: any;
  country?: COUNTRIES;
}

const ReferralNotice: React.FC<Props> = ({ user }) => {
  const { isOpen, closeNotice } = useNotice("referral-notice");
  const open = isOpen && dayjs(user?.created_at).isBefore(dayjs().subtract(3, "days"));
  const { modals, toggleModal } = useModals(["refer"]);

  const { referrals, rewards } = useReferrals();

  if (!open || !rewards?.subscription) return null;

  return (
    <>
      <div
        className="w-full px-6.25 lg:pl-7.5 py-2.5 bg-grey-fields-100 bg-opacity-50 flex items-start justify-between border-b border-grey-border border-opacity-50 cursor-pointer"
        onClick={() => toggleModal("refer")}
      >
        <div className="text-1xs text-dark font-medium mr-3.75">
          💸 Earn{" "}
          <b className="font-semibold">
            {rewards?.currency} {toNaira(rewards?.subscription)}
          </b>{" "}
          for 3 months when you refer your friends to Catlog. Learn More
        </div>
        <button
          className="no-outline"
          onClick={(e) => {
            e.stopPropagation();
            closeNotice();
          }}
        >
          {/* prettier-ignore */}
          <svg width="20" viewBox="0 0 25 25" fill="none" className="text-dark flex-shrink-0">
            <rect x="0.5" y="0.5" width="24" height="24" rx="12" fill="white" stroke="currentColor"/>
            <path d="M16.25 8.75L8.75 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.75 8.75L16.25 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
      {rewards && (
        <Portal>
          <ReferralModal
            show={modals.refer.show}
            toggle={() => toggleModal("refer")}
            rewards={rewards}
            referral_code={referrals?.referral_code}
          />
        </Portal>
      )}
    </>
  );
};

const ChallengeNotice: React.FC<Props> = ({ user }) => {
  const { isOpen, closeNotice } = useNotice("challenge-notice");
  const open = isOpen && dayjs(user?.created_at).isBefore(dayjs().subtract(3, "days"));
  const { modals, toggleModal } = useModals(["challenge"]);

  if (!open) return null;

  return (
    <>
      <div
        className="w-full px-6.25 lg:pl-7.5 py-2.5 bg-grey-fields-100 bg-opacity-50 flex items-start justify-between border-b border-grey-border border-opacity-50 cursor-pointer"
        onClick={() => toggleModal("challenge")}
      >
        <div className="text-1xs text-dark font-medium mr-3.75">
          Win <b className="font-semibold">NGN 10,000</b> in the ongoing 30-day referral challenge. Learn More
        </div>
        <button
          className="no-outline"
          onClick={(e) => {
            e.stopPropagation();
            closeNotice();
          }}
        >
          {/* prettier-ignore */}
          <svg width="20" viewBox="0 0 25 25" fill="none" className="text-dark flex-shrink-0">
            <rect x="0.5" y="0.5" width="24" height="24" rx="12" fill="white" stroke="currentColor"/>
            <path d="M16.25 8.75L8.75 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.75 8.75L16.25 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
      <Portal>
        <HowToWinModal show={modals.challenge.show} toggle={() => toggleModal("challenge")} />
      </Portal>
    </>
  );
};

const ValatineNotice: React.FC<Props> = ({ user, country }) => {
  const { isOpen, closeNotice } = useNotice("valentine-notice");
  const open = isOpen && dayjs(user?.created_at).isBefore(dayjs().subtract(3, "days"));
  const { modals, toggleModal } = useModals(["valentine"]);

  if (!open) return null;

  return (
    <>
      <div
        className="w-full px-6.25 lg:pl-7.5 py-2.5 bg-grey-fields-100 bg-opacity-50 flex items-start justify-between border-b border-grey-border border-opacity-50 cursor-pointer"
        onClick={() => toggleModal("valentine")}
      >
        <div className="text-1xs text-dark font-medium mr-3.75">
          💝 Would you be our Val? <b className="font-semibold">We have a gift for you</b>. Learn More
        </div>
        <button
          className="no-outline"
          onClick={(e) => {
            e.stopPropagation();
            closeNotice();
          }}
        >
          {/* prettier-ignore */}
          <svg width="20" viewBox="0 0 25 25" fill="none" className="text-dark flex-shrink-0">
            <rect x="0.5" y="0.5" width="24" height="24" rx="12" fill="white" stroke="currentColor"/>
            <path d="M16.25 8.75L8.75 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.75 8.75L16.25 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
      <Portal>
        <ValDiscountsExplainerModal
          country={country}
          show={modals.valentine.show}
          toggle={() => toggleModal("valentine")}
        />
      </Portal>
    </>
  );
};

const DiscountNotice: React.FC<Props> = ({ user }) => {
  const { isOpen, closeNotice } = useNotice("annual-discount-notice");
  const open = isOpen && dayjs(user?.created_at).isBefore(dayjs().subtract(3, "days"));

  if (!open) return null;

  return (
    <Link href="/my-store/change-plan">
      <div className="w-full px-6.25 lg:pl-7.5 py-2.5 bg-grey-fields-100 bg-opacity-50 flex items-start justify-between border-b border-grey-border border-opacity-50 cursor-pointer">
        <div className="text-1xs text-dark font-medium mr-3.75">
          🎁 Annual plans are here! We're giving <b className="font-semibold">15% OFF</b> on all 3, 6, and 12 months
          plans valid until May 4th.
        </div>
        <button
          className="no-outline"
          onClick={(e) => {
            e.stopPropagation();
            closeNotice();
          }}
        >
          {/* prettier-ignore */}
          <svg width="20" viewBox="0 0 25 25" fill="none" className="text-dark flex-shrink-0">
              <rect x="0.5" y="0.5" width="24" height="24" rx="12" fill="white" stroke="currentColor"/>
              <path d="M16.25 8.75L8.75 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.75 8.75L16.25 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
        </button>
      </div>
    </Link>
  );
};

const NewStorefrontNotice: React.FC<Props> = ({ user }) => {
  const { isOpen, closeNotice } = useNotice("new-storefront-notice");
  const open = isOpen && dayjs(user?.created_at).isBefore("2025-05-26");

  if (!open) return null;

  return (
    <Link href="https://blog.catlog.shop/whats-new-in-your-catlog-storefront/" target="_blank">
      <div className="w-full px-6.25 lg:pl-7.5 py-2.5 bg-grey-fields-100 bg-opacity-50 flex items-start justify-between border-b border-grey-border border-opacity-50 cursor-pointer">
        <div className="text-1xs text-dark font-medium mr-3.75">
          🎉 Your new storefront is here! See what's new in your storefront.
        </div>
        <button
          className="no-outline"
          onClick={(e) => {
            e.stopPropagation();
            closeNotice();
          }}
        >
          {/* prettier-ignore */}
          <svg width="20" viewBox="0 0 25 25" fill="none" className="text-dark flex-shrink-0">
              <rect x="0.5" y="0.5" width="24" height="24" rx="12" fill="white" stroke="currentColor"/>
              <path d="M16.25 8.75L8.75 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.75 8.75L16.25 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
        </button>
      </div>
    </Link>
  );
};

const DeliveryDiscountNotice: React.FC<Props> = ({ user, country }) => {
  const { isOpen, closeNotice } = useNotice("delivery-discount-notice");
  const open = isOpen && country === "NG";

  if (!open) return null;

  return (
    <Link href="/deliveries">
      <div className="w-full px-6.25 lg:pl-7.5 py-2.5 bg-grey-fields-100 bg-opacity-50 flex items-start justify-between border-b border-grey-border border-opacity-50 cursor-pointer">
        <div className="text-1xs text-dark font-medium mr-3.75">
          🚚 Book your next delivery with Catlog and save <b className="font-semibold">NGN 500</b>. Use the code{" "}
          <b className="font-semibold">SAVE500</b>
        </div>
        <button
          className="no-outline"
          onClick={(e) => {
            e.stopPropagation();
            closeNotice();
          }}
        >
          {/* prettier-ignore */}
          <svg width="20" viewBox="0 0 25 25" fill="none" className="text-dark flex-shrink-0">
              <rect x="0.5" y="0.5" width="24" height="24" rx="12" fill="white" stroke="currentColor"/>
              <path d="M16.25 8.75L8.75 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.75 8.75L16.25 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
        </button>
      </div>
    </Link>
  );
};

const QuarterlyDiscountNotice = () => {
  const { isOpen, closeNotice } = useNotice("quarterly-discount-notice");

  if (!open) return null;

  return (
    <Link href="/my-store/change-plan">
      <div className="w-full px-6.25 lg:pl-7.5 py-2.5 bg-grey-fields-100 bg-opacity-50 flex items-start justify-between border-b border-grey-border border-opacity-50 cursor-pointer">
        <div className="text-1xs text-dark font-medium mr-3.75">
          🎁 Ready for sales this ember season? Get <b className="font-semibold">15% OFF</b> on any{" "}
          <b className="font-semibold">3 or 6-month</b> plan. Offer ends 8th Sept.
        </div>
        <button
          className="no-outline"
          onClick={(e) => {
            e.stopPropagation();
            closeNotice();
          }}
        >
          {/* prettier-ignore */}
          <svg width="20" viewBox="0 0 25 25" fill="none" className="text-dark flex-shrink-0">
              <rect x="0.5" y="0.5" width="24" height="24" rx="12" fill="white" stroke="currentColor"/>
              <path d="M16.25 8.75L8.75 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.75 8.75L16.25 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
        </button>
      </div>
    </Link>
  );
};

const PaymentLinksNotice = () => {
  const { isOpen, closeNotice } = useNotice("payment-link-notice");

  if (!open) return null;

  return (
    <Link href="/payments/payment-links">
      <div className="w-full px-6.25 lg:pl-7.5 py-2.5 bg-grey-fields-100 bg-opacity-50 flex items-start justify-between border-b border-grey-border border-opacity-50 cursor-pointer">
        <div className="text-1xs text-dark font-medium mr-3.75">
          💸 You can now get paid with payment links. <b className="font-semibold">Try it out</b>
        </div>
        <button
          className="no-outline"
          onClick={(e) => {
            e.stopPropagation();
            closeNotice();
          }}
        >
          {/* prettier-ignore */}
          <svg width="20" viewBox="0 0 25 25" fill="none" className="text-dark flex-shrink-0">
            <rect x="0.5" y="0.5" width="24" height="24" rx="12" fill="white" stroke="currentColor"/>
            <path d="M16.25 8.75L8.75 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.75 8.75L16.25 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
    </Link>
  );
};

const PriceChangeNotice = () => {
  const { isOpen, closeNotice } = useNotice("quarterly-discount-notice");

  if (!open) return null;

  return (
    <Link href="/my-store/change-plan">
      <div className="w-full px-6.25 lg:pl-7.5 py-2.5 bg-grey-fields-100 bg-opacity-50 flex items-start justify-between border-b border-grey-border border-opacity-50 cursor-pointer">
        <div className="text-1xs text-dark font-medium mr-3.75">
          🎁 Our prices are changing on Dec 3! Subscribe to our <b className="font-semibold">3 or 6-month</b> plans now
          and get <b className="font-semibold">15% OFF</b> off to lock in current prices.
        </div>
        <button
          className="no-outline"
          onClick={(e) => {
            e.stopPropagation();
            closeNotice();
          }}
        >
          {/* prettier-ignore */}
          <svg width="20" viewBox="0 0 25 25" fill="none" className="text-dark flex-shrink-0">
            <rect x="0.5" y="0.5" width="24" height="24" rx="12" fill="white" stroke="currentColor"/>
            <path d="M16.25 8.75L8.75 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.75 8.75L16.25 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
        </button>
      </div>
    </Link>
  );
};

const YearWrapNotice = () => {
  const { isOpen, closeNotice } = useNotice("year-wrap-notice-new");

  if (!isOpen) return null;

  return (
    <div
      className="w-full px-6.25 lg:pl-7.5 py-2.5 bg-grey-fields-100 bg-opacity-50 flex items-start justify-between border-b border-grey-border border-opacity-50 cursor-pointer"
      onClick={() => window.open("/wrapped", "_blank")}
    >
      <div className="text-1xs text-dark font-medium mr-3.75">
        🎉 Your 2024 Year Wrap is here! Click here to see how your business journeyed through 2024!
      </div>
      <button
        className="no-outline"
        onClick={(e) => {
          e.stopPropagation();
          closeNotice();
        }}
      >
        {/* prettier-ignore */}
        <svg width="20" viewBox="0 0 25 25" fill="none" className="text-dark flex-shrink-0">
            <rect x="0.5" y="0.5" width="24" height="24" rx="12" fill="white" stroke="currentColor"/>
            <path d="M16.25 8.75L8.75 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.75 8.75L16.25 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
      </button>
    </div>
  );
};

const FezPartnershipNotice = () => {
  const { isOpen, closeNotice } = useNotice("fez-partnership-notice");
  const { modals, toggleModal } = useModals(["fez_explainer"]);

  if (!open) return null;

  return (
    <>
      {/* <Link href="/my-store/change-plan"> */}
      <div
        className="w-full px-6.25 lg:pl-7.5 py-2.5 bg-grey-fields-100 bg-opacity-50 flex items-start justify-between border-b border-grey-border border-opacity-50 cursor-pointer"
        onClick={() => toggleModal("fez_explainer")}
      >
        <div className="text-1xs text-dark font-medium mr-3.75">
          🎁 To take advantage of the Fez discount initiate a delivery from the order page. Please note that only orders
          paid for via your storefront are valid for this discount! <b className="font-semibold">Learn More</b>
        </div>
        <button
          className="no-outline"
          onClick={(e) => {
            e.stopPropagation();
            closeNotice();
          }}
        >
          {/* prettier-ignore */}
          <svg width="20" viewBox="0 0 25 25" fill="none" className="text-dark flex-shrink-0">
              <rect x="0.5" y="0.5" width="24" height="24" rx="12" fill="white" stroke="currentColor"/>
              <path d="M16.25 8.75L8.75 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.75 8.75L16.25 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
        </button>
      </div>
      {/* </Link> */}
      <FezPartnershipExplainerModal show={modals.fez_explainer.show} toggle={() => toggleModal("fez_explainer")} />
    </>
  );
};

const CustomDomainNotice: React.FC<Props> = ({ user }) => {
  const { isOpen, closeNotice } = useNotice("custom-domain-notice");
  const open = isOpen && dayjs(user?.created_at).isBefore("2025-07-05");

  if (!open) return null;

  return (
    <Link href="/my-store/configuration?tab=custom_domains" target="_blank">
      <div className="w-full px-6.25 lg:pl-7.5 py-2.5 bg-grey-fields-100 bg-opacity-50 flex items-start justify-between border-b border-grey-border border-opacity-50 cursor-pointer">
        <div className="text-1xs text-dark font-medium mr-3.75">
          🎉 You can now buy or connect an existing domain to your store. <b className="font-semibold">Learn More</b>
        </div>
        <button
          className="no-outline"
          onClick={(e) => {
            e.stopPropagation();
            closeNotice();
          }}
        >
          {/* prettier-ignore */}
          <svg width="20" viewBox="0 0 25 25" fill="none" className="text-dark flex-shrink-0">
              <rect x="0.5" y="0.5" width="24" height="24" rx="12" fill="white" stroke="currentColor"/>
              <path d="M16.25 8.75L8.75 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.75 8.75L16.25 16.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
        </button>
      </div>
    </Link>
  );
};

export {
  ChallengeNotice,
  ReferralNotice,
  DiscountNotice,
  ValatineNotice,
  DeliveryDiscountNotice,
  QuarterlyDiscountNotice,
  PaymentLinksNotice,
  PriceChangeNotice,
  FezPartnershipNotice,
  YearWrapNotice,
  NewStorefrontNotice,
  CustomDomainNotice,
};
