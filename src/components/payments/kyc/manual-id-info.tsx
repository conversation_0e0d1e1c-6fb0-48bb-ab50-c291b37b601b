import { useFormik } from "formik";
import React, { useEffect, useState } from "react";
import { ManuallyVerifyID, SetKycAsManual, VerifyID } from "../../../api/store.kyc";
import { useRequest } from "../../../api/utils";
import { getFieldvalues } from "../../../assets/js/utils/functions";
import { useModals } from "../../hooks/useModals";
import { AppBtn } from "../../ui/buttons";
import { InputField, SelectDropdown } from "../../ui/form-elements";
import { TierBadge } from "./commons";
import IDUploadWidget from "./id-upload-widget";
import * as Yup from "yup";
import { COUNTRIES, ID_TYPES, KYCInfo, Tier } from "../../../assets/interfaces";
import ErrorLabel from "../../ui/error-label";
import Link from "next/link";
import Router from "next/router";
import { toast } from "../../ui/toast";
import { VERIFICATION_METHODS } from "@/assets/js/utils/constants";
import { CountryIDMethodOptions } from "./id-info";

interface IProps {
  isActive: boolean;
  kycInfo: KYCInfo;
  next: () => void;
  setKycInfo: (kycInfo: KYCInfo) => void;
}

const ManualKYCIDInfo: React.FC<IProps> = ({ kycInfo, next, setKycInfo }) => {
  const { modals, toggleModal } = useModals(["id_upload"]);
  const verifyIDReq = useRequest(ManuallyVerifyID);
  const [startVerification, setStartVerification] = useState(false);
  const [idIsVerified, setIdIsVerified] = useState(false);

  const { error, response } = verifyIDReq;

  const form = useFormik({
    initialValues: {
      id_type: kycInfo?.identity?.type ?? "",
      id_number: kycInfo?.identity?.number ?? "",
      selfie: "",
      photo_id: kycInfo?.identity?.url ?? "",
      filename: kycInfo?.identity?.filename ?? "",
    },
    onSubmit: async (values) => {
      if (!modals.id_upload.show) {
        toggleModal("id_upload");
      } else {
        const [res, err] = await verifyIDReq.makeRequest(values);

        if (res) {
          handleSuccess(res?.data);
        }
      }
    },
    validationSchema: validationSchema(modals.id_upload.show),
  });

  useEffect(() => {
    if (kycInfo?.identity?.verified_at) {
      setIdIsVerified(true);
    }
  }, [kycInfo]);

  const handleSuccess = (data: KYCInfo) => {
    setKycInfo(data);
    setIdIsVerified(true);
  };

  // Get ID options based on country
  const getIDOptions = () => {
    const country = kycInfo?.country || COUNTRIES.NG;
    const countryMethods = CountryIDMethodOptions[country] || [ID_TYPES.NIN];

    return countryMethods.map((method) => {
      const option = IDMethodsOptions.find((opt) => opt.value === method);
      return option || IDMethodsOptions[0]; // Default to first option if not found
    });
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="w-full max-w-[550px] mx-auto py-12.5 sm:pt-16 lg:pt-20 text-center !pb-48">
        {/* <TierBadge className="text-accent-yellow-500 bg-accent-yellow-500" tier={Tier.TIER_2} /> */}
        <figure className="h-[70px] w-[70px] sm:h-20 sm:w-20 bg-accent-green-500 flex items-center justify-center rounded-full mb-3.75 mx-auto">
          {/* prettier-ignore */}
          <svg className="w-7.5 sm:w-9 text-white" viewBox="0 0 24 24" fill="none">
            <path d="M18.5002 4.11031L13.5102 2.24031C12.6802 1.93031 11.3202 1.93031 10.4902 2.24031L5.50016 4.11031C4.35016 4.54031 3.41016 5.90031 3.41016 7.12031V14.5503C3.41016 15.7303 4.19016 17.2803 5.14016 17.9903L9.44016 21.2003C10.8502 22.2603 13.1702 22.2603 14.5802 21.2003L18.8802 17.9903C19.8302 17.2803 20.6102 15.7303 20.6102 14.5503V7.12031C20.5902 5.90031 19.6502 4.54031 18.5002 4.11031ZM11.9302 7.03031C13.1102 7.03031 14.0702 7.99031 14.0702 9.17031C14.0702 10.3303 13.1602 11.2603 12.0102 11.3003H11.9902H11.9702C11.9502 11.3003 11.9302 11.3003 11.9102 11.3003C10.7102 11.2603 9.81016 10.3303 9.81016 9.17031C9.80016 7.99031 10.7602 7.03031 11.9302 7.03031ZM14.1902 16.3603C13.5802 16.7603 12.7902 16.9703 12.0002 16.9703C11.2102 16.9703 10.4102 16.7703 9.81016 16.3603C9.24016 15.9803 8.93016 15.4603 8.92016 14.8903C8.92016 14.3303 9.24016 13.7903 9.81016 13.4103C11.0202 12.6103 12.9902 12.6103 14.2002 13.4103C14.7702 13.7903 15.0902 14.3103 15.0902 14.8803C15.0802 15.4403 14.7602 15.9803 14.1902 16.3603Z" fill="currentColor"/>
          </svg>
        </figure>
        <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold">Share an ID Card</h2>
        <p className="text-sm text-dark mt-1.25 max-w-[250px] mx-auto">Any government recognized ID.</p>
        <form onSubmit={form.handleSubmit} className="max-w-[450px] mx-auto mt-6 sm:mt-7.5">
          <ErrorLabel
            error={
              Array.isArray((error as any)?.errors)
                ? (error as any)?.errors[0] || "Something went wrong, please try again"
                : null
            }
            perm
          />
          <SelectDropdown
            label="Select ID Type"
            {...getFieldvalues("id_type", form)}
            options={getIDOptions()}
            disabled={idIsVerified}
          />
          <InputField
            label="ID Number"
            {...getFieldvalues("id_number", form)}
            onChange={(e) => form.setFieldValue("id_number", e.target.value.replace(/[^a-zA-Z0-9 ]/g, ""))}
            disabled={idIsVerified}
          />

          <button
            className="w-full bg-grey-fields-100 rounded-xl text-dark text-1xs sm:text-sm mt-3.75 py-3 sm:py-3.5 font-medium flex items-center justify-center"
            type="submit"
          >
            {(!idIsVerified || !form.values.filename) && (
              <>
                {/* prettier-ignore */}
                <svg className="w-5 mr-2.5 text-dark" viewBox="0 0 24 24" fill="none">
                  <path d="M16.44 8.90002C20.04 9.21002 21.51 11.06 21.51 15.11V15.24C21.51 19.71 19.72 21.5 15.25 21.5H8.73998C4.26998 21.5 2.47998 19.71 2.47998 15.24V15.11C2.47998 11.09 3.92998 9.24002 7.46998 8.91002" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 15V3.62" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M15.35 5.85L12 2.5L8.65002 5.85" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="inline-block">Upload ID</span>
              </>
            )}

            {idIsVerified && form.values.filename && (
              <>
                {/* prettier-ignore */}
                <svg className="w-4 mr-2 animate-ping-once"viewBox="0 0 18 18" fill="none">
                  <rect width="18" height="18" rx="9" fill="#39B588"/>
                  <path d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z" fill="white"/>
                </svg>

                <span className="inline-block">{form?.values?.filename}</span>
              </>
            )}
          </button>
          <AppBtn isBlock size="lg" className="mt-6 sm:mt-7.5" disabled={!idIsVerified} onClick={next}>
            Next
          </AppBtn>
        </form>
      </div>
      <IDUploadWidget
        show={modals.id_upload.show}
        toggle={() => toggleModal("id_upload")}
        form={form}
        verifyIDReq={verifyIDReq}
        handleSuccess={handleSuccess}
      />
    </div>
  );
};

const validationSchema = (startVerfication: boolean) =>
  Yup.object().shape({
    id_type: Yup.string().required("ID type is required"),
    id_number: Yup.string().when("id_type", {
      is: (val) => val === ID_TYPES.NIN || val === ID_TYPES.GHANA_CARD,
      then: Yup.string().required("ID number is required").length(11, "ID number should be 11 characters"),
      otherwise: Yup.string().required("ID number is required"),
    }),
    selfie: startVerfication ? Yup.string().required("Selfie image is required") : undefined,
    photo_id: startVerfication ? Yup.string().required("ID image is required") : undefined,
    filename: startVerfication ? Yup.string().required("ID filename is required") : undefined,
  });

// Map of all ID options for reference
const IDMethodsOptions = [
  {
    value: VERIFICATION_METHODS.NIN,
    text: "National Identity Card/Slip",
  },
  {
    value: VERIFICATION_METHODS.GHANA_CARD,
    text: "Ghana Card",
  },
  {
    value: VERIFICATION_METHODS.DRIVERS_LINCENSE,
    text: "Drivers License",
  },
  {
    value: VERIFICATION_METHODS.INTERNATIONAL_PASSPORT,
    text: "International Passport",
  },
  {
    value: VERIFICATION_METHODS.VOTERS_CARD,
    text: "Voters ID",
  },
  {
    value: VERIFICATION_METHODS.SSNIT_ID,
    text: "Social Security National ID",
  },
  {
    value: VERIFICATION_METHODS.NATIONAL_ID,
    text: "National ID Card",
  },
];

export default ManualKYCIDInfo;
