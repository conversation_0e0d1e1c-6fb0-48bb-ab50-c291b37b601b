import { useFormik } from "formik";
import React, { useEffect, useState } from "react";
import { SetKycAsManual, VerifyID } from "../../../api/store.kyc";
import { useRequest } from "../../../api/utils";
import { getFieldvalues } from "../../../assets/js/utils/functions";
import { useModals } from "../../hooks/useModals";
import { AppBtn } from "../../ui/buttons";
import { InputField, SelectDropdown } from "../../ui/form-elements";
import { TierBadge } from "./commons";
import IDUploadWidget from "./id-upload-widget";
import * as Yup from "yup";
import { COUNTRIES, ID_TYPES, KYCInfo, Tier } from "../../../assets/interfaces";
import ErrorLabel from "../../ui/error-label";
import Link from "next/link";
import Router from "next/router";
import { toast } from "../../ui/toast";
import { VERIFICATION_METHODS } from "@/assets/js/utils/constants";

interface IProps {
  isActive: boolean;
  kycInfo: KYCInfo;
  next: () => void;
  setKycInfo: (kycInfo: KYCInfo) => void;
}

const KYCIDInfo: React.FC<IProps> = ({ kycInfo, next, setKycInfo }) => {
  const { modals, toggleModal } = useModals(["id_upload"]);
  const verifyIDReq = useRequest(VerifyID);
  const updateKycToManual = useRequest(SetKycAsManual);
  const [startVerification, setStartVerification] = useState(false);
  const [idIsVerified, setIdIsVerified] = useState(false);

  const { error, response } = verifyIDReq;

  const form = useFormik({
    initialValues: {
      id_type: kycInfo?.identity?.type ?? "",
      id_number: kycInfo?.identity?.number ?? "",
      selfie: "",
      photo_id: kycInfo?.identity?.url ?? "",
      filename: kycInfo?.identity?.filename ?? "",
    },
    onSubmit: async (values) => {
      if (!modals.id_upload.show) {
        toggleModal("id_upload");
      } else {
        const [res, err] = await verifyIDReq.makeRequest(values);

        if (res) {
          handleSuccessfulVerification(res?.data);
        }
      }
    },
    validationSchema: validationSchema(modals.id_upload.show),
  });

  useEffect(() => {
    if (kycInfo?.identity?.verified_at) {
      setIdIsVerified(true);
    }
  }, [kycInfo]);

  // Trigger manual verification when GHANA_CARD is selected
  useEffect(() => {
    if (form.values.id_type === VERIFICATION_METHODS.GHANA_CARD) {
      // Set Ghana Card as the selected option and proceed to manual verification
      form.setFieldValue("id_type", VERIFICATION_METHODS.GHANA_CARD);
      initiateManualVerification();
    }
  }, [form.values.id_type]);

  const initiateManualVerification = async () => {
    // First save the selected ID type and number
    const idType = form.values.id_type;
    const idNumber = form.values.id_number;

    const [res, err] = await updateKycToManual.makeRequest({
      id_type: idType,
      id_number: idNumber,
    });

    if (res) {
      Router.push({
        pathname: "/payments/kyc/manual",
        query: {
          id_type: idType,
          id_number: idNumber,
        },
      });
      return Promise.resolve(res);
    }

    toast.error({
      title: "Something went wrong",
      message: "Please try again",
    });

    return Promise.reject(err);
  };

  const handleSuccessfulVerification = (data: KYCInfo) => {
    setKycInfo(data);
    setIdIsVerified(true);
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="w-full max-w-[550px] mx-auto py-12.5 sm:pt-16 lg:pt-20 text-center !pb-48">
        {/* <TierBadge className="text-accent-yellow-500 bg-accent-yellow-500" tier={Tier.TIER_2} /> */}
        <figure className="h-[70px] w-[70px] sm:h-20 sm:w-20 bg-accent-green-500 flex items-center justify-center rounded-full mb-3.75 mx-auto">
          {/* prettier-ignore */}
          <svg className="w-7.5 sm:w-9 text-white" viewBox="0 0 24 24" fill="none">
            <path d="M18.5002 4.11031L13.5102 2.24031C12.6802 1.93031 11.3202 1.93031 10.4902 2.24031L5.50016 4.11031C4.35016 4.54031 3.41016 5.90031 3.41016 7.12031V14.5503C3.41016 15.7303 4.19016 17.2803 5.14016 17.9903L9.44016 21.2003C10.8502 22.2603 13.1702 22.2603 14.5802 21.2003L18.8802 17.9903C19.8302 17.2803 20.6102 15.7303 20.6102 14.5503V7.12031C20.5902 5.90031 19.6502 4.54031 18.5002 4.11031ZM11.9302 7.03031C13.1102 7.03031 14.0702 7.99031 14.0702 9.17031C14.0702 10.3303 13.1602 11.2603 12.0102 11.3003H11.9902H11.9702C11.9502 11.3003 11.9302 11.3003 11.9102 11.3003C10.7102 11.2603 9.81016 10.3303 9.81016 9.17031C9.80016 7.99031 10.7602 7.03031 11.9302 7.03031ZM14.1902 16.3603C13.5802 16.7603 12.7902 16.9703 12.0002 16.9703C11.2102 16.9703 10.4102 16.7703 9.81016 16.3603C9.24016 15.9803 8.93016 15.4603 8.92016 14.8903C8.92016 14.3303 9.24016 13.7903 9.81016 13.4103C11.0202 12.6103 12.9902 12.6103 14.2002 13.4103C14.7702 13.7903 15.0902 14.3103 15.0902 14.8803C15.0802 15.4403 14.7602 15.9803 14.1902 16.3603Z" fill="currentColor"/>
          </svg>
        </figure>
        <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold">Share an ID Card</h2>
        <p className="text-sm text-dark mt-1.25 max-w-[250px] mx-auto">Any government recognized ID.</p>
        <form onSubmit={form.handleSubmit} className="max-w-[450px] mx-auto mt-6 sm:mt-7.5">
          <ErrorLabel
            error={
              Array.isArray((error as any)?.errors) && !updateKycToManual?.response?.data
                ? (error as any)?.errors[0] || "Something went wrong, please try again"
                : null
            }
            perm
          />
          <SelectDropdown
            label="Select ID Type"
            {...getFieldvalues("id_type", form)}
            options={getIdDropdownOptions(kycInfo?.country ?? COUNTRIES.NG) ?? []}
            disabled={idIsVerified}
          />
          <InputField
            label="ID Number"
            {...getFieldvalues("id_number", form)}
            onChange={(e) => form.setFieldValue("id_number", e.target.value.replace(/[^a-zA-Z0-9 ]/g, ""))}
            disabled={idIsVerified}
          />

          {form.values.id_type == "NIN" && !idIsVerified && (
            <>
              <div className="bg-grey-fields-100 border-grey-border -mb-1.5 mt-5 rounded-15">
                <div className="flex items-start p-2.5">
                  <figure className="h-9 w-9 rounded-full bg-white shadow-pill flex items-center justify-center flex-shrink-0">
                    {/* prettier-ignore */}
                    <svg width="60%" className="text-accent-yellow-500" viewBox="0 0 24 24" fill="none">
                      <path d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM11.25 8C11.25 7.59 11.59 7.25 12 7.25C12.41 7.25 12.75 7.59 12.75 8V13C12.75 13.41 12.41 13.75 12 13.75C11.59 13.75 11.25 13.41 11.25 13V8ZM12.92 16.38C12.87 16.51 12.8 16.61 12.71 16.71C12.61 16.8 12.5 16.87 12.38 16.92C12.26 16.97 12.13 17 12 17C11.87 17 11.74 16.97 11.62 16.92C11.5 16.87 11.39 16.8 11.29 16.71C11.2 16.61 11.13 16.51 11.08 16.38C11.03 16.26 11 16.13 11 16C11 15.87 11.03 15.74 11.08 15.62C11.13 15.5 11.2 15.39 11.29 15.29C11.39 15.2 11.5 15.13 11.62 15.08C11.86 14.98 12.14 14.98 12.38 15.08C12.5 15.13 12.61 15.2 12.71 15.29C12.8 15.39 12.87 15.5 12.92 15.62C12.97 15.74 13 15.87 13 16C13 16.13 12.97 16.26 12.92 16.38Z" fill="currentColor"/>
                    </svg>
                  </figure>
                  <div className="ml-2.5 text-dark text-1xs text-left">
                    <b className="font-semibold text-black-secondary">VNIN is a unique 16 digit code from NIMC</b>{" "}
                    <br />
                    <span className="">
                      To get your VNIN, dial <b className="font-semibold">*346*3*Your NIN*1138183#</b> on the number
                      registered with your NIN or use agent code <b className="font-semibold">1138183</b> on the NIMC
                      App
                    </span>
                  </div>
                </div>
                <a
                  className="flex items-center justify-between px-2.5 py-2 border-t border-grey-border border-opacity-50"
                  target="_blank"
                  rel="noreferrer"
                  href={`tel:*346*3*${form.values.id_number ? form.values.id_number : "YOUR_NIN"}*1138183#`}
                >
                  <div className="flex items-center justify-center">
                    <figure className="bg-white h-6 w-6 rounded-full mr-2.5 flex items-center justify-center">
                      {/* prettier-ignore */}
                      <svg width="55%" viewBox="0 0 24 24" fill="none" className="text-accent-green-500">
                        <path d="M10.4199 13.4181H13.2599L13.5799 10.5781H10.7399L10.4199 13.4181Z" fill="currentColor"/>
                        <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM18.82 10.58H15.05L14.73 13.43H18.1C18.5 13.43 18.83 13.76 18.83 14.16C18.83 14.56 18.5 14.89 18.1 14.89H14.57L14.16 18.55C14.12 18.92 13.8 19.2 13.43 19.2C13.4 19.2 13.38 19.2 13.35 19.2C12.95 19.16 12.66 18.79 12.7 18.39L13.09 14.89H10.25L9.84 18.55C9.8 18.92 9.48 19.2 9.11 19.2C9.08 19.2 9.06 19.2 9.03 19.2C8.63 19.16 8.34 18.79 8.38 18.39L8.77 14.89H5.18C4.78 14.89 4.45 14.56 4.45 14.16C4.45 13.76 4.78 13.43 5.18 13.43H8.95L9.27 10.58H5.9C5.5 10.58 5.17 10.25 5.17 9.85C5.17 9.45 5.5 9.12 5.9 9.12H9.43L9.84 5.46C9.88 5.06 10.25 4.77 10.65 4.81C11.05 4.85 11.34 5.22 11.3 5.62L10.91 9.12H13.75L14.16 5.46C14.21 5.06 14.57 4.77 14.97 4.81C15.37 4.85 15.66 5.22 15.62 5.62L15.23 9.12H18.84C19.24 9.12 19.57 9.45 19.57 9.85C19.57 10.25 19.22 10.58 18.82 10.58Z" fill="currentColor"/>
                      </svg>
                    </figure>
                    <div className="text-black-secondary text-xs">
                      *346*3*{form.values.id_number ? form.values.id_number : "YOUR_NIN"}*1138183#
                    </div>
                  </div>
                  <span className="inline-block ml-1.5 text-placeholder text-xs">Tap to dial</span>
                </a>
              </div>
              <InputField
                label="VNIN"
                {...getFieldvalues("vnin", form)}
                onChange={(e) => form.setFieldValue("vnin", e.target.value.replace(/[^a-zA-Z0-9 ]/g, ""))}
                disabled={idIsVerified}
              />
              <div className="text-1xs text-dark flex items-center justify-start mt-1 mb-1">
                Can&apos;t get VNIN?{" "}
                <button
                  className="text-primary-500 font-medium inline-flex items-center ml-0.5"
                  type="button"
                  onClick={initiateManualVerification}
                >
                  Get verified manually
                  {!updateKycToManual.isLoading ? (
                    <>
                      {/* prettier-ignore */}
                      <svg className="w-3.5 mt-0.5" viewBox="0 0 15 16" fill="none">
                        <path d="M3.96484 11.5355L11.0359 4.46446" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M11.0352 11.5355L11.0352 4.46446L3.96409 4.46445" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </>
                  ) : (
                    <>
                      <span className="spinner spinner--sm text-primary-500 inline-block ml-0.5"></span>
                    </>
                  )}
                </button>
              </div>
            </>
          )}
          <button
            className="w-full bg-grey-fields-100 rounded-xl text-dark text-1xs sm:text-sm mt-3.75 py-3 sm:py-3.5 font-medium flex items-center justify-center"
            type="submit"
          >
            {(!idIsVerified || !form.values.filename) && (
              <>
                {/* prettier-ignore */}
                <svg className="w-5 mr-2.5 text-dark" viewBox="0 0 24 24" fill="none">
                  <path d="M16.44 8.90002C20.04 9.21002 21.51 11.06 21.51 15.11V15.24C21.51 19.71 19.72 21.5 15.25 21.5H8.73998C4.26998 21.5 2.47998 19.71 2.47998 15.24V15.11C2.47998 11.09 3.92998 9.24002 7.46998 8.91002" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 15V3.62" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M15.35 5.85L12 2.5L8.65002 5.85" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="inline-block">Upload ID</span>
              </>
            )}

            {idIsVerified && form.values.filename && (
              <>
                {/* prettier-ignore */}
                <svg className="w-4 mr-2 animate-ping-once"viewBox="0 0 18 18" fill="none">
                  <rect width="18" height="18" rx="9" fill="#39B588"/>
                  <path d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z" fill="white"/>
                </svg>

                <span className="inline-block">{form?.values?.filename}</span>
              </>
            )}
          </button>
          <AppBtn isBlock size="lg" className="mt-6 sm:mt-7.5" disabled={!idIsVerified} onClick={next}>
            Next
          </AppBtn>
        </form>
      </div>
      <IDUploadWidget
        show={modals.id_upload.show}
        toggle={() => toggleModal("id_upload")}
        form={form}
        verifyIDReq={verifyIDReq}
        handleSuccess={handleSuccessfulVerification}
      />
    </div>
  );
};

const validationSchema = (startVerfication: boolean) =>
  Yup.object().shape({
    id_type: Yup.string().required("ID type is required"),
    id_number: Yup.string().when("id_type", {
      is: ID_TYPES.NIN,
      then: Yup.string().required("NIN is required").length(11, "NIN should be 11 characters"),
      otherwise: Yup.string().required("ID number is required"),
    }),
    vnin: Yup.string().when("id_type", {
      is: ID_TYPES.NIN,
      then: Yup.string().required("VNIN is required").length(16, "VNIN should be 16 characters"),
      otherwise: undefined,
    }),
    selfie: startVerfication ? Yup.string().required("Selfie image is required") : undefined,
    photo_id: startVerfication ? Yup.string().required("ID image is required") : undefined,
    filename: startVerfication ? Yup.string().required("ID filename is required") : undefined,
  });

const IDMethodsOptions = [
  {
    value: VERIFICATION_METHODS.NIN,
    text: "National Identity Card/Slip",
  },
  {
    value: VERIFICATION_METHODS.DRIVERS_LINCENSE,
    text: "Drivers License",
  },
  {
    value: VERIFICATION_METHODS.INTERNATIONAL_PASSPORT,
    text: "International Passport",
  },
  {
    value: VERIFICATION_METHODS.VOTERS_CARD,
    text: "Voters ID",
  },
  {
    value: VERIFICATION_METHODS.SSNIT_ID,
    text: "Social Security National ID",
  },
  {
    value: VERIFICATION_METHODS.NATIONAL_ID,
    text: "National ID Card",
  },
  {
    value: VERIFICATION_METHODS.GHANA_CARD,
    text: "Ghana Card",
  },
];

export const CountryIDMethodOptions = {
  [COUNTRIES.NG]: [VERIFICATION_METHODS.NIN],
  [COUNTRIES.GH]: [
    VERIFICATION_METHODS.SSNIT_ID,
    VERIFICATION_METHODS.DRIVERS_LINCENSE,
    VERIFICATION_METHODS.VOTERS_CARD,
    VERIFICATION_METHODS.INTERNATIONAL_PASSPORT,
    VERIFICATION_METHODS.GHANA_CARD,
  ],
  [COUNTRIES.KE]: [VERIFICATION_METHODS.NATIONAL_ID, VERIFICATION_METHODS.INTERNATIONAL_PASSPORT],
  [COUNTRIES.ZA]: [VERIFICATION_METHODS.NATIONAL_ID],
};

const getIdDropdownOptions = (country: COUNTRIES) => {
  return CountryIDMethodOptions[country].map((method) => {
    return IDMethodsOptions.find((option) => option.value === method);
  });
};

export default KYCIDInfo;
