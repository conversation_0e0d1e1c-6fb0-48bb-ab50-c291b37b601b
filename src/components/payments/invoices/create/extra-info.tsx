import classNames from "classnames";
import { FormikProps } from "formik";
import React, { useEffect, useRef, useState } from "react";
import { getFieldvalues, handleImageSelectionFromFile } from "../../../../assets/js/utils/functions";
import authContext from "../../../../contexts/auth-context";
import useImageUploads from "../../../hooks/useImageUploads";
import { AppBtn } from "../../../ui/buttons";
import InputField from "../../../ui/form-elements/input-field";
import StepAccordion from "../../../ui/step-accordion";
import { FILE_TYPES, Image } from "../../../../assets/interfaces";
import { InvoiceForm } from "../../../../assets/interfaces/invoices";
import LazyImage from "@/components/lazy-image";

interface ExtraProps {
  isActive: boolean;
  form: FormikProps<InvoiceForm>;
  toggleStep: () => void;
  stepComplete: boolean;
  isEditing?: boolean;
}

const InvoiceExtraInfoForm: React.FC<ExtraProps> = ({ isActive, form, toggleStep, stepComplete, isEditing }) => {
  const { store } = authContext.useContainer();
  const [image, setImage] = useState<Image>({
    url: form?.values.store_logo,
    file: null,
    isUploading: false,
    uploadProgress: 0,
    src: "",
    name: "",
    lastModified: null,
    key: null,
  });
  const submitIsEnabled = !image.isUploading;
  const imagePickerRef = useRef<HTMLInputElement>(null);
  const hasError = Boolean(form?.errors?.store_address || form?.errors.store_logo);

  useImageUploads([image], FILE_TYPES.STORES, (images: Image[]) => setImage(images[0]));
  useEffect(() => {
    if (image.url) {
      form.setFieldValue("store_logo", image.url);
    }
  }, [image]);

  const handleContainerClick = (e: React.MouseEvent) => {
    if (image.isUploading) {
      return;
    }

    imagePickerRef.current.click();
  };

  const cancelImageUpload = (e: React.MouseEvent) => {
    e.stopPropagation();
    setImage({
      url: form?.values.store_logo,
      file: null,
      isUploading: false,
      uploadProgress: 0,
      src: "",
      name: "",
      lastModified: null,
      key: null,
    });
  };

  return (
    <StepAccordion
      {...{ isActive, isFirstStepComplete: true, toggleStep, stepComplete, isOptional: true, hasError }}
      title="Extra Info"
    >
      <div>
        <div>
          <div
            className="w-full bg-grey-fields-200 border border-dashed border-grey-border py-2.5 sm:py-3 px-3 sm:px-3.75 rounded-lg flex items-center group cursor-pointer"
            onClick={handleContainerClick}
          >
            <figure className="bg-white h-10 w-10 sm:h-12.5 sm:w-12.5 rounded-lg flex items-center justify-center text-[#AAAAAA] transition-all ease-out duration-300 group-hover:text-primary-500 overflow-hidden relative">
              {(image.src || image.url) && (
                <LazyImage src={image.src || image.url} alt="Store Logo" className="w-full h-full object-cover" />
              )}
              {/* prettier-ignore */}
              {(!image.src || !image.url) && (
                <svg width="25" height="25" viewBox="0 0 25 25" fill="none">
                  <path
                    d="M22.9381 17.5206L19.6777 9.89562C19.0839 8.49979 18.1985 7.70812 17.1881 7.65604C16.1881 7.60395 15.2193 8.30187 14.4797 9.6352L12.5006 13.1873C12.0839 13.9373 11.4902 14.3852 10.8443 14.4373C10.1881 14.4998 9.53182 14.156 9.00057 13.479L8.7714 13.1873C8.03182 12.2602 7.11515 11.8123 6.17765 11.906C5.24015 11.9998 4.43807 12.6456 3.90682 13.6977L2.10474 17.2915C1.4589 18.5935 1.5214 20.104 2.28182 21.3331C3.04224 22.5623 4.36515 23.3019 5.81307 23.3019H19.1047C20.5006 23.3019 21.8027 22.604 22.5735 21.4373C23.3652 20.2706 23.4902 18.8019 22.9381 17.5206Z"
                    fill="currentColor"
                  />
                  <path
                    d="M7.26107 8.72917C9.20557 8.72917 10.7819 7.15284 10.7819 5.20833C10.7819 3.26383 9.20557 1.6875 7.26107 1.6875C5.31656 1.6875 3.74023 3.26383 3.74023 5.20833C3.74023 7.15284 5.31656 8.72917 7.26107 8.72917Z"
                    fill="currentColor"
                  />
                </svg>
              )}
            </figure>
            {image.src === "" && !image.isUploading && (
              <span className="text-dark text-sm inline-block ml-5">
                Click to {image.url ? "change" : "upload"} invoice logo (optional)
              </span>
            )}

            {image.isUploading && (
              <div className="flex flex-1 items-center ml-5 justify-between overflow-hidden">
                <div className="flex-1 text-left overflow-hidden">
                  <div className="flex w-full items-center justify-between overflow-hidden">
                    <div className="flex-1 overflow-hidden flex items-center">
                      <span className="text-dark text-sm inline-block leading-snug w-full overflow-hidden overflow-ellipsis whitespace-nowrap">
                        {image.name}
                      </span>
                    </div>
                    <span className="text-placeholder text-xs inline-block ml-2">{image.uploadProgress}%</span>
                  </div>
                  <div className="h-1.5 w-full max-w-full bg-accent-green-500 bg-opacity-[0.15] rounded-10 mt-1 inline-block flex-shrink-0">
                    <div
                      className="h-full transition-all ease-out duration-300 bg-accent-green-500 rounded-10"
                      style={{ width: `${image.uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
                <button
                  className="ml-5 h-6 w-6 border border-placeholder text-placeholder bg-white flex items-center justify-center rounded-full hover:bg-accent-red-500 hover:text-white hover:border-accent-red-500 transition-all ease-out flex-shrink-0"
                  onClick={cancelImageUpload}
                >
                  {/* prettier-ignore */}
                  <svg className="w-4" viewBox="0 0 15 15" fill="none">
                    <path d="M11.25 3.75L3.75 11.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M3.75 3.75L11.25 11.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            )}

            {image.src && !image.isUploading && (
              <div className="flex flex-1 items-center ml-5 justify-between overflow-hidden">
                <div className="flex-1 overflow-hidden flex items-center">
                  <span className="text-dark text-sm inline-block leading-snug w-full overflow-hidden overflow-ellipsis whitespace-nowrap">
                    {image.name}
                  </span>
                </div>

                {/* prettier-ignore */}
                {image.uploadProgress === 100 && ( //prettier-ignore
                  <svg viewBox="0 0 18 18" fill="none" className="ml-5 h-4.5 flex-shrink-0">
                    <rect width="18" height="18" rx="9" fill="#39B588" />
                    <path
                      d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z"
                      fill="white"
                    />
                  </svg>
                )}
              </div>
            )}
          </div>
          <input
            type="file"
            name="store_logo"
            className="hidden"
            accept="image/*"
            ref={imagePickerRef}
            onChange={(e) =>
              handleImageSelectionFromFile({
                e,
                images: image,
                saveImages: (image: Image) => setImage(image),
              })
            }
          />
        </div>
        <InputField name="addreess" label="Store Address (Optional)" {...getFieldvalues("store_address", form)} />
      </div>
      <div className={`flex items-center w-full ${isEditing ? "mt-3.5" : "mt-5"}`}>
        <AppBtn
          isBlock
          disabled={!submitIsEnabled}
          type="submit"
          size={isEditing ? "md" : "lg"}
          color={isEditing ? "neutral" : "primary"}
        >
          Next
        </AppBtn>
      </div>
    </StepAccordion>
  );
};

export default InvoiceExtraInfoForm;
