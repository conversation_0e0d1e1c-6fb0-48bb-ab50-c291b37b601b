import { GetCustomers, GetCustomersBasic } from "@/api";
import { VerifyAndCreateAddress } from "@/api/deliveries";
import { GetCustomersParams } from "@/api/interfaces";
import { VerifyAndCreateAddressParams } from "@/api/interfaces/deliveries.interface";
import { useFetcher, useRequest } from "@/api/utils";
import { Phone } from "@/assets/interfaces";
import { DeliveryForm, IDeliveryAddress } from "@/assets/interfaces/deliveries";
import { getFieldvalues, phoneObjectFromString, phoneObjectToString } from "@/assets/js/utils/functions";
import { useAppendScript } from "@/components/hooks/useAppendScript";
import useTabs from "@/components/hooks/useTabs";
import { CustomerOptionRender } from "@/components/payments/invoices/create/basic-info";
import Portal from "@/components/portal";
import { AppBtn } from "@/components/ui/buttons";
import ErrorLabel from "@/components/ui/error-label";
import { InputField, PhoneInput, SelectDropdown, SelectWithModal } from "@/components/ui/form-elements";
import InputFieldWithAutocomplete, { InputOptions } from "@/components/ui/form-elements/input-field-with-autocomplete";
import { DropdownOptionInterface } from "@/components/ui/form-elements/select-dropdown";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import Toggle from "@/components/ui/toggle";
import classNames from "classnames";
import { FormikProps } from "formik";
import { useEffect, useState } from "react";
import AddressWithAutocompleteInput from "../initiate-page/address-with-autocomplete-input";

interface AddAddressProps {
  form: FormikProps<DeliveryForm>;
  show: boolean;
  toggle: (show: boolean) => void;
  title: string;
  isPickup?: boolean;
  googleApiKey: string;
  handleSuccess: (address: IDeliveryAddress) => void;
}

export const AddAdressModal: React.FC<AddAddressProps> = (props) => {
  const { form, show, title, toggle, isPickup, googleApiKey, handleSuccess } = props;
  const [customers, setCustomers] = useState<DropdownOptionInterface[]>([]);
  const formkey = isPickup ? "pickup_address" : "dropoff_address";
  const { response, error, isLoading, makeRequest } = useFetcher<GetCustomersParams>(GetCustomersBasic, {
    per_page: Number.MAX_SAFE_INTEGER,
  });
  const createAddressRequest = useRequest<VerifyAndCreateAddressParams>(VerifyAndCreateAddress);
  const [selectedCustomer, setSelectCustomer] = useState(null);
  const hasError = Boolean(form.errors[formkey]);
  const [errorMsg, setErroMsg] = useState();
  const { tabs, switchTab, switchByKey, acitveKey, active, activeValue } = useTabs(
    ["Existing Customer", "New Customer"],
    isPickup ? 1 : 0
  );

  useEffect(() => {
    (window as any).initMap = (e) => {
      console.log(e, "Google places loaded");
    };
  }, []);

  useAppendScript(
    `https://maps.googleapis.com/maps/api/js?key=${googleApiKey}&libraries=places&callback=initMap&solution_channel=GMP_QB_addressselection_v1_cA`,
    "GOOGLE_PLACE_SCRIPT"
  );

  useEffect(() => {
    if (response?.data?.data) {
      setCustomers(
        response.data.data.map((c) => ({
          text: c.name,
          value: c.id,
          meta: { phone: c.phone, email: c.email, address: c.address },
        }))
      );
    }
  }, [response]);

  const handleToggle = (state: boolean) => {
    // form.setFieldValue(formkey, undefined);
    setSelectCustomer(null);
    toggle(state);
  };

  const handleCustomerSelect = (id: string) => {
    setSelectCustomer(id);
  };

  useEffect(() => {
    if (selectedCustomer) {
      const customer = customers.find((c) => c.value === selectedCustomer);

      form.setFieldValue(formkey, {
        address: form.values[formkey]?.address,
        name: customer.text,
        phone: phoneObjectFromString(customer.meta.phone),
        customer: selectedCustomer,
        email: customer.meta.email,
      });
    }
  }, [selectedCustomer]);

  const handleSave = async () => {
    setErroMsg(null);

    const [res, error] = await createAddressRequest.makeRequest({
      ...form.values[formkey],
      phone: phoneObjectToString(form.values[formkey]?.phone as Phone),
    });

    if (res) {
      handleSuccess(res?.data);
      setSelectCustomer(null);
      toggle(false);
      return;
    }

    setErroMsg(error?.error ?? error?.message ?? "Something went wrong!");
  };

  return (
    <Portal>
      <Modal show={show} toggle={handleToggle} title={title} size="midi">
        <ModalBody className="relative !pt-0 p-5 sm:p-7.5" noPadding>
          <div className="pt-7.5">
            <ErrorLabel error={errorMsg} />
            <AddressWithAutocompleteInput form={form} formKey={`${formkey}.address`} />
            <div className="my-3.75 h-1 border-t border-grey-divider"></div>
            {!isPickup && (
              <div className="flex items-center space-x-2.5">
                {tabs.map((t, i) => (
                  <TabButton label={t} isActive={active === i} key={i} onClick={() => switchTab(i)} />
                ))}
              </div>
            )}
            {acitveKey === "existing_customer" && (
              <SelectWithModal
                OptionRender={CustomerOptionRender}
                label="Select Customer"
                options={customers}
                value={selectedCustomer}
                onChange={(e) => handleCustomerSelect(e.target.value)}
                isLoadingData={isLoading}
              />
            )}

            {acitveKey === "new_customer" && (
              <>
                <InputField label="Full Name" {...getFieldvalues(`${formkey}.name`, form)} />
                <PhoneInput label="Phone" {...getFieldvalues(`${formkey}.phone`, form)} />
                <InputField label="Email" {...getFieldvalues(`${formkey}.email`, form)} />
                {!isPickup && (
                  <div className="flex items-center gap-2.5 md:border-r border-grey-outline mt-5">
                    <Toggle
                      intialState={form.values[formkey]?.save_as_customer}
                      onChange={(state) => form.setFieldValue(`${formkey}.save_as_customer`, state)}
                    />
                    <span className="text-dark text-sm hidden sm:inline">Save as Customer</span>
                  </div>
                )}
              </>
            )}
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="w-full">
            <AppBtn
              disabled={createAddressRequest.isLoading || hasError}
              onClick={() => handleSave()}
              isBlock
              size="lg"
            >
              {createAddressRequest.isLoading ? "Saving Address..." : "Save Address"}
            </AppBtn>
          </div>
        </ModalFooter>
      </Modal>
    </Portal>
  );
};

interface TabButtonProps {
  isActive: boolean;
  label: string;
  onClick: VoidFunction;
  icon?: React.ReactElement;
}

export const TabButton: React.FC<TabButtonProps> = (props) => {
  const { isActive, label, icon, onClick } = props;

  return (
    <button
      className={classNames(
        "flex items-center space-x-0 py-3 px-3 rounded-30 text-1xs border font-medium transition-all ease-out duration-200 !leading-none",
        {
          "bg-primary-pastel text-primary-500 border-primary-500 border-opacity-50 shadow-pill": isActive,
          "bg-grey-fields-100 border-grey-border border-opacity-50 text-black-secondary": !isActive,
        }
      )}
      onClick={onClick}
      type="button"
    >
      {label}
    </button>
  );
};
