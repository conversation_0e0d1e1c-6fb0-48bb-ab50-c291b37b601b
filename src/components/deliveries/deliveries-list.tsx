import { CancelDelivery, GetDeliveries } from "@/api/deliveries";
import { GetDeliveriesParams, GetDeliveryParams } from "@/api/interfaces/deliveries.interface";
import { useFetcher, useRequest } from "@/api/utils";
import { createBrowserHistory } from "history";
import { useEffect, useState } from "react";
import ClearSearch from "../clear-search";
import useFluxState from "../hooks/useFluxState";
import usePagination from "../hooks/usePagination";
import useSearchParams from "../hooks/useSearchParams";
import useTabs from "../hooks/useTabs";
import Pagination from "../ui/pagination";
import Tabs from "../ui/tabs";
import DeliveriesContentState from "./content-state";
import DeliveriesBody from "./deliveries-body";
import DeliverySearch from "./delivery-search";
import { toast } from "../ui/toast";
import { tabStatuses } from "@/pages/deliveries";
import StatusInfo from "./status-info";
import Portal from "../portal";
import ConfirmDeliveryCancelModal from "./modals/confirm-cancel";
import { useModals } from "../hooks/useModals";
import { DELIVERY_STATUSES } from "@/assets/interfaces/deliveries";

const DeliveryList: React.FC<{ deliveryTabs: any }> = ({ deliveryTabs }) => {
  const { currentPage, perPage, setPerPage, goNext, goPrevious, setPage } = usePagination();
  const { search = null, status = null } = useSearchParams(["search", "status"]);
  const { tabs, active, switchTab, isActive, activeValue } = deliveryTabs;
  const { toggleModal, modals } = useModals(["confirm"]);
  const activeStatus = tabStatuses[deliveryTabs.activeValue];

  const [isfirstRun, setIsFirstRun] = useState(true);
  const [currentDeliveryIndex, setCurrentDeliveryIndex] = useState(null);
  const { response, error, isLoading, makeRequest } = useFetcher<GetDeliveriesParams>(GetDeliveries, getQueryParams());
  const cancelRequest = useRequest<GetDeliveryParams>(CancelDelivery);
  const [deliveries, setDeliveries] = useFluxState(response?.data?.data ?? [], [response]);
  const pageNotReady = deliveries.length < 1 || isLoading || error;

  useEffect(() => {
    Object.keys(tabStatuses).forEach((k, index) => {
      const value = tabStatuses[k];
      if (value === status) {
        switchTab(index);
        return;
      }
    });
  }, [status]);

  useEffect(() => {
    const history = createBrowserHistory();
    const searchParams = new URLSearchParams(window.location.search);
    setPage(1);

    if (active > 0 && !isfirstRun) {
      searchParams.set("status", tabStatuses[activeValue]);

      history.push({
        pathname: location.pathname,
        search: `?${searchParams.toString()}`,
      });
      return;
    }

    if (isfirstRun) {
      setIsFirstRun(false);
    }

    history.push({
      pathname: location.pathname,
      search: "",
    });
  }, [active]);

  const handleDelete = (idx: number) => {
    const deliveriesCopy = [...deliveries];
    deliveriesCopy.splice(idx, 1);

    setDeliveries(deliveriesCopy);
  };

  const handleCancelDelivery = async () => {
    toggleModal("confirm");

    const delivery = deliveries[currentDeliveryIndex];

    if (delivery.status !== DELIVERY_STATUSES.PENDING && delivery.status !== DELIVERY_STATUSES.CONFIRMED) {
      toast.error({
        title: "Cannot cancel delivery",
        message: "Please contact support to cancel this delivery",
      });

      return;
    }

    const [res, err] = await cancelRequest.makeRequest({ id: delivery.id });

    if (res) {
      const deliveriesCopy = [...deliveries];
      deliveriesCopy.splice(currentDeliveryIndex, 1);
      // deliveriesCopy[currentDeliveryIndex].status = "CANCELLED";
      setDeliveries(deliveriesCopy);
      toast.success({
        title: "Delivery cancelled successfully",
        message: "This delivery has been cancelled successfully.",
      });

      return;
    }

    toast.error({
      title: "Something went wrong",
      message: err.error,
    });
  };

  function getQueryParams() {
    const filter = (() => {
      const s = new URLSearchParams(window.location.search).get("search");
      const filter = {
        status: tabStatuses[activeValue],
      };

      if (s ?? search) {
        filter["search"] = s ?? search;
      }

      return filter;
    })();

    return {
      page: currentPage,
      per_page: perPage,
      sort: "DESC",
      filter,
    };
  }

  return (
    <div className="pt-5 sm:pt-7.5 lg:pt-8.75">
      <DeliverySearch />
      <div className="sticky top-0 bg-white z-50">
        <Tabs {...{ tabs, active, switchTab }} />
      </div>
      <div className="mt-5 sm:mt-7.5 overflow-hidden z-[90] pb-20">
        <ClearSearch search={search} />
        {pageNotReady && (
          <DeliveriesContentState
            isLoading={isLoading}
            deliveries={deliveries}
            error={error}
            activeLabel={activeValue.toLowerCase()}
            retryFun={() => makeRequest()}
          />
        )}

        {!pageNotReady && <StatusInfo status={activeStatus} />}
        {!pageNotReady && (
          <>
            {tabs.map(
              (tab, index) =>
                index === active && (
                  <DeliveriesBody
                    deleteDraft={handleDelete}
                    cancelDelivery={(index) => {
                      setCurrentDeliveryIndex(index);
                      toggleModal("confirm");
                    }}
                    status={tab}
                    key={index}
                    deliveries={deliveries}
                  />
                )
            )}

            <Pagination
              {...{
                currentPage,
                length: deliveries?.length,
                data: response?.data,
                goNext,
                goPrevious,
                label: "deliveries",
                per_page: perPage,
                setPage,
                setPerPage,
              }}
            />
            <Portal>
              <ConfirmDeliveryCancelModal
                complete={handleCancelDelivery}
                title="Cancel Delivery"
                show={modals.confirm.show}
                toggle={(state) => toggleModal("confirm")}
              />
            </Portal>
          </>
        )}
      </div>
    </div>
  );
};
export default DeliveryList;
