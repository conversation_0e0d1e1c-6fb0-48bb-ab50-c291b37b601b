import router from "next/router";
import { COUNTRIES, OrderInterface, OrderStatusType, ORDER_STATUSES } from "../../assets/interfaces";
import { toAppUrl, toCurrency } from "../../assets/js/utils/functions";
import { getItemThumbnail, getProductNameText } from "../../assets/js/utils/utils";
import useCopyClipboard from "../hooks/useCopyClipboard";
import LazyImage from "../lazy-image";
import { RoundActionBtn } from "../ui/buttons";
import Dropdown, { DropdownItem } from "../ui/dropdown-new";
import Checkbox from "../ui/form-elements/checkbox";
import StoreLogo from "../ui/store-logo";
import { toast } from "../ui/toast";
import authContext from "@/contexts/auth-context";
import { SCOPES, actionIsAllowed } from "@/assets/js/utils/permissions";
import Badge from "../ui/badge";

interface IProps {
  order: OrderInterface;
  showDetails: (data: any, type: any) => void;
  updateOrder: (order: OrderInterface) => void;
  handleStatusUpdate: (status: ORDER_STATUSES) => void;
  handlePaymentStatusUpdate: () => void;
  handleDeleteOrder: (order: OrderInterface) => void;
  payments_enabled: boolean;
  getOrderInvoice: VoidFunction;
}

const OrderGridItem: React.FC<IProps> = (props) => {
  const { store } = authContext.useContainer();
  const {
    order,
    showDetails,
    updateOrder,
    handleStatusUpdate,
    payments_enabled,
    getOrderInvoice,
    handlePaymentStatusUpdate,
    handleDeleteOrder,
  } = props;
  const [isCopied, copy] = useCopyClipboard(toAppUrl(`orders/${order.id}`), { successDuration: 1000 });

  const deliveryEnabledInCountry = actionIsAllowed({
    countryPermission: SCOPES.DELIVERIES.CAN_BOOK_DELIVERIES,
    country: store?.country?.code,
  });

  const getDropdownOptions = () => {
    const options: DropdownItem[] = [];

    if ([ORDER_STATUSES.PENDING, ORDER_STATUSES.ABANDONED].includes(order.status)) {
      options.push({
        text: order?.status === ORDER_STATUSES.PENDING ? "Mark as confirmed" : "Move to processing",
        onClick: () => handleStatusUpdate(ORDER_STATUSES.PROCESSING),
        icon: (
          <RoundActionBtn size="sm">
            {/* prettier-ignore */}
            <svg width="50%" viewBox="0 0 24 24" fill="none">
              <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M7.75 12L10.58 14.83L16.25 9.17004" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </RoundActionBtn>
        ),
      });
    }

    if (order.status === ORDER_STATUSES.PROCESSING) {
      options.push({
        text: "Mark as fulfilled",
        onClick: () => handleStatusUpdate(ORDER_STATUSES.FULFILLED),
        icon: (
          <RoundActionBtn size="sm">
            {/* prettier-ignore */}
            <svg width="50%" viewBox="0 0 24 24" fill="none">
              <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M7.75 12L10.58 14.83L16.25 9.17004" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </RoundActionBtn>
        ),
      });
    }

    if (!order.is_paid) {
      options.push({
        text: "Mark as Paid",
        onClick: handlePaymentStatusUpdate,
        icon: (
          <RoundActionBtn size="sm">
            {/* prettier-ignore */}
            <svg width="50%" viewBox="0 0 24 24" fill="none">
              <path d="M12 14.5C13.3807 14.5 14.5 13.3807 14.5 12C14.5 10.6193 13.3807 9.5 12 9.5C10.6193 9.5 9.5 10.6193 9.5 12C9.5 13.3807 10.6193 14.5 12 14.5Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M18.5 9.5V14.5" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M9 18C9 18.75 8.79001 19.46 8.42001 20.06C7.73001 21.22 6.46 22 5 22C3.54 22 2.26999 21.22 1.57999 20.06C1.20999 19.46 1 18.75 1 18C1 15.79 2.79 14 5 14C7.21 14 9 15.79 9 18Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M3.44141 17.9995L4.4314 18.9895L6.5614 17.0195" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M2 15.3V9C2 5.5 4 4 7 4H17C20 4 22 5.5 22 9V15C22 18.5 20 20 17 20H8.5" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </RoundActionBtn>
        ),
      });
    }

    options.push({
      text: "Copy Order link",
      onClick: () => copy(),
      icon: <RoundActionBtn size="sm" icon="copy" />,
    });

    if (order.invoice || payments_enabled) {
      options.push({
        text: order.invoice ? "Copy Invoice Link" : "Generate Invoice",
        onClick: getOrderInvoice,
        icon: (
          <RoundActionBtn size="sm">
            {/* prettier-ignore */}
            <svg width="50%" viewBox="0 0 24 24" fill="none">
              <path d="M6.73 19.7C7.55 18.82 8.8 18.89 9.52 19.85L10.53 21.2C11.34 22.27 12.65 22.27 13.46 21.2L14.47 19.85C15.19 18.89 16.44 18.82 17.26 19.7C19.04 21.6 20.49 20.97 20.49 18.31V7.04C20.5 3.01 19.56 2 15.78 2H8.22C4.44 2 3.5 3.01 3.5 7.04V18.3C3.5 20.97 4.96 21.59 6.73 19.7Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.09607 11H8.10505" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M10.8984 11H16.3984" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.09607 7H8.10505" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M10.8984 7H16.3984" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </RoundActionBtn>
        ),
      });
    }

    if (
      [ORDER_STATUSES.ABANDONED, ORDER_STATUSES.CANCELLED, ORDER_STATUSES.FULFILLED].includes(order.status) &&
      deliveryEnabledInCountry
    ) {
      options.push({
        text: order?.delivery ? "Track Delivery" : "Request Delivery",
        onClick: order?.delivery ? () => router.push(`/deliveries/${order?.delivery as string}`) : requestDelivery,
        icon: (
          <RoundActionBtn size="sm">
            {/* prettier-ignore */}
            <svg width="50%"  viewBox="0 0 24 24" fill="none" >
              <path d="M15 2V12C15 13.1 14.1 14 13 14H2V6C2 3.79 3.79 2 6 2H15Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M22 14V17C22 18.66 20.66 20 19 20H18C18 18.9 17.1 18 16 18C14.9 18 14 18.9 14 20H10C10 18.9 9.1 18 8 18C6.9 18 6 18.9 6 20H5C3.34 20 2 18.66 2 17V14H13C14.1 14 15 13.1 15 12V5H16.84C17.56 5 18.22 5.39001 18.58 6.01001L20.29 9H19C18.45 9 18 9.45 18 10V13C18 13.55 18.45 14 19 14H22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8 22C9.10457 22 10 21.1046 10 20C10 18.8954 9.10457 18 8 18C6.89543 18 6 18.8954 6 20C6 21.1046 6.89543 22 8 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M16 22C17.1046 22 18 21.1046 18 20C18 18.8954 17.1046 18 16 18C14.8954 18 14 18.8954 14 20C14 21.1046 14.8954 22 16 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M22 12V14H19C18.45 14 18 13.55 18 13V10C18 9.45 18.45 9 19 9H20.29L22 12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </RoundActionBtn>
        ),
      });
    }

    if (![ORDER_STATUSES.ABANDONED, ORDER_STATUSES.CANCELLED, ORDER_STATUSES.FULFILLED].includes(order.status)) {
      options.push({
        text: "Cancel Order",
        onClick: () => handleStatusUpdate(ORDER_STATUSES.CANCELLED),
        icon: (
          <RoundActionBtn size="sm">
            {/* prettier-ignore */}
            <svg width="50%" viewBox="0 0 16 16" fill="none" className="text-accent-red-500">
              <path d="M8.00016 14.6673C11.6668 14.6673 14.6668 11.6673 14.6668 8.00065C14.6668 4.33398 11.6668 1.33398 8.00016 1.33398C4.3335 1.33398 1.3335 4.33398 1.3335 8.00065C1.3335 11.6673 4.3335 14.6673 8.00016 14.6673Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M6.11328 9.88661L9.88661 6.11328" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M9.88661 9.88661L6.11328 6.11328" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </RoundActionBtn>
        ),

        className: "text-accent-red-500",
      });
    }

    if (order.status !== ORDER_STATUSES.FULFILLED && !order.is_paid) {
      options.push({
        text: "Delete Order",
        onClick: () => handleDeleteOrder(order),
        icon: <RoundActionBtn size="sm" icon="delete" className="text-accent-red-500" />,
        className: "text-accent-red-500",
      });
    }
    return options;
  };

  const requestDelivery = () => {
    if (order?.status === "PENDING") {
      toast.error({
        title: "Cannot request delivery for a pending order",
        message: "Please confirm order to request delivery",
      });

      return;
    }

    if (!store?.deliveries_enabled) {
      toast.error({
        title: "Deliveries not enabled!",
        message: "Please enable deliveries to request a delivery for this order",
      });
    }

    router.push(`/deliveries/initiate/orders/${order.id}`);
  };

  return (
    <div
      className="bg-white border border-grey-border border-opacity-50 w-full max-w-[500px] lg:max-w-[450px] rounded-10 flex flex-col cursor-pointer"
      onClick={() => router.push(`/orders/${order.id}`)}
    >
      <div className="px-3 sm:px-4">
        <div className="flex items-start justify-between py-3 sm:py-3.5">
          <div className="flex items-center">
            <figure className="h-10 w-10 rounded-10 flex-shrink-0 flex items-center justify-center overflow-hidden border relative">
              <LazyImage
                src={
                  order?.items[0]?.item?.variants?.type === "images"
                    ? order?.items[0]?.variant?.image
                    : getItemThumbnail(order.items[0].item)
                }
                alt="product image"
                className="h-full w-full object-cover"
              />
            </figure>
            <div className="flex flex-col ml-2">
              <span className="inline-block text-sm text-black-secondary leading-none mt-0.5">
                {getProductNameText(order?.items)}
              </span>
              <span className="font-semibold text-black text-1xs mt-2 inline-block leading-none">
                {toCurrency(order.total_amount, order?.currency)}
              </span>
            </div>
          </div>
          <button
            className="no-outline"
            onClick={(e) => {
              if (!order.customer) return;
              e.stopPropagation();
              showDetails({ ...order?.customer, delivery_address: order?.delivery_info?.delivery_address }, "customer");
            }}
          >
            <StoreLogo
              storeName={order?.customer?.name ?? "-"}
              logo={null}
              className="flex-shrink-0 w-6.25 h-6.25 text-xs"
            />
          </button>
        </div>
        <div className="flex items-center justify-between border-t border-grey-border border-opacity-50 py-2.5 sm:py-3">
          {/* <OrderStatusChange {...{ order, updateOrderStatus: handleStatusUpdate }} /> */}
          <Badge text={order?.is_paid ? "paid" : "unpaid"} color={order.is_paid ? "green" : "dark"} />
          <div className="ml-auto">
            <Dropdown items={getDropdownOptions()}>
              <button className="text-base font-semibold relative dropdown-toggle">
                {/* prettier-ignore */}
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <g opacity="0.5">
                  <path d="M5 10C3.9 10 3 10.9 3 12C3 13.1 3.9 14 5 14C6.1 14 7 13.1 7 12C7 10.9 6.1 10 5 10Z" fill="currentColor" />
                  <path d="M19 10C17.9 10 17 10.9 17 12C17 13.1 17.9 14 19 14C20.1 14 21 13.1 21 12C21 10.9 20.1 10 19 10Z" fill="currentColor" />
                  <path d="M12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10Z" fill="currentColor" />
                </g>
              </svg>
              </button>
            </Dropdown>
          </div>
        </div>
        {[ORDER_STATUSES.PROCESSING, ORDER_STATUSES.PENDING].includes(order.status) && (
          <button
            className="hidden"
            type="button"
            id={`cancel-order-${order.id}`}
            onClick={() => handleStatusUpdate(ORDER_STATUSES.CANCELLED)}
          >
            Cancel Order
          </button>
        )}
      </div>
      {/*
      {!order.customer && (
        <div className="px-4 py-2.5 bg-grey-header text-xs text-dark font-semibold">No customer info</div>
      )} */}
    </div>
  );
};

interface StatusChangeProps {
  order: OrderInterface;
  updateOrderStatus: (status: OrderStatusType) => void;
}

const OrderStatusChange: React.FC<StatusChangeProps> = ({ order, updateOrderStatus }) => {
  const status = order.status;

  if (order.status === "CANCELLED") {
    return null;
  }

  return (
    <div
      className={`${order.status === orderStatusOptions[status].updatesTo ? "opacity-60" : ""}`}
      onClick={(e) => e.stopPropagation()}
    >
      <Checkbox
        name={`set-order-as-fulfilled-${order.id}`}
        id={`update-order-${order.id}`}
        neutral
        // disabled={order.status === orderStatusOptions[status].updatesTo}
        checked={order.status === orderStatusOptions[status].updatesTo}
        label={orderStatusOptions[status].checkboxLabel}
        onChange={() => updateOrderStatus(orderStatusOptions[status].updatesTo)}
      />
    </div>
  );
};

export const orderStatusOptions: {
  [key: string]: {
    text: string;
    updatesTo?: OrderStatusType;
    checkboxLabel?: string;
  };
} = {
  PENDING: {
    text: "Pending",
    updatesTo: "PROCESSING",
    checkboxLabel: "Mark as confirmed",
  },
  PROCESSING: {
    text: "Processing",
    updatesTo: "FULFILLED",
    checkboxLabel: "Mark as fulfilled",
  },
  FULFILLED: {
    text: "Fulfilled",
    updatesTo: "FULFILLED",
    checkboxLabel: "Order Fulfilled",
  },
  CANCELLED: {
    text: "Cancelled",
  },
  ABANDONED: {
    text: "Abandoned",
    updatesTo: "PROCESSING",
    checkboxLabel: "Mark as Processing",
  },
};

export default OrderGridItem;

function getAvatarBg(name: string = "") {
  const char = name.charAt(0).toLowerCase();

  switch (true) {
    case char >= "a" && char <= "f":
      return "bg-accent-yellow-500";
    case char >= "g" && char <= "m":
      return "bg-accent-red-500";
    case char >= "n" && char <= "t":
      return "bg-accent-green-500";
    case char >= "u" && char <= "z":
      return "bg-accent-orange-500";
    default:
      return "bg-gray-600";
  }
}
