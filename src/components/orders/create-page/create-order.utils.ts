import { DELIVERY_METHODS, OrderForm } from "@/assets/interfaces";
import { phoneValidation } from "@/assets/js/utils/common-validations";
import { convertDotNotationToObject } from "@/assets/js/utils/functions";
import { transformYupErrorsIntoObject } from "@/assets/js/utils/utils";
import { FormikErrors, FormikProps } from "formik";
import * as Yup from "yup";

const validationSchema = (stepIndex: number) => {
  return Yup.object().shape({
    items: ItemsValidationShema,
    customer_info: stepIndex > 1 || stepIndex < 1 ? CustomerValidationShema : undefined,
    delivery_info: stepIndex > 2 || stepIndex < 1 ? DeliveryValidationShema : undefined,
    extra_info: stepIndex > 3 || stepIndex < 1 ? ExtraInfoValidationSchema : undefined,
  });
};

const ExtraInfoValidationSchema = Yup.object().shape({
  channel: Yup.string().required("The order's origin is required").typeError("Please add where this order came from"),
});

const ItemsValidationShema = Yup.array()
  .of(
    Yup.object().shape({
      quantity: Yup.number().required("Item quantity is required").min(1, "Item quantity must be greater than 0"),
    })
  )
  .min(1, "Please add at least one item");

const CustomerValidationShema = Yup.object().shape({
  phone: phoneValidation("phone"),
});

const DeliveryValidationShema = Yup.object().when("delivery_method", {
  is: DELIVERY_METHODS.DELIVERY,
  then: Yup.object().shape({
    name: Yup.string().when("use_customer_info", {
      is: false,
      then: Yup.string().required("Name is required"),
      otherwise: undefined,
    }),
    phone: Yup.object().when("use_customer_info", {
      is: false,
      then: phoneValidation("phone"),
      otherwise: undefined,
    }),
    area: Yup.string(),
    delivery_address: Yup.string().required("Delivery address is required"),
  }),
  otherwise: undefined,
});

const checkErrors = async (form: FormikProps<OrderForm>) => {
  const { values, initialValues, errors: initialErrors } = form;
  let errors: FormikErrors<OrderForm> = {};

  try {
    await validationSchema(4).validate(form.values, { abortEarly: false });
  } catch (err) {
    errors = transformYupErrorsIntoObject(err);
  }

  const objectErrors: any = convertDotNotationToObject(errors);

  const itemsInvalid = Boolean(objectErrors?.items) || objectErrors?.items?.length > 0;
  const customerInfoInvalid = Object.keys(objectErrors?.customer_info ?? {}).length > 0;
  const deliveryInfoInvalid = Object.keys(objectErrors?.delivery_info ?? {}).length > 0;
  const extrasInvalid = Object.keys(objectErrors?.extra_info ?? {}).length > 0;

  const statuses = {
    ITEMS: values.items.length > 0 ? itemsInvalid : true,
    CUSTOMER_INFO: customerInfoInvalid,
    DELIVERY_INFO: deliveryInfoInvalid,
    EXTRA_INFO: extrasInvalid,
  };

  return statuses;
};

export { validationSchema, checkErrors };
