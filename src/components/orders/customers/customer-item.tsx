import React from "react";
import { getUserCountry, millify, removeCountryCode, resolvePhone } from "../../../assets/js/utils/functions";
import { SCOPES } from "../../../assets/js/utils/permissions";
import { CustomerInterface } from "../../../assets/interfaces";
import Can from "../../ui/can";
import ContentWithCopy from "../../ui/content-with-copy";
import { TableCell, TableRow } from "../../ui/table";
import IconButton from "../../ui/icon-button";
import StoreLogo from "../../ui/store-logo";
import Badge from "../../ui/badge";

interface Props {
  customer: CustomerInterface;
  showDetails: (data: CustomerInterface, type: "customer" | "edit" | "delete") => void;
}

const CustomerItem: React.FC<Props> = ({ customer, showDetails }) => {
  return (
    <TableRow onClick={() => showDetails(customer, "customer")}>
      <TableCell>
        <div className="flex items-center font-black-secondary font-medium">
          <StoreLogo storeName={customer.name} logo={null} className="h-6.25 w-6.25 text-xs font-bold mr-1.5" />
          {customer.name ?? "No Name"}
        </div>
      </TableCell>
      <TableCell>
        <ContentWithCopy text={resolvePhone(customer.phone)}>
          <a className="no-outline font-medium" href={`tel:${resolvePhone(customer.phone)}`}>
            {removeCountryCode(customer.phone)}
          </a>
        </ContentWithCopy>
      </TableCell>
      <TableCell className=" hidden sm:table-cell font-medium">
        <Badge color="dark" text={`${customer?.total_orders ?? 0} orders`} />
      </TableCell>
      <TableCell className="hidden sm:table-cell">
        <span className="text-black-secondary font-medium">
          {getUserCountry().currency} {millify(customer.total_order_amount)}{" "}
          {/*UPDATE THIS TO SEPARATE THE PURCHASE CURRENCIES */}
        </span>
      </TableCell>
      <TableCell stopBubble className={"hidden sm:table-cell"}>
        <Can data={{ permission: SCOPES.CUSTOMERS.UPDATE_CUSTOMERS }}>
          {(isAllowed) => {
            return (
              isAllowed && (
                <div className="flex items-center space-x-2" onClick={(e) => e.stopPropagation()}>
                  <IconButton type="edit" onClick={() => showDetails(customer, "edit")} />
                  <IconButton type="delete" onClick={() => showDetails(customer, "delete")} />
                </div>
              )
            );
          }}
        </Can>
      </TableCell>
    </TableRow>
  );
};

export default CustomerItem;
