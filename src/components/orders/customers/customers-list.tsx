import React, { useEffect, useState } from "react";
import { useModals } from "../../hooks/useModals";
import { AppBtn } from "../../ui/buttons";
import Table, { TableHead, TableHeadItem, TableBody } from "../../ui/table";
import CustomerDetailsModal from "../modals/customer";
import { reloadPage } from "../../../assets/js/utils/functions";
import EditCustomerModal from "../modals/edit-customer";
import DeleteCustomerModal from "../modals/delete-customer";
import AddCustomerModal from "../modals/add-customer";
import { CustomerInterface } from "../../../assets/interfaces";
import CustomerItem from "./customer-item";
import CustomersHeader from "./customers-header";
import useSearchParams from "../../hooks/useSearchParams";
import { useFetcher } from "../../../api/utils";
import { GetCustomersParams } from "../../../api/interfaces/orders-customers.interface";
import { GetCustomers, GetCustomersBasic } from "../../../api/orders-customers";
import usePagination from "../../hooks/usePagination";
import ErrorBox from "../../ui/error";
import useScreenSize from "../../hooks/useScreenSize";
import { CustomerItemMobile } from "./customer-item-mobile";
import ContentState from "../../ui/content-state";
import ClearSearch from "../../clear-search";
import Pagination from "../../ui/pagination";

interface Props {}

const CustomersList: React.FC<Props> = ({}) => {
  const { currentPage, perPage, setPerPage, goNext, goPrevious, setPage } = usePagination();
  const [customers, setCustomers] = useState<CustomerInterface[]>([]);
  const [selected, setSelected] = useState<{
    customer: CustomerInterface;
    edit: CustomerInterface;
    delete: CustomerInterface;
  }>({
    customer: null,
    edit: null,
    delete: null,
  });
  const { modals, toggleModal } = useModals(["customer_details", "edit_details", "delete_details", "add_customer"]);
  const { isSmall } = useScreenSize();

  const { search = null } = useSearchParams(["search"]);
  const { response, error, isLoading, makeRequest } = useFetcher<GetCustomersParams>(GetCustomers, {
    page: currentPage,
    per_page: perPage,
    filter: (() => {
      const filter = {};
      const s = new URLSearchParams(window.location.search).get("search");

      if (s ?? search) {
        filter["search"] = s ?? search;
      }

      return filter;
    })(),
  });
  const pageNotReady = isLoading || error || !response?.data || customers.length < 1;

  useEffect(() => {
    setCustomers(response?.data.data);
  }, [response]);

  const showDetails = (data: CustomerInterface, type: "customer" | "edit" | "delete") => {
    const selectedCopy = { ...selected };
    selectedCopy[type] = data;

    setSelected(selectedCopy);
    toggleModal(`${type}_details`);
  };

  const updateCustomer = (details: CustomerInterface) => {
    const customersCopy = [...customers];

    const customerIndex = customersCopy.findIndex((c) => c.id === details.id);

    if (customerIndex > -1) {
      customersCopy[customerIndex] = details;
      setCustomers(customersCopy);
    }
  };

  const removeCustomer = (details: CustomerInterface) => {
    const customersCopy = [...customers];

    const customerIndex = customersCopy.findIndex((c) => c.id === details.id);

    if (customerIndex > -1) {
      customersCopy.splice(customerIndex, 1);
      setCustomers(customersCopy);
    }
  };

  const addCustomer = (customer: CustomerInterface) => {
    const customersCopy = [...customers];

    customersCopy.unshift(customer);
    setCustomers(customersCopy);
  };

  return (
    <div>
      <CustomersHeader toggleModal={toggleModal} />
      <ClearSearch search={search} />
      {pageNotReady && (
        <ContentState
          loadingText="Loading Customers..."
          isLoading={isLoading}
          isEmpty={customers?.length < 1}
          emptyIcon={
            // prettier-ignore
            <svg width="45%"  viewBox="0 0 24 24" fill="none">
              <path opacity="0.4" d="M12 2C9.38 2 7.25 4.13 7.25 6.75C7.25 9.32 9.26 11.4 11.88 11.49C11.96 11.48 12.04 11.48 12.1 11.49C12.12 11.49 12.13 11.49 12.15 11.49C12.16 11.49 12.16 11.49 12.17 11.49C14.73 11.4 16.74 9.32 16.75 6.75C16.75 4.13 14.62 2 12 2Z" fill="#AAAAAA"/>
              <path d="M17.08 14.1499C14.29 12.2899 9.74002 12.2899 6.93002 14.1499C5.66002 14.9999 4.96002 16.1499 4.96002 17.3799C4.96002 18.6099 5.66002 19.7499 6.92002 20.5899C8.32002 21.5299 10.16 21.9999 12 21.9999C13.84 21.9999 15.68 21.5299 17.08 20.5899C18.34 19.7399 19.04 18.5999 19.04 17.3599C19.03 16.1299 18.34 14.9899 17.08 14.1499Z" fill="#AAAAAA"/>
            </svg>
          }
          title="No Customers to show"
          description="Create a new customer"
          errorMessage="We couldn't load your customers, click on the button to retry"
          errorTitle="Fetching customers failed"
          error={error}
          errorAction={
            <AppBtn size="sm" onClick={() => makeRequest()}>
              Reload Customers
            </AppBtn>
          }
        >
          <AppBtn size="sm" onClick={() => toggleModal("add_customer")}>
            Create a customer
          </AppBtn>
        </ContentState>
      )}
      {!pageNotReady && (
        <>
          <div className="mt-5">
            {!isSmall && (
              <Table>
                <TableHead>
                  <TableHeadItem>Name</TableHeadItem>
                  <TableHeadItem>Phone</TableHeadItem>
                  <TableHeadItem className="hidden sm:table-cell">Purchases</TableHeadItem>
                  <TableHeadItem className="hidden sm:table-cell">Volume</TableHeadItem>
                  <TableHeadItem>Options</TableHeadItem>
                </TableHead>
                <TableBody>
                  {customers.map((customer, index) => (
                    <CustomerItem {...{ customer, showDetails }} key={index} />
                  ))}
                </TableBody>
              </Table>
            )}

            {isSmall && (
              <ul>
                {customers.map((c, index) => (
                  <CustomerItemMobile
                    key={index}
                    index={index}
                    onAction={(action: "customer" | "edit" | "delete") => showDetails(c, action)}
                    data={c}
                  />
                ))}
              </ul>
            )}
            <Pagination
              data={response?.data}
              {...{
                currentPage,
                setPage,
                goNext,
                length: customers.length,
                label: "customers",
                goPrevious,
                per_page: perPage,
                setPerPage,
              }}
            />
          </div>

          {selected.customer && (
            <CustomerDetailsModal
              show={modals.customer_details.show}
              toggle={() => toggleModal("customer_details")}
              customer={selected.customer}
            />
          )}

          {selected.edit && (
            <EditCustomerModal
              show={modals.edit_details.show}
              toggle={() => toggleModal("edit_details")}
              customer={selected.edit}
              updateCustomer={updateCustomer}
            />
          )}

          {selected.delete && (
            <DeleteCustomerModal
              show={modals.delete_details.show}
              toggle={() => toggleModal("delete_details")}
              customer={selected.delete}
              removeCustomer={removeCustomer}
            />
          )}

          <AddCustomerModal
            show={modals.add_customer.show}
            toggle={() => toggleModal("add_customer")}
            addCustomer={addCustomer}
          />
        </>
      )}
    </div>
  );
};

export default CustomersList;
