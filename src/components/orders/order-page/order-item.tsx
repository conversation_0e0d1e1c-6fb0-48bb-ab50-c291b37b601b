import router from "next/router";
import { CURRENCIES, CartItem, ProductItemInterface } from "../../../assets/interfaces";
import { genarateStringFromVariantValues, toCurrency } from "../../../assets/js/utils/functions";
import { getItemThumbnail } from "../../../assets/js/utils/utils";
import LazyImage from "../../lazy-image";
import Badge from "../../ui/badge";

interface IProps {
  item: CartItem;
  showProductDetails?: (item: ProductItemInterface) => void;
  currency: CURRENCIES;
  showBorderOnLast?: boolean;
  openImage: (image: string) => void;
}

const OrderItem: React.FC<IProps> = ({ item, showProductDetails, currency, showBorderOnLast = true, openImage }) => {
  const isImageVariant = item.snapshot?.variants?.type === "images" && item.snapshot?.variants?.options?.length > 0;
  const isCustomVariant = item.snapshot?.variants?.type === "custom" && item.snapshot?.variants?.options?.length > 0;

  const variantHasExtraOption = isImageVariant && item.snapshot?.variants?.options[0]?.values !== undefined;
  const variantDetails = item.variant;

  const discountIsApplied = isTieredDiscountApplied(item.snapshot, item.quantity);

  return (
    <div
      className={`w-full flex py-3 sm:py-3.75 border-b border-grey-divider hover:bg-grey-fields-100 hover:bg-opacity-30 cursor-pointer ${
        !showBorderOnLast && "last:border-0"
      } ${discountIsApplied ? "items-start" : "items-center"}`}
      onClick={() => router.push(`/products/${item?.snapshot.id}`)}
    >
      <figure
        className="h-10 w-10 rounded-10 overflow-hidden flex-shrink-0 relative cursor-pointer mt-2"
        onClick={(e) => {
          e.stopPropagation();
          openImage(isImageVariant ? variantDetails?.image : getItemThumbnail(item.snapshot));
        }}
      >
        <LazyImage
          src={isImageVariant ? variantDetails?.image : getItemThumbnail(item.snapshot)}
          alt={item?.snapshot?.name}
          className={`h-full w-full object-cover`}
        />
      </figure>
      <div className="flex-1 ml-2.5 flex items-start justify-between overflow-hidden">
        <div className="flex-1 overflow-hidden">
          <h5 className="text-dark overflow-hidden text-sm max-w-full whitespace-nowrap overflow-ellipsis capitalize flex items-center mt-1">
            {item?.snapshot?.name}
            {isCustomVariant && !item.is_deleted && (
              <span className="text-grey-subtext bg-grey-fields-100 py-0.75 px-1.75 inline-block text-xs ml-1.5 font-medium rounded-5">
                {genarateStringFromVariantValues(item?.variant?.values)}
              </span>
            )}

            {variantHasExtraOption && !item.is_deleted && (
              <span className="text-black-placeholder inline-flex items-center px-2 py-1 bg-grey-fields-100 text-xs sm:text-1xs rounded-5 font-body font-medium capitalize ml-1.5">
                {Object.keys(item.snapshot?.variants?.options[0]?.values)[0]} {Object.values(item?.variant?.values)[0]}
              </span>
            )}
          </h5>
          <div className="flex flex-col items-start mt-0.5">
            <div className="flex items-center">
              <span className="text-black-secondary font-semibold text-1xs">
                {toCurrency((item?.variant ? item?.variant.price : item?.snapshot.price) * item?.quantity, currency)}
              </span>
              {item?.variant?.original_price || item?.snapshot?.original_price ? (
                <span className="text-black-placeholder text-opacity-60 text-xxs font-semibold uppercase py-0.5 mt-0.5 ml-1 line-through">
                  {toCurrency(
                    (item?.variant ? item?.variant.original_price : item?.snapshot.original_price) * item?.quantity,
                    currency
                  )}
                </span>
              ) : null}
            </div>
            <BulkDiscountBadge item={item.snapshot} quantity={item.quantity} />
          </div>
        </div>
        <span className="text-black-placeholder font-semibold text-xxs inline-block mt-2">
          {item?.quantity} UNIT{item?.quantity > 1 ? "S" : ""}
        </span>
      </div>
    </div>
  );
};

interface BulkDiscountBadgeProps {
  item: ProductItemInterface;
  quantity: number;
}

const BulkDiscountBadge = ({ item, quantity }: BulkDiscountBadgeProps) => {
  const discountIsApplied = isTieredDiscountApplied(item, quantity);

  if (!discountIsApplied) return null;

  return (
    <div className="flex items-center text-black-placeholder text-opacity-60 text-xxs font-semibold uppercase py-0.5 mt-0.5">
      {/* prettier-ignore */}
      <svg width="16" viewBox="0 0 24 24" fill="none" className="mr-1">
        <path d="M21.5289 10.8689L20.0089 9.34891C19.7489 9.08891 19.5389 8.57891 19.5389 8.21891V6.05891C19.5389 5.17891 18.8189 4.45891 17.9389 4.45891H15.7889C15.4289 4.45891 14.9189 4.24891 14.6589 3.98891L13.1389 2.46891C12.5189 1.84891 11.4989 1.84891 10.8789 2.46891L9.33891 3.98891C9.08891 4.24891 8.57891 4.45891 8.20891 4.45891H6.05891C5.17891 4.45891 4.45891 5.17891 4.45891 6.05891V8.20891C4.45891 8.56891 4.24891 9.07891 3.98891 9.33891L2.46891 10.8589C1.84891 11.4789 1.84891 12.4989 2.46891 13.1189L3.98891 14.6389C4.24891 14.8989 4.45891 15.4089 4.45891 15.7689V17.9189C4.45891 18.7989 5.17891 19.5189 6.05891 19.5189H8.20891C8.56891 19.5189 9.07891 19.7289 9.33891 19.9889L10.8589 21.5089C11.4789 22.1289 12.4989 22.1289 13.1189 21.5089L14.6389 19.9889C14.8989 19.7289 15.4089 19.5189 15.7689 19.5189H17.9189C18.7989 19.5189 19.5189 18.7989 19.5189 17.9189V15.7689C19.5189 15.4089 19.7289 14.8989 19.9889 14.6389L21.5089 13.1189C22.1589 12.5089 22.1589 11.4889 21.5289 10.8689ZM7.99891 8.99891C7.99891 8.44891 8.44891 7.99891 8.99891 7.99891C9.54891 7.99891 9.99891 8.44891 9.99891 8.99891C9.99891 9.54891 9.55891 9.99891 8.99891 9.99891C8.44891 9.99891 7.99891 9.54891 7.99891 8.99891ZM9.52891 15.5289C9.37891 15.6789 9.18891 15.7489 8.99891 15.7489C8.80891 15.7489 8.61891 15.6789 8.46891 15.5289C8.17891 15.2389 8.17891 14.7589 8.46891 14.4689L14.4689 8.46891C14.7589 8.17891 15.2389 8.17891 15.5289 8.46891C15.8189 8.75891 15.8189 9.23891 15.5289 9.52891L9.52891 15.5289ZM14.9989 15.9989C14.4389 15.9989 13.9889 15.5489 13.9889 14.9989C13.9889 14.4489 14.4389 13.9989 14.9889 13.9989C15.5389 13.9989 15.9889 14.4489 15.9889 14.9989C15.9889 15.5489 15.5489 15.9989 14.9989 15.9989Z" fill="currentColor"/>
      </svg>
      Bulk Discount
    </div>
  );
};

const isTieredDiscountApplied = (item: ProductItemInterface, quantity: number) => {
  if (!item?.tiered_pricing?.tiers?.length || !item?.tiered_pricing?.active) return false;

  const tier = item?.tiered_pricing?.tiers
    .sort((a, b) => a.minimum_quantity - b.minimum_quantity)
    .find((tier) => quantity >= tier.minimum_quantity);
  return tier ? true : false;
};

export const getItemPrice = (cartItem: CartItem) => {
  return cartItem.is_deleted || cartItem.is_unavailable
    ? 0
    : cartItem.variant
    ? cartItem.variant.price
    : cartItem.snapshot.price;
};

export default OrderItem;
