import React, { useState } from "react";
import { OrderInterface, ORDER_FEES } from "../../../assets/interfaces";
import OrderItem from "./order-item";
import { DeliveryIcon, DiscountsIcon, OtherFeesIcon, VATIcon } from "../../../assets/icons/new/order-fees";
import { getUserCountry, toCurrency } from "../../../assets/js/utils/functions";
import symbols from "../../../assets/js/utils/currency-symbols";
import Badge from "../../ui/badge";
import { ProductDetailIcons } from "../../products/products-page/product-details";
import { statusBadgeColors } from "./order-toolbar";
import { formatDateString } from "../../../assets/js/utils/utils";
import { AppBtn } from "@/components/ui/buttons";
import Portal from "@/components/portal";
import MediaCarouselModal from "@/components/products/image-carousel-modal";
import { useModals } from "@/components/hooks/useModals";

interface IProps {
  order: OrderInterface;
}

const OrderItemsAndFees: React.FC<IProps> = ({ order }) => {
  const [imagesToDisplay, setImagesToDisplay] = useState<string[]>([]);
  const { modals, toggleModal } = useModals(["images"]);

  return (
    <>
      <div className="pt-2.5">
        <div className="border-b border-grey-border border-opacity-50 pb-3 mt-2">
          {/* <h4 className="text-black-secondary text-base font-bold">Order Status</h4>
                  <Badge color={statusBadgeColors[orderData?.status] as any} text={orderData?.status} size="md" /> */}
          <div className="flex items-center justify-between  py-1.75">
            <div className="flex items-center">
              <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2 text-dark">
                {ProductDetailIcons.payment}
              </div>
              <span className="text-1xs sm:text-sm text-dark">Payment Status</span>
            </div>
            <Badge text={order?.is_paid ? "paid" : "unpaid"} color={order.is_paid ? "green" : "dark"} />
          </div>
          <div className="flex items-center justify-between  py-1.75">
            <div className="flex items-center">
              <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2">{ProductDetailIcons.date}</div>
              <span className="text-1xs sm:text-sm text-dark">Date</span>
            </div>
            <span className="ml-2 text-black-secondary font-medium text-1xs">
              {formatDateString(new Date(order?.created_at))}
            </span>
          </div>
        </div>
        {order.items.map((item, index) => (
          <OrderItem
            item={item}
            key={index}
            currency={order?.currency}
            openImage={(image) => {
              setImagesToDisplay([image]);
              toggleModal("images");
            }}
          />
        ))}
        <div className="mt-1">
          {order.fees.map((fee, index) => (
            <div className="flex items-center justify-between py-2" key={index}>
              <div className="flex items-center">
                <div className="h-7 w-7 sm:h-7.5 sm:w-7.5 flex items-center justify-center bg-grey-fields-200 rounded-full mr-2 text-dark">
                  <figure className="w-[55%]">{feeIcons[fee.type]()}</figure>
                </div>
                <span className="text-sm text-dark">{fee.label}</span>
              </div>
              <span className="font-medium ml-2 text-black-secondary text-sm">{`${toCurrency(
                fee.amount,
                order?.currency
              )}`}</span>
            </div>
          ))}
          <div className="flex items-center justify-between py-2.5">
            <div className="flex items-center">
              <figure className="bg-accent-green-500 h-7 w-7 sm:h-7.5 sm:w-7.5 rounded-full flex items-center justify-center text-white text-sm font-bold mr-2">
                {symbols[order?.currency]}
              </figure>
              <span className="inline-block font-medium text-dark text-sm">Total Amount</span>
            </div>
            <span className="inline-block font-bold text-black text-sm">
              {toCurrency(order.total_amount, order?.currency)}
            </span>
          </div>
        </div>
      </div>
      <Portal>
        <MediaCarouselModal
          images={imagesToDisplay}
          show={modals.images.show}
          toggle={() => toggleModal("images")}
          title="Product/Option Image"
        />
      </Portal>
    </>
  );
};

const feeIcons = {
  [ORDER_FEES.VAT]: VATIcon,
  [ORDER_FEES.OTHERS]: OtherFeesIcon,
  [ORDER_FEES.PAYMENT]: OtherFeesIcon,
  [ORDER_FEES.DELIVERY]: DeliveryIcon,
  [ORDER_FEES.DISCOUNT]: DiscountsIcon,
  [ORDER_FEES.COUPON]: DiscountsIcon,
};

export default OrderItemsAndFees;
