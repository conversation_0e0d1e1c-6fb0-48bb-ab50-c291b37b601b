import { create<PERSON><PERSON>er<PERSON><PERSON><PERSON> } from "history";
import React, { useEffect, useState } from "react";
import { GetOrdersParams } from "../../api/interfaces/orders-customers.interface";
import { GetOrders } from "../../api/orders-customers";
import { useFetcher } from "../../api/utils";
import { CURRENCIES, CustomerInterface, OrderInterface, ProductItemInterface } from "../../assets/interfaces";
import { ProductDetailsModal } from "../../components/products";
import ClearSearch from "../clear-search";
import useFluxState from "../hooks/useFluxState";
import { useModals } from "../hooks/useModals";
import usePagination from "../hooks/usePagination";
import useSearchParams from "../hooks/useSearchParams";
import useTabs from "../hooks/useTabs";
import Portal from "../portal";
import { AppBtn } from "../ui/buttons";
import Pagination from "../ui/pagination";
import Tabs from "../ui/tabs";
import OrdersContentState from "./content-state";
import CustomerDetailsModal from "./modals/customer";
import FilterOrder from "./modals/filter";
import OrdersBody from "./orders-body";
import OrdersSearch from "./orders-search";

interface Props {
  currencies: CURRENCIES[];
}

const OrdersList: React.FC<Props> = ({ currencies }) => {
  const PER_PAGE = 12;
  const { currentPage, goNext, goPrevious, setPage, setPerPage, perPage } = usePagination(false, PER_PAGE);
  const { search = null, from = null, to = null, status = null } = useSearchParams(["search", "status", "from", "to"]);
  const { tabs, active, switchTab, isActive, activeValue } = useTabs(
    ["Pending", "Processing", "Fulfilled", "Cancelled", "Abandoned"],
    0
  );

  const [selected, setSelected] = useState<{
    order: OrderInterface;
    item: ProductItemInterface;
    customer: CustomerInterface;
    status: {
      order: OrderInterface;
      statusText: string;
      status: string;
    };
  }>({
    order: null,
    customer: null,
    item: null,
    status: null,
  });

  const { response, error, isLoading, makeRequest } = useFetcher<GetOrdersParams>(GetOrders, getQueryParams());
  const [orders, setOrders] = useFluxState(response?.data?.data ?? [], [response]);
  const pageNotReady = isLoading || error || !response?.data || orders.length < 1;

  const { modals, toggleModal } = useModals([
    "order_details",
    "item_details",
    "customer_details",
    "filter",
    "status_details",
  ]);

  useEffect(() => {
    tabs.includes(status) && switchTab(tabs.findIndex((s) => s === status));
  }, [status]);

  useEffect(() => {
    const history = createBrowserHistory();
    const searchParams = new URLSearchParams(window.location.search);
    setPage(1);

    if (active > 0) {
      searchParams.set("status", activeValue);

      history.push({
        pathname: location.pathname,
        search: `?${searchParams.toString()}`,
      });
      return;
    }

    history.push({
      pathname: location.pathname,
      search: "",
    });
  }, [active]);

  const showDetails = (
    data: OrderInterface & CustomerInterface & ProductItemInterface,
    type: "order" | "item" | "customer"
  ) => {
    const selectedCopy = { ...selected };
    selectedCopy[type] = data;

    setSelected(selectedCopy);
    toggleModal(`${type}_details`);
  };

  const updateOrder = (order: OrderInterface) => {
    //update order when anything changes [esp status]
    const ordersCopy = [...orders];
    const orderIndex = ordersCopy.findIndex((x) => x.id === order.id);

    ordersCopy[orderIndex] = order;
    setOrders(ordersCopy);
  };

  function getQueryParams() {
    const filter = (() => {
      const s = new URLSearchParams(window.location.search).get("search");
      const filter = {
        status: activeValue?.toLocaleUpperCase(),
      };

      if (from) {
        filter["from"] = from;
      }

      if (to) {
        filter["to"] = to;
      }

      if (s ?? search) {
        filter["search"] = s ?? search;
      }

      return filter;
    })();

    return {
      page: currentPage,
      per_page: perPage,
      sort: "DESC",
      filter,
    };
  }

  return (
    <div className="flex-1">
      <OrdersSearch currencies={currencies} />
      <div>
        <div className="sticky top-0 bg-white z-[99]">
          <Tabs tabs={tabs} switchTab={switchTab} active={active} />
        </div>
        <div className="mt-5 sm:mt-7.5 overflow-hidden z-[90] pb-20">
          <ClearSearch search={search} />
          {pageNotReady && (
            <OrdersContentState
              isLoading={isLoading}
              orders={response?.data?.data ?? []}
              error={error}
              activeLabel={activeValue}
              retryFun={() => makeRequest()}
            />
          )}
          {!pageNotReady && (
            <>
              {tabs.map(
                (tab, index) =>
                  isActive(tab.toLowerCase()) && (
                    <OrdersBody
                      key={index}
                      {...{
                        orders: orders,
                        updateOrder,
                        showDetails,
                        status: tab,
                        setOrders,
                      }}
                    />
                  )
              )}

              <Pagination
                {...{
                  currentPage,
                  length: orders?.length,
                  data: response?.data,
                  goNext,
                  goPrevious,
                  label: "orders",
                  per_page: perPage,
                  setPage,
                  setPerPage,
                }}
              />
            </>
          )}
        </div>
      </div>
      <Portal>
        {selected.item && (
          <ProductDetailsModal
            product={selected.item}
            show={modals.item_details.show}
            toggle={() => toggleModal("item_details")}
          />
        )}

        {selected.customer && (
          <CustomerDetailsModal
            show={modals.customer_details.show}
            toggle={() => toggleModal("customer_details")}
            customer={selected.customer}
          />
        )}

        <FilterOrder show={modals.filter.show} toggle={() => toggleModal("filter")} title="" />
      </Portal>
    </div>
  );
};

export default OrdersList;
