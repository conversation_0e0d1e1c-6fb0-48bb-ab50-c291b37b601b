import { convertAwsToCloudFront } from "@/components/lazy-image";
import React, { useEffect, useImperativeHandle, useRef, useState } from "react";

interface LazyVideoProps
  extends React.DetailedHTMLProps<React.VideoHTMLAttributes<HTMLVideoElement>, HTMLVideoElement> {
  src: string;
  showLoader?: boolean;
  loaderClasses?: string;
}

const LazyVideo = React.forwardRef<HTMLVideoElement, LazyVideoProps>(
  ({ src, showLoader = true, loaderClasses, ...props }, ref) => {
    const videoRef = useRef<HTMLVideoElement>(null);
    const [isVisible, setIsVisible] = useState(false);
    const [hasLoaded, setHasLoaded] = useState(false);
    const [resourceBlobUrl, setResourceBlobUrl] = useState<string>();
    const canPlay = useRef(false)

    // Expose videoRef to parent
    useImperativeHandle(ref, () => videoRef.current as HTMLVideoElement, []);

    useEffect(() => {
      if (src?.startsWith?.("/") || src?.startsWith?.("blob:")) {
        setResourceBlobUrl(src);
        return;
      }

      const awsLink = convertAwsToCloudFront(src);

      if (awsLink) {
        setResourceBlobUrl(awsLink);
      } else {
        setResourceBlobUrl(src);
      }
    }, [src]);

    useEffect(() => {
      const videoObserver = new IntersectionObserver(
        (entries, observer) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              setIsVisible(true);
              observer.unobserve(videoRef.current!);
            }
          });
        },
        { threshold: 0.1 }
      );

      if (videoRef.current) {
        videoObserver.observe(videoRef.current);
        videoRef.current.addEventListener('loadeddata', () => {
          if (canPlay.current === false) {
            videoRef.current.pause()
            videoRef.current.currentTime = 0.1
            canPlay.current = true
          }
        })
      }

      return () => videoObserver.disconnect();
    }, []);

    function getVideoSrc() {
      return isVisible ? resourceBlobUrl : "";
    }

    return (
      <>
        {!hasLoaded && showLoader && (
          <div className={`bg-grey-loader h-full w-full absolute top-0 animate-pulse ${loaderClasses}`} />
        )}
        <video
          src={getVideoSrc()}
          {...props}
          style={!hasLoaded ? { opacity: 0 } : props.style}
          ref={videoRef}
          onCanPlay={(e) => {
            setHasLoaded(true);
            props.onCanPlay?.(e);
          }}
          playsInline
          muted
          autoPlay
          preload="auto"
          crossOrigin="anonymous"
        />
      </>
    );
  }
);

export default LazyVideo;

LazyVideo.displayName = "LazyVideo";
