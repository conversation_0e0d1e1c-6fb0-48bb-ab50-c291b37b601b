import { useState, useEffect } from "react";
import { getFieldvalues } from "../../assets/js/utils/functions";
import ErrorLabel from "../ui/error-label";
import { TextArea } from "../ui/form-elements";
import SuccessLabel from "../ui/success-label";
import Toggle from "../ui/toggle";
import { base64ToUint8Array } from "../../assets/js/utils/utils";
import { useRequest } from "../../api/utils";
import { RegisterPushNotification, RemovePushSubscription, UpdateProfile } from "../../api";
import { toast } from "../ui/toast";
import { RegisterPushNotificationsParams, UpdateProfileParams } from "../../api/interfaces";
import usePushSubscription from "../hooks/usePush";
import authContext from "@/contexts/auth-context";

declare global {
  interface Window {
    workbox: any;
  }
}

const ManageNotifications: React.FC = ({}) => {
  const { user, updateUser, stores } = authContext.useContainer();
  const { isLoading, error, makeRequest, response } = useRequest<UpdateProfileParams>(UpdateProfile);

  // const [autoDebitEna]
  const { pushEnabled, enablePush } = usePushSubscription();

  const updateAutoDebits = async (state: boolean) => {
    toast.promise(
      async () => {
        const [res, err] = await makeRequest({ auto_debit_wallets: state });
        if (res) {
          updateUser({ ...response, auto_debit_wallets: state, stores });
          return;
        }
        throw err;
      },
      {
        error: {
          message: "An error occurred",
          title: "Error!",
          actionFunc: () => updateAutoDebits(state),
          actionText: "Try again",
        },
        loading: {
          message: "Setting Prefrence",
          title: "Updating...",
        },
        success: {
          title: "Success!",
          message: "Successfully updated preference",
        },
      }
    );
  };

  return (
    <>
      <div className="mb-8 md:mb-10">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-green-500 text-white m-auto flex items-center justify-center">
          {/* prettier-ignore */}
          <svg className="w-[30px] sm:w-8" viewBox="0 0 24 24" fill="none">
            <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM7.67 5.5C7.67 5.09 8.01 4.75 8.42 4.75C8.83 4.75 9.17 5.09 9.17 5.5V9.4C9.17 9.81 8.83 10.15 8.42 10.15C8.01 10.15 7.67 9.81 7.67 9.4V5.5ZM9.52282 16.4313C9.31938 16.5216 9.17 16.7132 9.17 16.9358V18.5C9.17 18.91 8.83 19.25 8.42 19.25C8.01 19.25 7.67 18.91 7.67 18.5V16.9358C7.67 16.7132 7.5206 16.5216 7.31723 16.4311C6.36275 16.0064 5.7 15.058 5.7 13.95C5.7 12.45 6.92 11.22 8.42 11.22C9.92 11.22 11.15 12.44 11.15 13.95C11.15 15.0582 10.4791 16.0066 9.52282 16.4313ZM16.33 18.5C16.33 18.91 15.99 19.25 15.58 19.25C15.17 19.25 14.83 18.91 14.83 18.5V14.6C14.83 14.19 15.17 13.85 15.58 13.85C15.99 13.85 16.33 14.19 16.33 14.6V18.5ZM15.58 12.77C14.08 12.77 12.85 11.55 12.85 10.04C12.85 8.93185 13.5209 7.98342 14.4772 7.55873C14.6806 7.46839 14.83 7.27681 14.83 7.05421V5.5C14.83 5.09 15.17 4.75 15.58 4.75C15.99 4.75 16.33 5.09 16.33 5.5V7.06421C16.33 7.28681 16.4794 7.47835 16.6828 7.56885C17.6372 7.9936 18.3 8.94195 18.3 10.05C18.3 11.55 17.08 12.77 15.58 12.77Z" fill="currentColor"/>
          </svg>
        </figure>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          Manage Preferences
        </h2>
        <span className="block text-center text-1xs text-grey-subtext">Your account preferences</span>
      </div>
      <div className="w-full mx-auto max-w-[450px]">
        <div className="flex justify-between w-full pb-5">
          <div className="flex gap-2.5 sm:gap-3.75 ">
            <div className="rounded-full h-[fit-content] p-2 sm:p-2.5 text-accent-orange-500  bg-accent-orange-500 bg-opacity-10">
              {/* prettier-ignore */}
              <svg width="20" viewBox="0 0 24 24" fill="none">
                <path d="M19 8C20.6569 8 22 6.65685 22 5C22 3.34315 20.6569 2 19 2C17.3431 2 16 3.34315 16 5C16 6.65685 17.3431 8 19 8Z" fill="currentColor"/>
                <path d="M19.8 9.42C19.78 9.42 19.76 9.43 19.74 9.43C19.64 9.45 19.54 9.46 19.43 9.48C19.01 9.52 18.56 9.5 18.1 9.41C17.98 9.38 17.88 9.36 17.77 9.32C17.44 9.24 17.13 9.11 16.84 8.94C16.72 8.88 16.6 8.8 16.49 8.73C16.01 8.4 15.6 7.99 15.27 7.51C15.2 7.4 15.12 7.28 15.06 7.16C14.89 6.87 14.76 6.56 14.68 6.23C14.64 6.12 14.62 6.02 14.59 5.9C14.5 5.44 14.48 4.99 14.52 4.57C14.54 4.46 14.55 4.36 14.57 4.26C14.57 4.24 14.58 4.22 14.58 4.2C14.7 3.58 14.24 3 13.6 3H7.52C7.38 3 7.24 3.01 7.11 3.02C6.99 3.03 6.88 3.04 6.76 3.06C6.64 3.07 6.52 3.09 6.41 3.11C4 3.46 2.46 4.99 2.11 7.41C2.09 7.52 2.07 7.64 2.06 7.76C2.04 7.88 2.03 7.99 2.02 8.11C2.01 8.24 2 8.38 2 8.52V16.48C2 16.62 2.01 16.76 2.02 16.89C2.03 17.01 2.04 17.12 2.06 17.24C2.07 17.36 2.09 17.48 2.11 17.59C2.46 20.01 4 21.54 6.41 21.89C6.52 21.91 6.64 21.93 6.76 21.94C6.88 21.96 6.99 21.97 7.11 21.98C7.24 21.99 7.38 22 7.52 22H15.48C15.62 22 15.76 21.99 15.89 21.98C16.01 21.97 16.12 21.96 16.24 21.94C16.36 21.93 16.48 21.91 16.59 21.89C19 21.54 20.54 20.01 20.89 17.59C20.91 17.48 20.93 17.36 20.94 17.24C20.96 17.12 20.97 17.01 20.98 16.89C20.99 16.76 21 16.62 21 16.48V10.4C21 9.76 20.42 9.3 19.8 9.42ZM6.75 12.5H11.75C12.16 12.5 12.5 12.84 12.5 13.25C12.5 13.66 12.16 14 11.75 14H6.75C6.34 14 6 13.66 6 13.25C6 12.84 6.34 12.5 6.75 12.5ZM15.75 18H6.75C6.34 18 6 17.66 6 17.25C6 16.84 6.34 16.5 6.75 16.5H15.75C16.16 16.5 16.5 16.84 16.5 17.25C16.5 17.66 16.16 18 15.75 18Z" fill="currentColor"/>
              </svg>
            </div>
            <div>
              <h2 className="font-bold text-base sm:text-[17px] lg:text-lg text-black">Enable Push Notifications</h2>
              <p className="text-xs sm:text-1xs text-black-muted mt-1">
                Get notified about orders, payments and other activities on your store
              </p>
            </div>
          </div>
          <div className="pl-5 lg:pl-12">
            <Toggle intialState={pushEnabled} onChange={(state) => enablePush()} />
          </div>
        </div>
        <div className="flex justify-between w-full pb-5 hidden">
          <div className="flex gap-2.5 sm:gap-3.75 ">
            <div className="rounded-full h-[fit-content] p-2 sm:p-2.5 text-accent-yellow-500 bg-accent-yellow-500 bg-opacity-10">
              {/* prettier-ignore */}
              <svg width="20" viewBox="0 0 24 24" fill="none">
                <path opacity="0.8" d="M12.8992 2.52009L12.8692 2.59009L9.96922 9.32009H7.11922C6.43922 9.32009 5.79922 9.45009 5.19922 9.71009L6.94922 5.53009L6.98922 5.44009L7.04922 5.28009C7.07922 5.21009 7.09922 5.15009 7.12922 5.10009C8.43922 2.07009 9.91922 1.38009 12.8992 2.52009Z" fill="currentColor"/>
                <path d="M18.2907 9.52002C17.8407 9.39002 17.3707 9.32002 16.8807 9.32002H9.9707L12.8707 2.59002L12.9007 2.52002C13.0407 2.57002 13.1907 2.64002 13.3407 2.69002L15.5507 3.62002C16.7807 4.13002 17.6407 4.66002 18.1707 5.30002C18.2607 5.42002 18.3407 5.53002 18.4207 5.66002C18.5107 5.80002 18.5807 5.94002 18.6207 6.09002C18.6607 6.18002 18.6907 6.26002 18.7107 6.35002C18.9707 7.20002 18.8107 8.23002 18.2907 9.52002Z" fill="currentColor"/>
                <path opacity="0.4" d="M21.7602 14.1998V16.1498C21.7602 16.3498 21.7502 16.5498 21.7402 16.7398C21.5502 20.2398 19.6002 21.9998 15.9002 21.9998H8.10023C7.85023 21.9998 7.62023 21.9798 7.39023 21.9498C4.21023 21.7398 2.51023 20.0398 2.29023 16.8598C2.26023 16.6198 2.24023 16.3898 2.24023 16.1498V14.1998C2.24023 12.1898 3.46023 10.4598 5.20023 9.70982C5.80023 9.44982 6.44023 9.31982 7.12023 9.31982H16.8802C17.3702 9.31982 17.8402 9.38982 18.2902 9.51982C20.2902 10.1298 21.7602 11.9898 21.7602 14.1998Z" fill="currentColor"/>
                <path opacity="0.6" d="M6.95023 5.52979L5.20023 9.70978C3.46023 10.4598 2.24023 12.1898 2.24023 14.1998V11.2698C2.24023 8.42979 4.26023 6.05979 6.95023 5.52979Z" fill="currentColor"/>
                <path opacity="0.6" d="M21.7591 11.2698V14.1998C21.7591 11.9898 20.2891 10.1298 18.2891 9.51984C18.8091 8.22984 18.9691 7.19984 18.7091 6.34984C18.6891 6.25984 18.6591 6.17984 18.6191 6.08984C20.4891 7.05984 21.7591 9.02984 21.7591 11.2698Z" fill="currentColor"/>
              </svg>
            </div>
            <div>
              <h2 className="font-bold text-base sm:text-[17px] lg:text-lg text-black">
                Wallet Auto-Debits for Subscriptions
              </h2>
              <p className="text-xs sm:text-1xs text-black-muted mt-1">
                Auto-debit your wallet to renew your subscription
              </p>
            </div>
          </div>
          <div className="pl-5 lg:pl-12">
            <Toggle intialState={user?.auto_debit_wallets} onChange={(state) => updateAutoDebits(state)} />
          </div>
        </div>
      </div>
    </>
  );
};

export default ManageNotifications;
