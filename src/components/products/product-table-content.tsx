import { SCOPES, actionIsAllowed } from "@/assets/js/utils/permissions";
import authContext from "@/contexts/auth-context";
import router from "next/router";
import { useEffect, useState } from "react";
import { EditItemParams } from "../../api/interfaces/items.interface";
import { EditItems, UpdateFeaturedItem } from "../../api/items";
import { useRequest } from "../../api/utils";
import { toAppUrl, toBase64, toCurrency } from "../../assets/js/utils/functions";
import useCopyClipboard from "../hooks/useCopyClipboard";
import { useModals } from "../hooks/useModals";
import useShare from "../hooks/useShare";
import { toast } from "../ui/toast";
import { DeleteProductModal, EditProductModal, ProductDetailsModal } from "./modals";
import VariantsModal from "./modals/variants";
import SelectAffiliate from "./modals/select-affiliate";

function TableContent(Component) {
  return function TableContentComponent({ ...props }) {
    const {
      store: { subscription, id: storeId },
    } = authContext.useContainer();

    const {
      product,
      categories,
      updateItem,
      updateItemList,
      deleteItem,
      isStats = false,
      itemsHaveQuantity,
      selectAffiliate,
      templates = [],
      canManageAffiliates,
      infoBlocks,
      setInfoBlocks,
      blocksLoading,
      blocksError,
      pricingTiers,
      setPricingTiers,
      storeItems,
      fetchItemsReq,
      getItem,
    } = props;

    const { available: availability, slug } = product;
    const [available, setAvailable] = useState(availability);
    const { modals, toggleModal } = useModals([
      "edit",
      "duplicate",
      "delete",
      "product_details",
      "variants",
      "select_affiliate",
    ]);
    const editItemReq = useRequest<EditItemParams>(EditItems);
    const featureEditReq = useRequest<EditItemParams>(UpdateFeaturedItem);
    const [copied, copy] = useCopyClipboard(toAppUrl(`p/${slug}`), { successDuration: 500 });
    const [isDuplication, setIsDuplication] = useState(false);
    async function updateItemFun(payload: any, isFeatureUpdate = false) {
      const [res, err] = await (isFeatureUpdate ? featureEditReq : editItemReq).makeRequest({
        id: product.id,
        item: payload,
      });

      if (err) {
        return Promise.reject(err);
      } else {
        return Promise.resolve(res);
      }
    }

    const handleToggle = (key: string) => {
      if (key === "edit") setIsDuplication(false);
      else if (key === "duplicate") setIsDuplication(true);
      toggleModal(key as any);
    };

    const { share } = useShare({
      title: product?.name,
      text: `${product?.name}\nPrice: ${toCurrency(
        product?.discount_price ? product?.discount_price : product?.price
      )}\nClick here to buy: ${toAppUrl(`/products/${slug}`, true, true)}`,
      images: product?.images,
    });

    const handleFeatureUpdate = (state: boolean) => {
      const isAllowed = actionIsAllowed({
        planPermission: SCOPES.PRODUCTS.FEATURE_PRODUCTS,
        plan: subscription?.plan?.type,
      });

      if (!isAllowed) {
        const msg = "You'll need to be on the basic plan to feature items";
        router.push(`/my-store/change-plan?message=${toBase64(msg)}`);
        return;
      }

      const toastOpts = {
        loading: {
          title: "Updating item feature",
          message: "Please wait...",
        },
        success: {
          title: "Successful",
          message: "Item feature updated successfully!",
        },
        error: {
          title: "Failed",
          message: "We couldn't update this item!",
          actionText: "Retry",
          actionFunc: () => updateItemFeature(state),
        },
      };

      async function updateItemFeature(is_featured: boolean) {
        try {
          const res = await updateItemFun(
            {
              is_featured,
            },
            true
          );
          updateItem({ ...product, is_featured });

          return res;
        } catch (e) {
          throw e;
        }
      }

      toast.promise(() => updateItemFeature(state), toastOpts);
    };

    const handleAvailabilityToggle = (state: boolean) => {
      const toastOpts = {
        loading: {
          title: "Updating item availability",
          message: "Please wait...",
        },
        success: {
          title: "Successful",
          message: "Item availability updated successfully!",
        },
        error: {
          title: "Failed to update",
          message: "We couldn't update the availability for this item!",
          actionText: "Retry",
          actionFunc: () => updateItemAvailability(state),
        },
      };

      async function updateItemAvailability(available: boolean) {
        try {
          const res = await updateItemFun({
            available,
            quantity: product?.quantity,
            is_always_available: product?.is_always_available,
          });
          setAvailable(available);

          return res;
        } catch (e) {
          throw e;
        }
      }

      toast.promise(() => updateItemAvailability(state), toastOpts);
    };

    return (
      <>
        <Component
          {...{
            product,
            handleAvailabilityToggle,
            handleFeatureUpdate,
            toggleModal: handleToggle,
            available,
            isStats,
            itemsHaveQuantity,
            selectAffiliate: () => selectAffiliate(product),
            copyLink: () => copy(),
            share: () => share(),
            canManageAffiliates,
          }}
        />
        <EditProductModal
          show={isDuplication ? modals.duplicate.show : modals.edit.show}
          toggle={() => toggleModal(isDuplication ? "duplicate" : "edit")}
          item={isDuplication ? { ...product, name: `${product?.name} (Duplicate)` } : product}
          categories={categories}
          updateItem={updateItem}
          infoBlocks={infoBlocks}
          setInfoBlocks={setInfoBlocks}
          blocksLoading={blocksLoading}
          blocksError={blocksError}
          updateItemList={updateItemList}
          isDuplication={isDuplication}
          templates={templates}
          pricingTiers={pricingTiers}
          setPricingTiers={setPricingTiers}
          storeItems={storeItems}
          fetchItemsReq={fetchItemsReq}
          getItem={getItem}
        />
        <ProductDetailsModal
          show={modals.product_details.show}
          toggle={() => toggleModal("product_details")}
          product={product}
          openEditModal={() => toggleModal("edit")}
          showVariantsModal={() => toggleModal("variants")}
        />
        <DeleteProductModal
          show={modals.delete.show}
          item={product}
          toggle={() => toggleModal("delete")}
          deleteItem={deleteItem}
        />
        {product?.variants?.options.length > 0 && (
          <VariantsModal
            show={modals.variants.show}
            product={product}
            title=""
            toggle={() => toggleModal("variants")}
            editItemReq={editItemReq}
            updateItem={updateItem}
          />
        )}
      </>
    );
  };
}

export default TableContent;
