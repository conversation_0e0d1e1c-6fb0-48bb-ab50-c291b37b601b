import { useEffect, useState } from "react";
import { GetItemsParams } from "../../api/interfaces/items.interface";
import { GetCustomItems } from "../../api/items";
import { useRequest } from "../../api/utils";
import { CustomItemInterface } from "../../assets/interfaces";
import { AppEvent } from "../../assets/js/utils/event-types";
import { reloadPage, toCurrency } from "../../assets/js/utils/functions";
import authContext from "../../contexts/auth-context";
import ClearSearch from "../clear-search";
import { useListener } from "../hooks/useListener";
import { useModals } from "../hooks/useModals";
import usePagination from "../hooks/usePagination";
import useScreenSize from "../hooks/useScreenSize";
import useSearchParams from "../hooks/useSearchParams";
import Portal from "../portal";
import { AppBtn } from "../ui/buttons";
import RoundActionButton from "../ui/buttons/round-action-btn";
import ContentState from "../ui/content-state";
import { productPageIcons } from "../ui/layouts/product";
import Pagination from "../ui/pagination";
import Table, { TableBody, TableCell, TableHead, TableHeadItem, TableRow } from "../ui/table";
import CreateCustomItemModal from "./modals/create-custom-item";
import DeleteCustomItemModal from "./modals/delete-custom-item";
import UpdateCustomItemModal from "./modals/update-custom-item";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import ErrorBox from "../ui/error";

interface Props {
  items: CustomItemInterface[];
  setItems: (items: CustomItemInterface[]) => void;
  openCreateModal: () => void;
}
const CustomItemsList: React.FC<Props> = ({ items, setItems, openCreateModal }) => {
  const { currentPage, perPage, setPerPage, goNext, goPrevious, setPage } = usePagination();
  const [currentItemIndex, setCurrentItemIndex] = useState<number>();
  const { search } = useSearchParams(["search"]);
  const { store, subscription } = authContext.useContainer();
  const { response, error, isLoading, makeRequest } = useRequest<GetItemsParams>(GetCustomItems);
  const { toggleModal, modals } = useModals(["edit", "delete", "create"]);
  const { width } = useScreenSize();
  const currentItem = items[currentItemIndex];

  useEffect(() => {
    const s = new URLSearchParams(location.search).get("search");
    if (currentPage) {
      fetchItems(s || search);
    }
  }, [currentPage, search, perPage]);

  useEffect(() => {
    setItems(response?.data.items || []);
  }, [response]);

  async function fetchItems(search: string) {
    if (!isLoading) {
      const [res, err] = await makeRequest({
        filter: { store: store.id, search: search ? search : "" },
        page: currentPage,
        per_page: perPage,
        sort: "asc",
      });
    }
  }

  const updateItem = (item: CustomItemInterface, index: number) => {
    const itemsCopy = [...items];
    itemsCopy[index] = item;

    setItems(itemsCopy);
  };

  const deleteItem = (index: number) => {
    const itemsCopy = [...items];
    itemsCopy.splice(index, 1);

    setItems(itemsCopy);
  };

  const canManageItems = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_ITEMS,
  });

  if (!canManageItems) {
    return (
      <ErrorBox title="Upgrade required" message="Upgrade to the basic or business plus plan to manage custom items">
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  if (isLoading || error || !response?.data || items.length < 1) {
    return (
      <>
        <ClearSearch search={search} />
        <ContentState
          isLoading={isLoading}
          loadingText="Loading products..."
          errorTitle="Couldn't load items"
          error={error}
          errorMessage="We couldn't load your items please click the button to retry"
          errorAction={
            <AppBtn size="md" onClick={reloadPage}>
              Reload Items
            </AppBtn>
          }
          isEmpty={items.length < 1}
          emptyIcon={
            // prettier-ignore
            <svg width="45%" viewBox="0 0 20 20" fill="none">
              <path d="M17.534 5.99169L10.0006 10.35L2.46729 5.99169C2.80062 5.37502 3.28395 4.83335 3.82562 4.53335L8.27562 2.06669C9.22562 1.53335 10.7756 1.53335 11.7256 2.06669L16.1756 4.53335C16.7173 4.83335 17.2006 5.37502 17.534 5.99169Z" fill="#F0F0F0"/>
              <path d="M10.0005 10.35V18.3333C9.37552 18.3333 8.75052 18.2 8.27552 17.9333L3.82552 15.4666C2.81719 14.9083 1.99219 13.5083 1.99219 12.3583V7.64164C1.99219 7.1083 2.17552 6.52497 2.46719 5.99164L10.0005 10.35Z" fill="#B1B1B1"/>
              <path d="M18.0088 7.64164V12.3583C18.0088 13.5083 17.1838 14.9083 16.1755 15.4666L11.7255 17.9333C11.2505 18.2 10.6255 18.3333 10.0005 18.3333V10.35L17.5338 5.99164C17.8255 6.52497 18.0088 7.1083 18.0088 7.64164Z" fill="#858484"/>
            </svg>
          }
          title="No items to show"
          description="Create a new item"
        >
          <AppBtn size="md" className="max-w-[240px] m-auto" onClick={() => openCreateModal()}>
            Create Item
          </AppBtn>
        </ContentState>
      </>
    );
  }

  if (response?.data && items.length > 0) {
    return (
      <>
        <ClearSearch search={search} />
        {
          <Table>
            <TableHead>
              <TableHeadItem className="">Name</TableHeadItem>
              {/* <TableHeadItem className="hidden sm:table-cell">Item ID</TableHeadItem> */}
              <TableHeadItem className="">price</TableHeadItem>
              <TableHeadItem>Actions</TableHeadItem>
            </TableHead>
            <TableBody>
              {items.map((product, index) => (
                <TableRow key={index}>
                  <TableCell className="text-black-secondary font-medium">{product.name} </TableCell>
                  {/* <TableCell className=" hidden sm:table-cell">{product.id}</TableCell> */}
                  <TableCell className="text-black-secondary">{toCurrency(product.price)}</TableCell>
                  <TableCell stopBubble>
                    <div className="flex items-center space-x-2">
                      <RoundActionButton
                        icon="edit"
                        onClick={() => {
                          setCurrentItemIndex(index);
                          toggleModal("edit");
                        }}
                      />
                      <RoundActionButton
                        icon="delete"
                        onClick={() => {
                          setCurrentItemIndex(index);
                          toggleModal("delete");
                        }}
                      />
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        }
        <Pagination
          data={response}
          {...{
            currentPage,
            setPage,
            goNext,
            length: items.length,
            label: "items",
            goPrevious,
            per_page: perPage,
            setPerPage,
          }}
        />
        {currentItem && (
          <Portal>
            <UpdateCustomItemModal
              updateItem={(item) => updateItem(item, currentItemIndex)}
              item={currentItem}
              toggle={() => toggleModal("edit")}
              show={modals.edit.show}
            />
            <DeleteCustomItemModal
              deleteItem={() => deleteItem(currentItemIndex)}
              item={currentItem}
              toggle={() => toggleModal("delete")}
              show={modals.delete.show}
            />
          </Portal>
        )}
      </>
    );
  }
  return null;
};

export default CustomItemsList;
