import React, { useEffect, useState } from "react";
import <PERSON><PERSON>, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "../../../ui/modal";
import { AppBtn } from "../../../ui/buttons";
import { toAppUrl } from "../../../../assets/js/utils/functions";
import useCopyClipboard from "../../../hooks/useCopyClipboard";
import { toast } from "../../../ui/toast";
import StoreLogo from "@/components/ui/store-logo";
import { CardWithDetails } from "@/components/ui/card-with-details";

interface Props {
    show: boolean;
    toggle: (status: boolean) => void;
    action: (string: "normal" | "bulk") => void;
}

const DiscountType: React.FC<Props> = ({ show, toggle, action }) => {
    const [discountType, setDiscountType] = useState<"normal" | "bulk">("normal");

    return (
        <Modal size="midi" {...{ show, toggle }} title="Create a Discount">
            <ModalBody>
                <p className="text-sm mb-4 text-black-muted">What kind of discount do you want to create?</p>
                <div className="divide-y divide-grey-border divide-opacity-30 rounded-20 overflow-hidden w-full">
                    <CardWithDetails
                        icon={
                            <figure className="rounded-full w-8 h-8 sm:h-10 sm:w-10 text-accent-orange-500 bg-white shadow-card flex items-center justify-center">
                                {/* prettier-ignore */}
                                <svg width="55%" viewBox="0 0 20 20" fill="none">
                                    <path d="M9.99935 1.66699C5.39935 1.66699 1.66602 5.40033 1.66602 10.0003C1.66602 14.6003 5.39935 18.3337 9.99935 18.3337C14.5993 18.3337 18.3327 14.6003 18.3327 10.0003C18.3327 5.40033 14.5993 1.66699 9.99935 1.66699ZM7.27435 6.38366C7.94935 6.38366 8.50768 6.93366 8.50768 7.61699C8.50768 8.29199 7.95768 8.85033 7.27435 8.85033C6.59935 8.85033 6.04102 8.30033 6.04102 7.61699C6.04102 6.93366 6.59102 6.38366 7.27435 6.38366ZM7.37435 13.167C7.24935 13.292 7.09102 13.3503 6.93268 13.3503C6.77435 13.3503 6.61602 13.292 6.49102 13.167C6.24935 12.9253 6.24935 12.5253 6.49102 12.2837L11.9493 6.82533C12.191 6.58366 12.591 6.58366 12.8327 6.82533C13.0743 7.06699 13.0743 7.46699 12.8327 7.70866L7.37435 13.167ZM12.7243 13.617C12.0493 13.617 11.491 13.067 11.491 12.3837C11.491 11.7087 12.041 11.1503 12.7243 11.1503C13.3993 11.1503 13.9577 11.7003 13.9577 12.3837C13.9577 13.067 13.4077 13.617 12.7243 13.617Z" fill="currentColor"/>
                                </svg>

                            </figure>
                        }
                        chosen={discountType}
                        title="Normal Discount"
                        description={
                            "Apply a flat discount across multiple products"
                        }
                        value="normal"
                        name="discount-type"
                        onChange={() => setDiscountType("normal")}
                    />

                    <CardWithDetails
                        icon={
                            <figure className="rounded-full w-8 h-8 sm:h-10 sm:w-10 text-accent-green-500 bg-white shadow-card flex items-center justify-center">
                                {/* prettier-ignore */}
                                <svg width="55%" viewBox="0 0 20 20" fill="none">
                                    <path d="M13.491 1.66699H6.50768C3.47435 1.66699 1.66602 3.47533 1.66602 6.50866V13.4837C1.66602 16.5253 3.47435 18.3337 6.50768 18.3337H13.4827C16.516 18.3337 18.3243 16.5253 18.3243 13.492V6.50866C18.3327 3.47533 16.5243 1.66699 13.491 1.66699ZM11.5993 13.8587H7.49935C7.15768 13.8587 6.87435 13.5753 6.87435 13.2337C6.87435 12.892 7.15768 12.6087 7.49935 12.6087H11.5993C12.666 12.6087 13.541 11.742 13.541 10.667C13.541 9.59199 12.6743 8.72533 11.5993 8.72533H7.37435L7.59102 8.94199C7.83268 9.18366 7.83268 9.58366 7.59102 9.82533C7.46602 9.95033 7.30768 10.0087 7.14935 10.0087C6.99102 10.0087 6.83268 9.95033 6.70768 9.82533L5.39935 8.51699C5.15768 8.27533 5.15768 7.87533 5.39935 7.63366L6.70768 6.32533C6.94935 6.08366 7.34935 6.08366 7.59102 6.32533C7.83268 6.56699 7.83268 6.96699 7.59102 7.20866L7.30768 7.48366H11.5993C13.3577 7.48366 14.791 8.91699 14.791 10.6753C14.791 12.4337 13.3577 13.8587 11.5993 13.8587Z" fill="currentColor"/>
                                </svg>
                            </figure>
                        }
                        chosen={discountType}
                        title="Bulk Purchase Discount"
                        description={
                            "Set quantity-based discount tiers that only apply when shoppers buy in bulk "
                        }
                        value="bulk"
                        name="discount-type"
                        onChange={() => setDiscountType("bulk")}
                    />

                </div>
            </ModalBody>
            <ModalFooter>
                <AppBtn onClick={() => action(discountType)} isBlock size="lg">
                    Continue
                </AppBtn>
            </ModalFooter>
        </Modal>
    );
};

export default DiscountType;
