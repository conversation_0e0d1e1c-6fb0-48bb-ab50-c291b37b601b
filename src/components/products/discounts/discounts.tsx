import { useEffect, useState } from "react";
import { GetItemsParams, UpdateDiscountParams } from "../../../api/interfaces/items.interface";
import { GetDiscounts, GetItems, UpdateDiscount } from "../../../api/items";
import { useFetcher, useRequest } from "../../../api/utils";
import { DiscountItemInterface, ProductItemInterface } from "../../../assets/interfaces";
import authContext from "../../../contexts/auth-context";
import { useModals } from "../../hooks/useModals";
import useScreenSize from "../../hooks/useScreenSize";
import { AppBtn } from "../../ui/buttons";
import { productPageIcons } from "../../ui/layouts/product";
import Table, { TableBody, TableHead, TableHeadItem } from "../../ui/table";
import DeleteDiscountModal from "./modals/delete-discount";
import DiscountDetailsModal from "./modals/discount-details";
import EditDiscountModal from "./modals/edit-discount";
import DiscountItem from "./discount-item";
import { DiscountItemMobile } from "./discount-mobile";
import useSearchParams from "../../hooks/useSearchParams";
import usePagination from "../../hooks/usePagination";
import Portal from "../../portal";
import ClearSearch from "../../clear-search";
import ContentState from "../../ui/content-state";
import Pagination from "../../ui/pagination";
import { toast } from "../../ui/toast";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import ErrorBox from "@/components/ui/error";

const PER_PAGE = 10;
const Discounts = () => {
  const { search } = useSearchParams(["search"]);
  const { currentPage, perPage, setPerPage, goNext, goPrevious, setPage } = usePagination();

  const { response, isLoading, error, makeRequest } = useFetcher(GetDiscounts, {
    filter: { search: search ? search : "" },
    page: currentPage,
    per_page: perPage,
    sort: "asc",
  });

  const { subscription } = authContext.useContainer();
  const [discounts, setDiscounts] = useState<DiscountItemInterface[]>([]);
  const [storeItems, setItems] = useState<ProductItemInterface[]>([]);
  const [currentDiscount, setCurrentDiscount] = useState<number>(undefined);
  const { isSmall } = useScreenSize();
  const { modals, toggleModal } = useModals(["edit", "delete", "discount_details"]);
  const updateDiscountRequest = useRequest<UpdateDiscountParams>(UpdateDiscount);

  useEffect(() => {
    if (response) {
      const data = response?.data.items as DiscountItemInterface[];
      setDiscounts(data);
    }
  }, [response]);

  const updateDiscounts = (update: DiscountItemInterface) => {
    const index = discounts.findIndex((value) => update.id === value.id);
    const discountsCopy = [...discounts];
    discountsCopy[index] = update;
    setDiscounts(discountsCopy);
  };

  const deleteDiscount = (deleted: DiscountItemInterface) => {
    setDiscounts(discounts.filter((discount) => discount.id !== deleted.id));
  };

  const handleDiscountAction = (action: "click" | "edit" | "delete" | "toggle", discount: DiscountItemInterface) => {
    const discountIndex = discounts.findIndex((d) => d.id === discount.id);
    setCurrentDiscount(discountIndex);

    switch (action) {
      case "click":
        toggleModal("discount_details");
        break;
      case "delete":
        toggleModal("delete");
        break;
      case "edit":
        toggleModal("edit");
        break;
      default:
      // do nothing
    }
  };

  const toastOpts = {
    loading: {
      title: "Updating discount activity",
      message: "Please wait...",
    },
    success: {
      title: "Successful",
      message: "Discount activity updated successfully!",
    },
    error: {
      title: "Failed",
      message: "We couldn't update the activity for this discount!",
      actionText: "Retry",
      actionFunc: () => {},
    },
  };

  async function updateDiscountActivity(active: boolean, discount: DiscountItemInterface) {
    const update = { ...discount, active };
    const [res, err] = await updateDiscountRequest.makeRequest(update);
    if (err) {
      return Promise.reject(err);
    } else {
      updateDiscounts(update);
      // handleDiscountAction("toggle", discount);
      return Promise.resolve(res);
    }
  }

  const canManageDiscounts = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_DISCOUNTS,
  });

  if (!canManageDiscounts) {
    return (
      <ErrorBox title="Upgrade required" message="Upgrade to the basic or business plus plan to manage discounts">
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  if (isLoading || error || !response?.data || discounts.length < 1) {
    return (
      <>
        <ClearSearch search={search} />
        <ContentState
          isLoading={isLoading}
          loadingText="Loading discounts"
          errorTitle="Failed to load discounts"
          error={error}
          errorMessage="We couldn't load your discounts please click the button to retry"
          errorAction={
            <AppBtn size="md" onClick={() => makeRequest()}>
              Reload Discounts
            </AppBtn>
          }
          isEmpty={discounts.length < 1}
          emptyIcon={<div className="w-8.75 h-8.75 text-grey-muted">{productPageIcons.discount}</div>}
          title="No discounts to show"
          description="Create a discount"
        >
          <AppBtn size="md" className="max-w-[240px] m-auto" href={`/products/create-discount`}>
            Create Discount
          </AppBtn>
        </ContentState>
      </>
    );
  }

  return (
    <>
      {!isLoading &&
        discounts.length > 0 &&
        (!isSmall ? (
          <div>
            <ClearSearch search={search} />
            <Table>
              <TableHead>
                {/* <TableHeadItem className=" hidden sm:table-cell">Discount ID</TableHeadItem> */}
                <TableHeadItem className="">Label</TableHeadItem>
                <TableHeadItem className="">Percentage</TableHeadItem>
                <TableHeadItem className="">Start/end date</TableHeadItem>
                <TableHeadItem className="">Options</TableHeadItem>
                <TableHeadItem className=""></TableHeadItem>
              </TableHead>
              <TableBody>
                {discounts &&
                  discounts.map((discount, index) => (
                    <DiscountItem
                      discount={discount}
                      key={index}
                      onAction={(action) => handleDiscountAction(action, discount)}
                      toggle={(state) => toast.promise(() => updateDiscountActivity(state, discount), toastOpts)}
                    />
                  ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <ul>
            {discounts &&
              discounts.map((discount, index) => (
                <DiscountItemMobile
                  data={discount}
                  key={index}
                  index={index}
                  onAction={(action) => handleDiscountAction(action, discount)}
                  toggle={(state) => toast.promise(() => updateDiscountActivity(state, discount), toastOpts)}
                />
              ))}
          </ul>
        ))}
      <Pagination
        data={response?.data}
        {...{
          currentPage,
          setPage,
          goNext,
          length: discounts.length,
          label: "discounts",
          goPrevious,
          per_page: perPage,
          setPerPage,
        }}
      />
      {currentDiscount !== undefined && (
        <Portal>
          <DiscountDetailsModal
            show={modals.discount_details.show}
            toggle={() => toggleModal("discount_details")}
            openEditModal={() => toggleModal("edit")}
            storeItems={storeItems}
            value={discounts[currentDiscount]}
          />
          <EditDiscountModal
            show={modals.edit.show}
            toggle={() => toggleModal("edit")}
            storeItems={storeItems}
            value={discounts[currentDiscount]}
            updateDiscount={(discount) => {
              toggleModal("edit");
              updateDiscounts(discount);
            }}
          />
          <DeleteDiscountModal
            show={modals.delete.show}
            discountId={discounts[currentDiscount].id}
            toggle={() => toggleModal("delete")}
            deleteDiscount={() => deleteDiscount(discounts[currentDiscount])}
          />
        </Portal>
      )}
    </>
  );
};

export default Discounts;
