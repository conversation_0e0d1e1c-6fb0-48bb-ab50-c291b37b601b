import { useEffect, useState } from "react";
import { AppEvent } from "../../assets/js/utils/event-types";
import { Category } from "../../assets/interfaces";
import { emit, useListenerState } from "../../components/hooks/useListener";
import { StoreProductCategories } from "../../components/store";
import { AppBtn } from "../../components/ui/buttons";
import { productPageIcons } from "../../components/ui/layouts/product";
import Modal, { ModalBody, ModalFooter } from "../../components/ui/modal";
import Table, { TableBody, TableCell, TableHead, TableHeadItem, TableRow } from "../../components/ui/table";
import authContext from "../../contexts/auth-context";
import ContentState from "../../components/ui/content-state";
import { useFetcher } from "../../api/utils";
import { GetStoreCategories } from "../../api";
import RoundActionButton from "../../components/ui/buttons/round-action-btn";
import useCopyClipboard from "../../components/hooks/useCopyClipboard";
import { subdomainStoreLink, toAppUrl } from "../../assets/js/utils/functions";
import useSearchParams from "../../components/hooks/useSearchParams";
import Portal from "../portal";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import ErrorBox from "../ui/error";

interface Props {
  showCreateModal: boolean;
  setShowCreateModal: (state: boolean) => void;
}

const ProductCategoriesMain: React.FC<Props> = ({ showCreateModal, setShowCreateModal }) => {
  const { store, subscription, categories } = authContext.useContainer();
  const { search } = useSearchParams(["search"]);
  const { isLoading, error, makeRequest } = useFetcher(GetStoreCategories, { id: store?.id });
  // const categories: Category[] = response?.data ?? [];
  const filteredCategories = search
    ? [...categories].filter((c) => c.name.toLocaleLowerCase().includes(search.toLocaleLowerCase()))
    : categories;

  let canManageCategories = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_CATEGORIES,
  });

  if (!canManageCategories) {
    return (
      <ErrorBox title="Upgrade required" message="Upgrade to the basic or business plus plan to manage categories">
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  return (
    <>
      <div className="pb-20">
        {isLoading || !categories || filteredCategories?.length < 1 ? (
          <>
            <ContentState
              isLoading={isLoading}
              loadingText="Loading categories..."
              errorTitle="Failed to load categories"
              error={error}
              errorMessage="We couldn't load your categories please reload page"
              errorAction={
                <AppBtn size="md" onClick={() => makeRequest()}>
                  Reload Categories
                </AppBtn>
              }
              isEmpty={filteredCategories.length < 1}
              emptyIcon={<div className="w-8.75 h-8.75 text-grey-muted">{productPageIcons.category}</div>}
              title="No categories"
              description="Create a category"
            >
              <AppBtn onClick={() => setShowCreateModal(true)} size="md" className="max-w-[240px] m-auto">
                Create Category
              </AppBtn>
            </ContentState>
          </>
        ) : (
          <Table>
            <TableHead>
              <TableHeadItem className="">CATEGORY NAME</TableHeadItem>
              <TableHeadItem className="">EMOJI</TableHeadItem>
              <TableHeadItem className="">ITEMS</TableHeadItem>
              <TableHeadItem className="">Link</TableHeadItem>
            </TableHead>
            <TableBody>
              {filteredCategories?.map((c, index) => (
                <CategoryItem data={c} storeSlug={store.slug} key={index} />
              ))}
            </TableBody>
          </Table>
        )}
      </div>
      <Portal>
        <CategoriesModal show={showCreateModal} toggle={() => setShowCreateModal(!showCreateModal)} />
      </Portal>
    </>
  );
};

interface CategoryItemProps {
  data: Category;
  storeSlug: string;
}

const CategoryItem: React.FC<CategoryItemProps> = ({ data, storeSlug }) => {
  const [copied, copy] = useCopyClipboard(
    subdomainStoreLink(`${storeSlug}`, true, "store", "products", `category=${data.id}`),
    {
      successDuration: 500,
    }
  );

  return (
    <TableRow>
      <TableCell className="text-black-secondary font-medium">{data.name}</TableCell>
      <TableCell className="">{data.emoji ?? "-"}</TableCell>
      <TableCell className="text-black-secondary">{data.items_count}</TableCell>
      <TableCell className="">
        <RoundActionButton icon="copy" onClick={() => copy()} />
      </TableCell>
    </TableRow>
  );
};
interface ModalProps {
  toggle: (state: boolean) => void;
  show: boolean;
}
const CategoriesModal: React.FC<ModalProps> = ({ toggle, show }) => {
  const { store, updateStore } = authContext.useContainer();
  const [isLoading, setIsLoading] = useListenerState(AppEvent.STORE_IS_UPDATING, false);

  const updateCategory = () => {
    emit(AppEvent.UPDATE_CATEGORIES);
  };

  return (
    <Modal title="Manage Categories" toggle={toggle} show={show} size="midi">
      <ModalBody>
        <StoreProductCategories {...{ store, updateStore, isInModal: true, setIsLoading }} />
      </ModalBody>
      <ModalFooter>
        <AppBtn id="update-category" isBlock disabled={isLoading} onClick={() => updateCategory()} size="lg">
          {isLoading ? "Updating..." : "Update product categories"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default ProductCategoriesMain;
