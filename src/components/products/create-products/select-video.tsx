import React, { useMemo, useRef } from "react";
import { AddCircle, DocumentUpload, Instagram, Warning2 } from "iconsax-react";
import useMediaProcessor from "@/components/hooks/useMediaProcessor";
import { MediaType } from "@/assets/interfaces";
import { Media } from "@/assets/interfaces";
import { Product } from "@/pages/products/create";
import { useRouter } from "next/router";
import Dropdown, { DropdownItem } from "@/components/ui/dropdown-new";
import { useModals } from "@/components/hooks/useModals";
import Portal from "@/components/portal";
import InstagramMediaPicker from "@/components/products/create-products/instagram-media-picker";
import { toast } from "@/components/ui/toast";
import { generateKey } from "@/assets/js/utils/image-selection";
import { generateSimpleUUID } from "@/assets/js/utils/functions";
import AppBtn from "@/components/ui/buttons/app-btn";
interface Props {
  product: Product;
  saveMedias: (media: Media[]) => void;
  canUploadVideos: boolean;
  canProcessVideos: boolean;
}

const SelectVideo = ({ product, saveMedias, canUploadVideos, canProcessVideos }: Props) => {
  const router = useRouter();
  const imagePicker = useRef<HTMLInputElement>(null);
  const { modals, toggleModal } = useModals(["instagram"]);

  const productMedias: Media[] = useMemo(() => {
    const imgMedias = (product.images ?? [])?.map((i) => ({ ...i, type: MediaType.IMAGE }));
    const videoMedias = (product.videos ?? [])?.map((i) => ({ ...i, type: MediaType.VIDEO }));
    return [...imgMedias, ...videoMedias];
  }, [product]);

  const { processFiles } = useMediaProcessor(
    (m) => {
      saveMedias(m);
    },
    productMedias,
    null,
    false
  );

  const handleClick = () => {
    if (canUploadVideos && canProcessVideos) {
      imagePicker.current?.click();
    } else {
      router.push("/my-store/change-plan");
    }
  };

  const handleInstagramVideos = async (urls: string[]) => {
    toggleModal("instagram");

    const filesPromise = Promise.all(
      urls.map(async (url, index) => {
        const blob = await fetch(
          process.env.NEXT_PUBLIC_API_URL + `/utils/download-file?url=${btoa(unescape(encodeURIComponent(url)))}`,
          {
            mode: "cors",
          }
        ).then((r) => r.blob());
        const file = new File([blob], `video-${index.toString()}.mp4`, { type: "video/mp4" });
        return file;
      })
    );

    toast.promise(() => filesPromise, {
      error: {
        title: "Error",
        message: "Something went wrong processing files!",
      },
      loading: {
        title: "Processing...",
        message: "Processing files. Please wait",
      },
      success: {
        title: "Processing Complete",
        message: "Files processed successfully",
      },
    });

    const files = await filesPromise;
    const medias: Media[] = [];

    files.forEach((file) => {
      medias.push({
        name: file.name,
        lastModified: file.lastModified,
        file: file,
        src: URL.createObjectURL(file),
        thumbnail: "",
        key: generateKey(file),
        meta: {
          id: generateSimpleUUID(),
        },
        type: MediaType.VIDEO,
      });
    });
    saveMedias(medias);
    // form.setFieldValue("videos", videosCopy);
    // setProcessingVideoFiles(false);
  };

  const addVideoOptions: DropdownItem[] = [
    {
      text: "Select from Device",
      onClick: () => handleClick(),
      icon: <DocumentUpload size={16} variant="Outline" className="text-dark" />,
    },

    {
      text: "Select from Instagram",
      onClick: () => toggleModal("instagram"),
      icon: <Instagram size={16} variant="Outline" className="text-dark" />,
    },
  ];

  return (
    <>
      {canProcessVideos && (
        <>
          <div className="">
            <Dropdown items={addVideoOptions} className="w-full" fullWidth>
              {/* <AppBtn size="md" color="neutral" className="w-full dropdown-toggle relative">
              + Add New Video
            </AppBtn> */}
              <button
                className=" w-full dropdown-toggle pt-[22%] border border-dashed border-grey-border rounded-15 flex flex-col items-center justify-center relative hover:border-primary-500 transition-all duration-300 ease-out cursor-pointer"
                // onClick={handleClick}
                type="button"
              >
                <div className="h-full w-full absolute inset-0 flex items-center justify-center flex-col">
                  {canUploadVideos ? (
                    <>
                      <div className="flex items-center">
                        <AddCircle size={20} variant="Outline" className="text-placeholder" />
                        <p className="text-1xs text-dark font-medium ml-0.5 mt-0.5">Add Video</p>
                      </div>
                      <p className="text-xs text-placeholder mt-1.5">MP4,MOV,OGG,WEBM allowed, max 100MB</p>
                    </>
                  ) : (
                    <p className="text-xs text-placeholder mt-1.5">
                      Please upgrade to the business+ plan to add product videos.{" "}
                      <span className="text-primary-500 font-semibold">Upgrade Plan</span>
                    </p>
                  )}
                </div>
              </button>
            </Dropdown>
          </div>
        </>
      )}

      {!canProcessVideos && (
        <div className="p-2.5 bg-opacity-20 mt-3.75 rounded-md gap-2.5 bg-accent-orange-100 text-accent-orange-500 text-xs flex items-center justify-center">
          <Warning2 size={15} className="text-accent-orange-500" />
          You cannot process videos on this browser! Please use a desktop computer.
        </div>
      )}
      <input
        type="file"
        ref={imagePicker}
        name="product-medias"
        multiple
        accept={"video/mp4,webm,ogg,video/quicktime"}
        id="product-media-picker"
        className="hidden"
        onChange={(e) => processFiles(e.target.files, [])}
      />
      <Portal>
        <InstagramMediaPicker
          mediaType="VIDEO"
          show={modals.instagram.show}
          toggleModal={(s) => toggleModal("instagram", s)}
          handleMediaSelect={handleInstagramVideos}
        />
      </Portal>
    </>
  );
};

export default SelectVideo;
