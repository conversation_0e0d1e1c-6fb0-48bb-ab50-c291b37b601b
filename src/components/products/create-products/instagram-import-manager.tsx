import { GetMultipleInstagramAlbumMedia, GetProductDetailsFromMultipleCaptions } from "@/api";
import { GetMultipleInstagramAlbumMediaParams, GetProductDetailsFromMultipleCaptionsParams } from "@/api/interfaces";
import { useRequest } from "@/api/utils";
import { Image, Media, MediaType } from "@/assets/interfaces";
import { cleanUpNumberString, generateSimpleUUID, resizeImageAndGetObjectFromUrl } from "@/assets/js/utils/functions";
import { generateKey } from "@/assets/js/utils/image-selection";
import { useLocalObject } from "@/components/hooks/useLocalState";
import { useModals } from "@/components/hooks/useModals";
import { ProductCreateMethod, ProductUploadStep } from "@/components/hooks/useProductController";
import useProgress from "@/components/hooks/useProgess";
import useSearchParams from "@/components/hooks/useSearchParams";
import ProgressModal from "@/components/ui/progress-modal";
import { toast } from "@/components/ui/toast";
import { Product, ProductForm } from "@/pages/products/create";
import React, { useEffect, useState } from "react";
import InstagramImportModal, { InstagramMedia } from "../modals/instagram-import";

interface InstagramImportManagerProps {
  medias: Media[];
  setMedias: React.Dispatch<React.SetStateAction<Media[]>>;
  active: boolean;
  setCurrentStep: (step: ProductUploadStep) => void;
  form: ProductForm;
  setForm: (form: ProductForm) => void;
  maxUploadable: number;
}

const InstagramImportManager: React.FC<InstagramImportManagerProps> = (props) => {
  const { medias, setMedias, active, setCurrentStep, form, setForm, maxUploadable } = props;
  const {
    currentProgressStep: currentProgressStep,
    progressSteps,
    setStepIsLoading,
    setStepProgress,
    nextStep,
    currentKey,
  } = useProgress([
    { key: "IMPORT", label: "Importing images from instagram...", isLoading: false, complete: false },
    { key: "UPLOADING", label: "Uploading images to our servers...", progress: 0 },
    { key: "GENERATE", label: "Generating product details...", progress: 0 },
    { key: "ASSIGN", label: "Downloading videos and assigning details...", progress: 0 },
  ]);
  const { code } = useSearchParams(["show_import_modal", "code"]);
  const [mediaCache, setMediaCache] = useLocalObject<{ [key: string]: string }>("instagram-media-cache");
  const [videosMap, setVideosMap] = useState<{ [key: string]: string[] }>({});
  const [captionMap, setCaptionMap] = useState<{ [key: string]: string }>({});
  const { modals, toggleModal } = useModals(["progress", "instagram"]);

  const getAlbumsReq = useRequest<GetMultipleInstagramAlbumMediaParams>(GetMultipleInstagramAlbumMedia);
  const getProductDetailsFromMultipleCaptionsReq = useRequest<GetProductDetailsFromMultipleCaptionsParams>(
    GetProductDetailsFromMultipleCaptions
  );

  useEffect(() => {
    if (currentKey === "GENERATE") {
      createProductsFromMedia();
    }
  }, [currentKey]);

  useEffect(() => {
    if (active || code) toggleModal("instagram");
  }, [active, code]);

  useEffect(() => {
    if (currentKey === "UPLOADING") {
      const imgs = medias.filter((i) => i.type === MediaType.IMAGE) ?? [];
      const allImagesUploaded = imgs?.every((i) => i.uploadProgress === 100 && !i.isUploading) && imgs.length > 0;
      const hasVideos = Object.keys(videosMap ?? {})?.length > 0;
      const canGoNext = allImagesUploaded || (imgs?.length === 0 && hasVideos);

      if (canGoNext) {
        setMediaCache({
          ...mediaCache,
          ...imgs.reduce((p, c) => {
            p[c.key] = c.url;
            return p;
          }, {}),
        });
        nextStep();
      } else {
        const imageUploadProgress = imgs.reduce((prev, cur) => {
          return Math.min(prev, cur.uploadProgress);
        }, 100);
        setStepProgress(imageUploadProgress);
      }
    }
  }, [medias, videosMap, currentKey]);

  const createProductsFromMedia = async () => {
    const captions: { key: string; caption: string }[] = Object.keys(captionMap).map((key) => ({
      key,
      caption: captionMap[key]?.replace(/(\r\n|\n|\r)/gm, "") ?? "",
    }));
    const products: Product[] = [];
    let productData: { price: string; name: string; description: string; variants?: any[]; variants_type?: string };

    if (captions.length > 0) {
      const [res, error] = await getProductDetailsFromMultipleCaptionsReq.makeRequest({ captions });
      const data = res?.data;

      const currentStep = nextStep();

      const videoFileMap: { [key: string]: File[] } = {};
      const videoKeys = Object.keys(videosMap);

      if (videoKeys.length > 0) {
        for (let i = 0; i < videoKeys.length; i++) {
          const k = videoKeys[i];
          const progress = 50.0 * ((i + 1) / videoKeys.length);
          const urls = videosMap[k];

          const files = await Promise.all(
            urls.map(async (url) => {
              const blob = await fetch(
                process.env.NEXT_PUBLIC_API_URL + `/utils/download-file?url=${btoa(unescape(encodeURIComponent(url)))}`,
                {
                  mode: "cors",
                }
              ).then((r) => r.blob());
              const file = new File([blob], `video-${k}.mp4`, { type: "video/mp4" });
              return file;
            })
          );

          videoFileMap[k] = [...(videoFileMap[k] ?? []), ...files];
          setStepProgress(progress, currentStep);
        }
      }

      if (error) {
        toast.error({ title: "Something went wrong!", message: error?.message });
      } else if (Array.isArray(data) && data.length > 0) {
        const imgs = medias.filter((i) => i.type === MediaType.IMAGE);

        data.forEach((result, i) => {
          const progress = 50 + 50.0 * ((i + 1) / data.length);
          productData = result?.data;
          const price = cleanUpNumberString(productData?.price);
          const videoFiles = videoFileMap[result?.key] ?? [];

          products.push({
            images: [...imgs.filter((i) => i.meta?.postId == result?.key)],
            name: productData?.name ?? "",
            price: isNaN(price) ? "0" : String(price),
            category: "",
            description: productData?.description ?? "",
            thumbnail: 0,
            price_unit: "",
            variants: {
              type: "custom",
              is_template: false,
              options: [],
            },
            is_always_available: true,
            upload_source: ProductCreateMethod.INSTAGRAM,
            info_blocks: [],
            tiered_pricing: null,
            videos: videoFiles.map((f) => ({
              name: f.name,
              key: generateKey(f),
              lastModified: f.lastModified,
              file: f,
              src: URL.createObjectURL(f),
              thumbnail: "",
              isUploading: false,
              meta: {
                id: generateSimpleUUID(),
              },
              uploadProgress: 0,
            })),
          });

          setStepProgress(progress, currentStep);
        });
      }

      setForm({ ...form, products });
      setCurrentStep("details");
    }
  };

  const handleSelectComplete = async (posts: InstagramMedia[]) => {
    toggleModal("instagram");
    toggleModal("progress");
    setStepIsLoading();

    const images: Media[] = [];
    const videos: { [key: string]: string[] } = {};
    const captions = {};
    const carouselPosts: string[] = [];

    for (const post of posts) {
      captions[post.id] = post.caption;
      const meta = { postId: post.id };

      if (post.media_type === "CAROUSEL_ALBUM") {
        carouselPosts.push(post.id);
      } else {
        if (post.media_type === "VIDEO") {
          videos[post.id] = [...(videos[post.id] ?? []), post.media_url];
        } else {
          const cachedUrl = mediaCache?.[post.id];
          const mediaImage = await resizeImageAndGetObjectFromUrl(
            post.thumbnail_url ?? post.media_url,
            post.id,
            cachedUrl,
            meta
          );
          images.push({ ...mediaImage, type: MediaType.IMAGE });
        }
      }
    }

    //the goal here is to make only one request to fetch all carousel media
    if (carouselPosts.length > 0) {
      const [res, err] = await getAlbumsReq.makeRequest({ media_ids: carouselPosts });

      if (err) {
        toast.error({ title: "Something went wrong!", message: "Couldn't load some images" });
      } else {
        const medias = res?.data as InstagramMedia[];

        for (const m of medias) {
          if (m.media_type === "VIDEO") {
            videos[m.post_id] = [...(videos[m.post_id] ?? []), m.media_url];
          } else {
            const cachedUrl = mediaCache?.[m.id];
            const image = await resizeImageAndGetObjectFromUrl(m?.thumbnail_url ?? m.media_url, m.id, cachedUrl, {
              postId: m?.post_id,
            });
            images.push({ ...image, type: MediaType.IMAGE });
          }
        }
      }
    }
    setMedias(images);
    setCaptionMap(captions);
    setVideosMap(videos);
    nextStep();
  };

  return (
    <>
      <InstagramImportModal
        toggle={() => toggleModal("instagram")}
        show={modals.instagram.show}
        title="Import Instagram Products"
        handleSelectComplete={handleSelectComplete}
        maxUploadable={maxUploadable}
      />
      <ProgressModal
        steps={progressSteps}
        currentStep={currentProgressStep}
        toggle={() => toggleModal("progress")}
        show={modals.progress.show}
        title="Import progress"
      />
    </>
  );
};

export default InstagramImportManager;

/*   const createProductsFromImages = async () => {
    const captions: { key: string; caption: string }[] = Object.keys(captionMap).map((key) => ({
      key,
      caption: captionMap[key]?.replace(/(\r\n|\n|\r)/gm, "") ?? "",
    }));
    const products: Product[] = [];
    let productData: { price: string; name: string; description: string; variants?: any[]; variants_type?: string };

    if (captions.length > 0) {
      const [res, error] = await getProductDetailsFromMultipleCaptionsReq.makeRequest({ captions });
      const data = res?.data;

      const videoFileMap: { [key: string]: File } = {};
      const videoKeys = Object.keys(videosMap);
     
       if (videoKeys.length > 0) {
        for (let i = 0; i < videoKeys.length; i++) {
          const k = videoKeys[i];
          const progress = 100.0 * ((i + 1) / videoKeys.length);
          const url = videosMap[k];
          const blob = await fetch(url, { mode: "cors" }).then((r) => r.blob());
          const file = new File([blob], `video-${k}.mp4`, { type: "video/mp4" });
          videoFileMap[k] = file;
          setStepProgress(progress);
          
        }
      } 
      // setStepProgress(100);
      // nextStep();
      // console.log(videoFileMap, videosMap);
      // return;

      if (error) {
        toast.error({ title: "Something went wrong!", message: error?.message });
      } else if (Array.isArray(data) && data.length > 0) {
        data.forEach((result, i) => {
          const progress = 100.0 * ((i + 1) / data.length);
          productData = result?.data;
          const price = cleanUpNumberString(productData?.price);
          const videoFile = videoFileMap[result?.key];

          products.push({
            images: [...images.filter((i) => i.meta?.postId == result?.key)],
            name: productData?.name ?? "",
            price: isNaN(price) ? "0" : String(price),
            category: "",
            description: productData?.description ?? "",
            thumbnail: 0,
            price_unit: "",
            variants: {
              type: "custom",
              is_template: false,
              options: [],
            },
            upload_source: ProductCreateMethod.INSTAGRAM,
            video: videoFile
              ? {
                  name: videoFile.name,
                  key: result?.key,
                  lastModified: videoFile.lastModified,
                  file: videoFile,
                  src: URL.createObjectURL(videoFile),
                  thumbnail: "",
                }
              : undefined,
          });

          setStepProgress(progress);
        });
      }

      setForm({ ...form, products });
      setCurrentStep("details");
    }
  } */
