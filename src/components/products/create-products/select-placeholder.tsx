import { Image } from "@/assets/interfaces";
import { generateSimpleUUID } from "@/assets/js/utils/functions";
import LazyImage from "@/components/lazy-image";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import { useState } from "react";

interface Props {
  toggle: VoidFunction;
  show: boolean;
  onComplete: (image: Image) => void;
}
const SelectPlaceholder: React.FC<Props> = ({ toggle, show, onComplete }) => {
  const [placeholder, setPlaceholder] = useState<string>();
  const isSelected = (src: string) => {
    return placeholder === src;
  };

  const handleSelectToggle = (placeholder: string) => {
    if (isSelected(placeholder)) {
      setPlaceholder(undefined);
    } else {
      setPlaceholder(placeholder);
    }
  };

  const handleComplete = () => {
    const image = {
      file: null,
      lastModified: Date.now(),
      name: "placeholder",
      src: placeholder,
      url: placeholder,
      isUploading: false,
      isPlaceholder: true,
      meta: {
        id: generateSimpleUUID(),
      },
    };
    onComplete(image);
    toggle();
  };

  return (
    <Modal show={show} toggle={toggle} title="Select a placeholder">
      <ModalBody noPadding>
        {mockImages?.length > 0 && (
          <div className="grid grid-cols-3 gap-2.5 p-5 sm:p-6.25">
            {newPlaceholderImages.map((img, idx) => (
              <div
                key={idx}
                onClick={() => handleSelectToggle(img)}
                className="cursor-pointer relative rounded-2xl overflow-hidden w-full"
                style={{ aspectRatio: "1/1" }}
              >
                <LazyImage
                  alt="product image"
                  className="h-full w-full object-cover"
                  loaderClasses="rounded-5"
                  src={img}
                />
                {isSelected(img) && (
                  <div className="top-0 left-0 w-full h-full absolute bg-black bg-opacity-40 flex flex-col items-center justify-center">
                    {/* prettier-ignore */}
                    <svg className="w-[25%] text-white" viewBox="0 0 24 24" fill="none">
                      <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M7.75 12L10.58 14.83L16.25 9.17004" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock onClick={handleComplete} disabled={placeholder === undefined} size="lg">
          Continue
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};
export default SelectPlaceholder;

const mockImages = [
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/food-preset-images/wraps.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/food-preset-images/cake-1.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/food-preset-images/breakfast-egg-bacon-sausage.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/food-preset-images/drink.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/food-preset-images/seafood-crab.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111826/food-preset-images/sandwich.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/food-preset-images/rice.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111826/food-preset-images/food-plate.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/food-preset-images/dessert-cake-milkshake.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/food-preset-images/burger.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111826/food-preset-images/seafood-fish.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/food-preset-images/noodles.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111824/food-preset-images/cake.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111826/food-preset-images/chicken.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111826/food-preset-images/beef.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/food-preset-images/chips-fries.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/food-preset-images/pasta-1.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111824/food-preset-images/bottle-drink.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111826/food-preset-images/doughnut.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/food-preset-images/pancakes-breakfast.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/food-preset-images/combo.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/food-preset-images/pasta.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/food-preset-images/milkshake.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/food-preset-images/canned-drink.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713112967/food-preset-images/soup.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713119692/food-preset-images/waffle.png",
];

const newPlaceholderImages = [
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/new-placeholder-images/wraps.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/new-placeholder-images/cake-1.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/new-placeholder-images/breakfast-egg-bacon-sausage.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/new-placeholder-images/drink.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/new-placeholder-images/seafood-crab.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111826/new-placeholder-images/sandwich.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/new-placeholder-images/rice.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111826/new-placeholder-images/food-plate.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/new-placeholder-images/dessert-cake-milkshake.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/new-placeholder-images/burger.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111826/new-placeholder-images/seafood-fish.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/new-placeholder-images/noodles.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111824/new-placeholder-images/cake.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111826/new-placeholder-images/chicken.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111826/new-placeholder-images/beef.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/new-placeholder-images/chips-fries.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/new-placeholder-images/pasta-1.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111824/new-placeholder-images/bottle-drink.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111826/new-placeholder-images/doughnut.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/new-placeholder-images/pancakes-breakfast.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/new-placeholder-images/combo.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/new-placeholder-images/pasta.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111827/new-placeholder-images/milkshake.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713111825/new-placeholder-images/canned-drink.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713112967/new-placeholder-images/soup.png",
  "https://res.cloudinary.com/catlog/image/upload/v1713119692/new-placeholder-images/waffle.png",
];
