import { useRequest } from "@/api/utils";
import { generateSimpleUUID } from "@/assets/js/utils/functions";
import { AppBtn } from "@/components/ui/buttons";
import SliderInput from "@/components/ui/form-elements/slider-input";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "@/components/ui/modal";
import { Product, ProductForm } from "@/pages/products/create";
import { useState } from "react";

interface Props {
  show: boolean;
  toggle: VoidFunction;
  onComplete: VoidFunction;
  setForm: (products: ProductForm) => void;
  maxUploadable: number;
}
const CreateBlankProducts: React.FC<Props> = ({ show, toggle, onComplete, setForm, maxUploadable }) => {
  const [count, setCount] = useState(0);

  const createBlankProducts = () => {
    const products: Product[] = [];

    for (let i = 0; i < count; i++) {
      products.push({
        category: "",
        description: "",
        images: [
          {
            file: null,
            lastModified: Date.now(),
            name: "placeholder",
            src: "https://res.cloudinary.com/catlog/image/upload/v1713111825/new-placeholder-images/rice.png",
            url: "https://res.cloudinary.com/catlog/image/upload/v1713111825/new-placeholder-images/rice.png",
            isUploading: false,
            isPlaceholder: true,
            meta: {
              id: generateSimpleUUID(),
            },
          },
        ],
        info_blocks: [],
        tiered_pricing: null,
        thumbnail_type: "image",
        name: "",
        price: "",
        thumbnail: 0,
        hasImages: false,
        variants: { options: [], type: "custom" },
        id: generateSimpleUUID(),
        videos: [],
      });
    }
    setForm({ products });
    onComplete();
    toggle();
  };

  return (
    <Modal show={show} toggle={toggle} title="Number of Products" size="midi">
      <ModalBody>
        <div className="m-auto max-w-[400px]">
          <p className="text-1sm sm:text-base font-medium text-black">How many products will you like to create?</p>
          <SliderInput
            className="mt-5"
            value={0}
            start={0}
            max={maxUploadable >= 10 ? 10 : maxUploadable}
            min={0}
            onChange={setCount}
          />

          <div className="grid items-start w-full grid-cols-2 gap-y-4 gap-x-6 sm:gap-x-6 mt-5">
            {Array.from(Array(count)).map((__, index) => (
              <div key={index} className="flex items-center p-1.25 border border-grey-divider rounded-[12px]">
                <figure
                  className="w-[35px] h-[35px] sm:w-12.5 sm:h-12.5 relative group cursor-pointer flex items-center justify-center flex-shrink-0 border border-grey-border border-opacity-20 rounded-10 overflow-hidden"
                  key={index}
                >
                  <div className="h-full w-full rounded-6 bg-grey-fields-100 flex items-center justify-center"></div>
                </figure>
                <div className="flex flex-col items-start ml-2.5 flex-1">
                  <div className="flex items-center mb-1 sm:mb-1.5 w-full justify-between">
                    <h4 className="inline-block text-grey-muted text-1xs sm:text-sm font-bold">Product {index + 1}</h4>
                  </div>
                  <div className="w-[90%] h-1.5 bg-grey-fields-100 bg-opacity-80 rounded-10"></div>
                  <div className="mt-1 w-[50%] h-1.5 bg-grey-fields-100 rounded-10 bg-opacity-80"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn disabled={count === 0} onClick={createBlankProducts} isBlock color="primary" size="lg">
          Create {count} Product{count > 1 ? "s" : ""}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};
export default CreateBlankProducts;
