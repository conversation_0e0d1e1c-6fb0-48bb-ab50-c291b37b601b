import React from "react";
import RoundActionButton from "@/components/ui/buttons/round-action-btn";
import { Product } from "@/pages/products/create";
import { FormikProps } from "formik";
import { DiscountShape, InfoCircle } from "iconsax-react";
import { AppBtn, RoundActionBtn } from "../../ui/buttons";
import Portal from "@/components/portal";
import { PricingTierInterface, ProductItemInterface } from "@/assets/interfaces";
import PricingTierModal from "../pricing-tiers/modals/view-tier-pricing";
import { RequestInterface } from "@/api/utils";
import { ProductFormRef } from "./form";
import AssignedItemsModal from "../pricing-tiers/modals/assigned-items";
import { CreateTierModal, EditTierModal } from "../pricing-tiers/imports";
import { useListenerState } from "@/components/hooks/useListener";

interface Props {
  form: FormikProps<Product>;
  toggleModal: (modal: any) => void;
  editPricingTier: () => void;
  deletePricingTier: () => void;
  viewPricingTier: () => void;
}

const ManageTieredPricing = ({ form, toggleModal, editPricingTier, deletePricingTier, viewPricingTier }: Props) => {
  return (
    <>
      {!form.values.tiered_pricing && (
        <div className="mt-3.5 flex items-center justify-between">
          <div className="flex items-center">
            <AppBtn color="neutral" size="sm" onClick={() => toggleModal("select_pricing_tier")}>
              Add Bulk Purchase Discount (Optional)
            </AppBtn>
          </div>

          <button className="bg-grey-fields-100 flex items-center justify-center h-8 w-8 rounded-full text-black-placeholder">
            <InfoCircle size={18} />
          </button>
        </div>
      )}

      {form.values.tiered_pricing && (
        <div className="mt-3.5 bg-grey-fields-100 rounded-15">
          <div className="flex items-center justify-between px-3 py-2">
            <span className="text-sm text-dark">Bulk Purchase Discount</span>

            <div className="flex gap-2 items-center" onClick={(e) => e.stopPropagation()}>
              <RoundActionBtn
                icon="edit"
                className="text-black-secondary !bg-white"
                onClick={editPricingTier}
                size="sm"
              />
              <RoundActionBtn
                icon="delete"
                className="text-accent-red-500 !bg-white"
                onClick={deletePricingTier}
                size="sm"
              />
            </div>
          </div>

          <div className="flex items-center justify-between px-3 py-3.75 bg-white rounded-15 border border-grey-border border-opacity-50">
            <div className="flex items-center">
              <figure
                className={`h-6.25 w-6.25 rounded-full flex items-center justify-center text-accent-green-500 bg-grey-fields-100 mr-1.5`}
              >
                <DiscountShape size={16} variant="Bold" />
              </figure>
              <span className="text-1xs text-black font-medium">{form.values.tiered_pricing.label}</span>
            </div>

            <button className="flex items-center cursor-pointer select-none" onClick={viewPricingTier}>
              <span className={`mr-2 font-semibold text-primary-500 text-1xs`}>View Pricing</span>
              {/* prettier-ignore */}
              <svg viewBox="0 0 14 15" fill="none" className="w-3 transform rotate-[-45deg] -ml-1">
                <path d="M1 7.5L13 7.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M7 13.5L13 7.5L7 1.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      )}
    </>
  );
};

interface PricingTierModalProps {
  currentPricingTier: PricingTierInterface;
  modals: any;
  toggleModal: (modal: any) => void;
  toCurrency: (amount: number) => string;
  storeItems: ProductItemInterface[];
  fetchItemsReq: RequestInterface<any>;
  getItem: (item: any) => any;
  formRefs: { [key: number]: ProductFormRef | null };
  currentProduct: number;
  setCurrentPricingTier: (tier: PricingTierInterface) => void;
  pricingTiersData: {
    tiers: PricingTierInterface[];
    setTiers: (tiers: PricingTierInterface[]) => void;
  };
}

export const PricingTierModals = ({
  currentPricingTier,
  modals,
  toggleModal,
  toCurrency,
  storeItems,
  fetchItemsReq,
  getItem,
  formRefs,
  currentProduct,
  setCurrentPricingTier,
  pricingTiersData,
}: PricingTierModalProps) => {
  const [itemPrice, setItemPrice] = useListenerState<number>("item-price-change", 0);

  return (
    <Portal>
      {currentPricingTier && (
        <>
          <PricingTierModal
            show={modals.view_pricing_tier.show}
            toggle={() => toggleModal("view_pricing_tier")}
            rule={currentPricingTier}
            editTier={() => toggleModal("edit_pricing_tier")}
            viewItems={() => toggleModal("assigned_items")}
            fromProductForm
            itemPrice={itemPrice}
          />

          <AssignedItemsModal
            show={modals.assigned_items.show}
            toggle={() => toggleModal("assigned_items")}
            items={currentPricingTier.items}
            formatAsCurrency={toCurrency}
            allItems={storeItems}
            fetchItemsReq={fetchItemsReq}
            getItem={getItem}
            tierId={currentPricingTier?.id}
            updateTier={(data: PricingTierInterface) => {
              const form = formRefs[currentProduct]?.formik;
              form?.setFieldValue("tiered_pricing", data);
            }}
            fromProductForm
          />

          <EditTierModal
            show={modals.edit_pricing_tier.show}
            toggle={() => toggleModal("edit_pricing_tier")}
            tier={currentPricingTier}
            updateTier={(data: PricingTierInterface) => {
              const form = formRefs[currentProduct]?.formik;
              form?.setFieldValue("tiered_pricing", data);

              //set the current pricing tier to ensure all modals are updated
              setCurrentPricingTier(data);

              //update the pricing tiers list
              const tierIndex = pricingTiersData.tiers.findIndex((t) => t.id === data.id);
              const tiersCopy = [...pricingTiersData.tiers];
              tiersCopy[tierIndex] = data;
              pricingTiersData.setTiers(tiersCopy);
            }}
            allItems={storeItems}
            fetchItemsReq={fetchItemsReq}
            getItem={getItem}
            fromProductForm
          />
        </>
      )}
      <CreateTierModal
        show={modals.create_pricing_tier.show}
        toggle={() => toggleModal("create_pricing_tier")}
        updateTiers={(tier) => {
          pricingTiersData.setTiers([tier, ...pricingTiersData.tiers]);
          const form = formRefs[currentProduct]?.formik;
          form?.setFieldValue("tiered_pricing", tier);
        }}
        allItems={[]}
        fetchItemsReq={null}
        getItem={() => null}
      />
    </Portal>
  );
};

export default ManageTieredPricing;
