import { Video } from "@/assets/interfaces";
import { getVideoSize } from "@/assets/js/utils/utils";
import { useModals } from "@/components/hooks/useModals";
import { VideoProgress, VideoStepStatus } from "@/components/hooks/useVideoTranscode";
import { convertAwsToCloudFront } from "@/components/lazy-image";
import LazyVideo from "@/components/lazy-video";
import Portal from "@/components/portal";
import { formatTime } from "@/components/products/modals/process-video/trim-manager";
import PlayVideoModal from "@/components/ui/play-video";
import classNames from "classnames";
import { ArrowCircleUp2, Image, PictureFrame, Refresh2, TickCircle, Trash, VideoCircle } from "iconsax-react";
import { useEffect, useMemo, useRef, useState } from "react";

interface Props {
  openVideoModal?: (modal: string) => void;
  removeVideo?: VoidFunction;
  retryTask: VoidFunction;
  changeThumbnail?: VoidFunction;
  videoProgress: VideoProgress;
  video: Video;
  error?: string;
  minimal?: boolean;
}

const VideoUploadCard: React.FC<Props> = ({
  video,
  openVideoModal,
  removeVideo,
  retryTask,
  changeThumbnail,
  videoProgress,
  error,
  minimal = false,
}) => {
  const [loaded, setLoaded] = useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const [fileSize, setFileSize] = useState(0);

  const videoUrl = useMemo(() => {
    if (video && video?.file !== null && video?.file instanceof File)
      try {
        return URL.createObjectURL(video.file);
      } catch (err) {
        // console.log(video.file);
        // console.log(err);
        return video?.url;
      }
    else return video?.url;
  }, [video]);

  // console.log(videoUrl)

  useEffect(() => {
    if (video && video?.file !== null && video?.file instanceof File) setFileSize(video.file.size);
    else getVideoSize(video?.url).then((size) => setFileSize(size));
  }, [video]);

  const { modals, toggleModal } = useModals(["video"]);
  const step = videoProgress?.step;

  const hasError = step === VideoStepStatus.ERROR;
  const hasFile = Boolean(video.file);

  const isActive = Boolean(videoProgress?.progress) || !hasFile;
  const showStatusProgress = step !== undefined || video.uploadProgress === 100;
  const showSecondaryActions = !showStatusProgress;

  // console.log({ videoUrl });
  // console.log({ thumbnail: video?.thumbnail });
  // console.log({ loaded, hasFile });
  // console.log({ videoRef: videoRef.current });

  return (
    <>
      <div className={`${error && "border-accent-red-500 border"} w-full relative rounded-15 bg-grey-fields-100 `}>
        <div className="p-4 w-full ">
          <div className="flex justify-between items-start w-full">
            <div className="flex flex-1 gap-2.5 w-full">
              <figure
                onClick={() => toggleModal("video")}
                className="w-[55px] h-[55px] sm:w-15 sm:h-15 relative group cursor-pointer flex items-center justify-center flex-shrink-0 border border-grey-border border-opacity-20 rounded-10 overflow-hidden"
                style={
                  video?.thumbnail
                    ? {
                        backgroundImage: `url(${convertAwsToCloudFront(video?.thumbnail)})`,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                      }
                    : {}
                }
              >
                <LazyVideo
                  ref={videoRef}
                  src={videoUrl}
                  controls={false}
                  className="w-full h-full object-cover"
                  playsInline
                  onCanPlay={() => setLoaded(true)}
                  onLoadedData={() => setLoaded(true)}
                />
                <div className="absolute top-0 left-0 right-0 bottom-0 w-full h-full bg-black bg-opacity-20"></div>
                <div className="absolute left-0 right-0 mx-auto p-1.25 bg-white bg-opacity-20 w-[fit-content] h-[fit-content] rounded-full">
                  <VideoCircle size={20} className="text-white" />
                </div>
              </figure>
              <div className="text-left flex-1 w-1/2">
                <span className="block overflow-hidden w-[80%] overflow-ellipsis text-sm text-black-secondary font-semibold">
                  {(video as any)?.file?.name || video?.meta?.name || "video"}
                </span>
                {loaded && hasFile && (
                  <span className="text-xs text-black-placeholder text-left">
                    {fileSize > 0
                      ? fileSize > 1024 * 1024
                        ? (fileSize / (1024 * 1024)).toFixed(2) + " MB  • "
                        : (fileSize / 1024).toFixed(2) + " KB  • "
                      : ""}
                    {formatTime(videoRef?.current?.duration)}
                  </span>
                )}

                {((loaded && videoStatusText[step]) || video?.uploadProgress === 100) && (
                  <div className="flex flex-1 justify-between items-center">
                    <span
                      className={classNames("flex items-center gap-0.5 text-xs", {
                        "text-primary-500": !hasError,
                        "text-accent-red-500": hasError,
                        "text-accent-green-500": step === VideoStepStatus.SUCCESS || video?.uploadProgress === 100,
                      })}
                    >
                      {videoStatusText[step] ?? "Uploaded"}{" "}
                      {(step === VideoStepStatus.SUCCESS || video?.uploadProgress === 100) && (
                        <TickCircle size={15} variant="Bold" className="text-accent-green-500" />
                      )}
                    </span>

                    {video?.uploadProgress === 100 && (
                      <button
                        className="inline-flex text-primary-500  items-center gap-1.25"
                        type="button"
                        onClick={changeThumbnail}
                      >
                        <PictureFrame size={13} variant="Outline" className="text-primary-500" />
                        <span className="text-xs font-medium">Update Thumbnail</span>
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="flex absolute right-2.5 top-2.5 flex-shrink-0 items-center gap-2.5">
              {!minimal && (
                <>
                  <button
                    type="button"
                    onClick={removeVideo}
                    className="h-6.25 w-6.25 bg-white flex items-center justify-center rounded-full"
                  >
                    <Trash size={15} variant="Outline" className="text-accent-red-500" />
                  </button>
                  <button
                    type="button"
                    onClick={() => toggleModal("video")}
                    className="h-6.25 w-6.25 bg-white flex items-center justify-center rounded-full"
                  >
                    <VideoCircle size={15} variant="Outline" className="text-black-secondary" />
                  </button>
                </>
              )}
            </div>
          </div>

          {showStatusProgress && videoProgress?.step !== VideoStepStatus.SUCCESS && video?.uploadProgress !== 100 && (
            <div className="gap-1.25 flex items-center justify-between text-left mt-2.5">
              <div className="flex-1 h-1 bg-grey-border rounded-full relative overflow-hidden">
                {!isActive && <div className="highlight"></div>}
                {isActive && (
                  <div className="h-full bg-accent-green-500" style={{ width: `${videoProgress?.progress}%` }}></div>
                )}
              </div>
              <span className="text-dark text-xs">
                {isActive ? <span className="font-semibold"> {videoProgress?.progress ?? 100}%</span> : ""}
              </span>
            </div>
          )}

          {(showSecondaryActions || hasError) && (
            <div className="pt-3.75 flex items-center  border-t border-grey-outline justify-between mt-2.5">
              {showSecondaryActions && (
                <div className="flex items-center gap-3.75 w-full">
                  <button
                    className="inline-flex text-primary-500 items-center gap-1.25 ml-auto"
                    type="button"
                    onClick={() => {
                      openVideoModal("process_video");
                    }}
                  >
                    <ArrowCircleUp2 size={15} variant="Outline" className="text-primary-500" />
                    <span className="text-1xs font-medium">Save Video</span>
                  </button>
                </div>
              )}
              {hasError && (
                <button className="flex text-primary-500 items-center gap-1.25" type="button" onClick={retryTask}>
                  <Refresh2 size={15} variant="Outline" className="text-primary-500" />
                  <span className="text-1xs font-medium">Retry</span>
                </button>
              )}
            </div>
          )}
        </div>

        <Portal>
          {video?.file && (
            <PlayVideoModal
              title={(video as any)?.file?.name || video?.meta?.name || "video"}
              video={videoUrl}
              show={modals.video.show}
              toggle={() => toggleModal("video")}
            ></PlayVideoModal>
          )}
        </Portal>
      </div>
      {error && (
        <div className="flex items-center justify-between mt-1.25">
          <div className="text-accent-red-500 text-xs font-medium flex items-center font-body">
            {/* prettier-ignore */}
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="mr-1">
            <path d="M7.018 3.55288L2.15392 11.6731C2.05363 11.8467 2.00057 12.0437 2 12.2442C1.99944 12.4447 2.0514 12.6419 2.15071 12.8162C2.25003 12.9904 2.39323 13.1356 2.56607 13.2373C2.73892 13.339 2.93538 13.3937 3.13592 13.3959H12.8641C13.0646 13.3937 13.2611 13.339 13.4339 13.2373C13.6068 13.1356 13.75 12.9904 13.8493 12.8162C13.9486 12.6419 14.0006 12.4447 14 12.2442C13.9994 12.0437 13.9464 11.8467 13.8461 11.6731L8.982 3.55288C8.87963 3.3841 8.73548 3.24456 8.56347 3.14772C8.39146 3.05088 8.1974 3 8 3C7.8026 3 7.60854 3.05088 7.43653 3.14772C7.26452 3.24456 7.12037 3.3841 7.018 3.55288V3.55288Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"></path><path d="M8 6.50452V8.8016" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round">
              </path><path d="M8 11.0992H8.00718" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"></path>
              </svg>
            <span className="inline-block">{error}</span>
          </div>
        </div>
      )}
    </>
  );
};
export default VideoUploadCard;

const videoStatusText = {
  [VideoStepStatus.WAITING]: "Waiting...",
  [VideoStepStatus.COMPRESSING]: "Optimizing...",
  [VideoStepStatus.UPLOADING]: "Uploading...",
  [VideoStepStatus.SUCCESS]: "Uploaded",
};
