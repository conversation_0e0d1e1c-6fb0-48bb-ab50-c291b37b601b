import { getProductThumbnail } from "@/assets/js/utils/utils";
import useProductForm from "@/components/hooks/useProductForm";
import { VideoProgresses, VideoTranscodeTask } from "@/components/hooks/useVideoTranscode";
import LazyImage from "@/components/lazy-image";
import Portal from "@/components/portal";
import VideoUploadCard from "@/components/products/create-products/video-upload-card";
import DeleteOptionsConfirmation from "@/components/products/modals/delete-options";
import DataAccordion from "@/components/ui/data-accordion";
import React, { forwardRef, useImperativeHandle } from "react";
import { ProductCategoriesModal } from "../";
import { InfoBlockInterface, PricingTierInterface, VariantForm } from "../../../assets/interfaces";
import { getFieldvalues, getProductsCurrency } from "../../../assets/js/utils/functions";
import { Product, ProductForm as ProductFormType } from "../../../pages/products/create";
import { AppBtn, RoundActionBtn } from "../../ui/buttons";
import { DatePickerInput, InputField, SelectDropdown, TextArea, MultiSelect } from "../../ui/form-elements";
import { toast } from "../../ui/toast";
import MediaCarouselModal from "../image-carousel-modal";
import ManageBlocksInProducts from "../info-blocks/manage-blocks-in-products";
import { ManageVariants } from "../modals";
import ProcessVideoModal from "../modals/process-video";
import PickProductMedia from "../pick-product-media";
import ProductUploadFooter from "./footer";
import MangageProductQuantity from "./manage-product-quantity";
import SelectPlaceholder from "./select-placeholder";
import SelectVideo from "./select-video";
import VariantsExplainerModal from "./variants-explainer";
import { InfoCircle } from "iconsax-react";
import SelectPricingTier from "../pricing-tiers/modals/select-pricing-tier";
import ManageTieredPricing from "./manage-tiered-pricing";

export interface ProductFormProps {
  product: Product;
  setForm: (form: ProductFormType) => void;
  index: number;
  form: ProductFormType;
  changeView: (dir: string) => void;
  submitForm: VoidFunction;
  isLoading: boolean;
  categories: any[];
  allowVariants: boolean;
  isSetup: boolean;
  currentIndex?: number;
  variantTemplates?: Product["variants"][];
  toggleOuterModal: (m: string) => void;
  videoProgresses: VideoProgresses;
  removeVideoProgress: (productIndex: number, taskId?: string) => void;
  videoResourcesLoaded?: boolean;
  transcodeVideo: (data: VideoTranscodeTask) => void;
  retryTranscode: (task: string, taskId: string) => void;
  canProcessVideos?: boolean;
  canUploadVideos?: boolean; //plan permission for videos
  infoBlocksData: {
    blocks: InfoBlockInterface[];
    loading: boolean;
    error: any;
    setBlocks: (blocks: InfoBlockInterface[]) => void;
  };
  preventVideoUploads?: boolean;
  viewPricingTier: () => void;
  editPricingTier: () => void;
  allTiers: PricingTierInterface[];
  openCreatePricingTierModal: () => void;
}

export interface ProductFormRef {
  formik: any;
}

const ProductForm = forwardRef<ProductFormRef, ProductFormProps>((props, ref) => {
  const {
    index,
    videoProgresses,
    categories,
    allowVariants,
    form,
    variantTemplates,
    changeView,
    isLoading,
    transcodeVideo,
    canProcessVideos = false,
    canUploadVideos = false,
    retryTranscode,
    infoBlocksData,
    preventVideoUploads = false,
    viewPricingTier,
    editPricingTier,
    allTiers,
    openCreatePricingTierModal,
    isSetup,
  } = props;

  const {
    changeThumbnail,
    store,
    thisForm,
    toggleModal,
    modals,
    currentVideoIndex,
    updateForm,
    product,
    handleOpenVideoModal,
    openVariantForm,
    removePickedMedia,
    removeProductVideo,
    saveImages,
    saveMedias,
    scrollPageToTop,
    productImageUploading,
    variantsCount,
    deleteToastOptions,
    setVideoTrim,
    deleteOptions,
    videoTrim,
    deletePricingTier,
  } = useProductForm(props);

  // Expose formik methods to parent via ref
  useImperativeHandle(ref, () => ({
    formik: thisForm,
  }));

  return (
    <>
      <form onSubmit={thisForm.handleSubmit} className="flex flex-col items-center text-center w-full pb-12.5">
        <div className="flex flex-col items-center">
          {getProductThumbnail(thisForm.values) && (
            <figure
              className="h-20 relative w-20 rounded-15 overflow-hidden mb-2.5 cursor-pointer"
              onClick={() => toggleModal("images")}
            >
              <LazyImage src={getProductThumbnail(thisForm.values)} alt="" className="w-full h-full object-cover" />
            </figure>
          )}
          <h4 className="font-bold text-dark text-base sm:text-lg">Add Details - Product {index + 1}</h4>
          <button
            className="flex items-center text-1xs font-medium text-accent-red-500 bg-grey-fields-100 hover:bg-opacity-60 transition-all duration-300 ease-out px-3 py-1.5 rounded-lg mt-2"
            onClick={() => toast.error(deleteToastOptions)}
            type="button"
          >
            <span>Remove item</span>
            {/* prettier-ignore */}
            <svg width="13" viewBox="0 0 14 14" fill="none" className="ml-1">
              <path d="M1.75 3.5H2.91667H12.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
              <path d="M4.66699 3.49984V2.33317C4.66699 2.02375 4.78991 1.72701 5.0087 1.50821C5.22749 1.28942 5.52424 1.1665 5.83366 1.1665H8.16699C8.47641 1.1665 8.77316 1.28942 8.99195 1.50821C9.21074 1.72701 9.33366 2.02375 9.33366 2.33317V3.49984M11.0837 3.49984V11.6665C11.0837 11.9759 10.9607 12.2727 10.742 12.4915C10.5232 12.7103 10.2264 12.8332 9.91699 12.8332H4.08366C3.77424 12.8332 3.47749 12.7103 3.2587 12.4915C3.03991 12.2727 2.91699 11.9759 2.91699 11.6665V3.49984H11.0837Z" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
              <path d="M5.83301 6.4165V9.9165" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
              <path d="M8.16699 6.4165V9.9165" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </button>
        </div>
        <div className="my-8 sm:my-10 w-full">
          <PickProductMedia
            selectPlaceholder={() => toggleModal("placeholders")}
            {...{
              product: thisForm.values,
              changeThumbnail,
              removePickedMedia,
              saveMedias,
              saveImages,
              canProcessVideos: canProcessVideos && canUploadVideos && !preventVideoUploads,
            }}
          />
          {thisForm.values?.videos?.length > 0 && !preventVideoUploads && (
            <DataAccordion
              isClosed={false}
              className="border-b border-grey-divider rounded-none"
              title={
                <span className="text-base font-bold font-body tracking-normal">
                  <span className="font-display">Product Videos</span>
                </span>
              }
            >
              <div className="space-y-2.5 pt-2.5">
                {thisForm.values.videos.map((v, i) => (
                  <VideoUploadCard
                    key={i}
                    retryTask={() => retryTranscode(product.id, v.meta.id)}
                    videoProgress={videoProgresses?.[v.meta.id]}
                    openVideoModal={(m) => handleOpenVideoModal(m, i)}
                    removeVideo={() => removeProductVideo(index, v.meta.id)}
                    video={v}
                    error={
                      thisForm.touched.videos &&
                      (thisForm.errors?.videos?.[i] ? "Please select a thumbnail" : undefined)
                    }
                  />
                ))}
                <SelectVideo
                  product={thisForm.values}
                  saveMedias={saveMedias}
                  canUploadVideos={canUploadVideos}
                  canProcessVideos={canProcessVideos}
                />
              </div>
            </DataAccordion>
          )}

          {thisForm.values?.videos?.length < 1 && !preventVideoUploads && (
            <div className="mt-5 border-t border-grey-divider pt-3.5">
              <SelectVideo
                product={thisForm.values}
                saveMedias={saveMedias}
                canUploadVideos={canUploadVideos}
                canProcessVideos={canProcessVideos}
              />
            </div>
          )}
          <InputField label="Product Name" {...getFieldvalues("name", thisForm)} />
          <MultiSelect
            label="Product Categories"
            options={[...categories].map((c) => ({ text: `${c.emoji} ${c.name}`, value: c.id }))}
            emptyLabel="No categories to show"
            hasSearch
            searchLabel="Search for categories"
            action={{ label: "Create new categories +", onClick: () => toggleModal("categories") }}
            {...getFieldvalues("tags", thisForm)}
          />
          <TextArea label="Product Description" {...getFieldvalues("description", thisForm)} rows={2} />

          <div className="mt-5 border-t border-grey-divider pt-3.5" onClick={(e) => e.preventDefault()}>
            <DataAccordion
              isClosed={false}
              title={
                <span className="text-base font-bold font-body tracking-normal">
                  <span className="font-display">Product Pricing</span>
                </span>
              }
            >
              <div className="mt-3.5 -mb-3.5">
                <InputField
                  label={`Product Price ${getProductsCurrency()} (No commas)`}
                  type="number"
                  min={1}
                  {...getFieldvalues("price", thisForm)}
                  inputMode="numeric"
                  step="any"
                />
                <InputField
                  label={`Discount Price ${getProductsCurrency()} (Optional)`}
                  type="number"
                  {...getFieldvalues("discount_price", thisForm)}
                  inputMode="numeric"
                />
                <InputField
                  label={`What was the cost to buy/make this? (Optional)`}
                  type="number"
                  {...getFieldvalues("cost_price", thisForm)}
                  inputMode="numeric"
                />
                {thisForm.values.discount_price && thisForm.values.variants?.options.length > 0 && (
                  <div className="bg-grey-fields-100 p-2 rounded-lg text-placeholder font-medium text-xs mt-1.5 flex items-center">
                    <figure className="h-8 w-8 rounded-full bg-white flex items-center justify-center text-accent-yellow-500 flex-shrink-0 mr-1.5">
                      <InfoCircle variant="Bold" size={18} />
                    </figure>
                    <span>
                      When you set a discount price, No need to change option prices — discounts apply automatically.
                    </span>
                  </div>
                )}
                <ManageTieredPricing
                  form={thisForm}
                  toggleModal={toggleModal}
                  editPricingTier={editPricingTier}
                  deletePricingTier={deletePricingTier}
                  viewPricingTier={viewPricingTier}
                />
              </div>
            </DataAccordion>
          </div>

          {!isSetup && (
            <div className="mt-5 border-t border-grey-divider pt-3.5" onClick={(e) => e.preventDefault()}>
              <DataAccordion
                isClosed={false}
                title={
                  <span className="text-base font-bold font-body tracking-normal">
                    <span className="font-display">Product Options</span>
                  </span>
                }
              >
                <div className="mt-3.5 -mb-3.5">
                  <div className="mt-3 flex items-center justify-between">
                    <div className="flex items-center">
                      <AppBtn color="neutral" size="sm" onClick={() => openVariantForm()} disabled={!allowVariants}>
                        {variantsCount > 0 ? `Update Options` : "Add Options"}
                      </AppBtn>
                      <span className="text-dark text-1xs inline-block ml-1.5 font-medium">
                        {variantsCount} Options added.
                      </span>
                    </div>
                    {variantsCount > 0 && (
                      <RoundActionBtn
                        icon="delete"
                        className="text-accent-red-500"
                        onClick={() => toggleModal("delete_options")}
                      />
                    )}
                  </div>
                  <div className="mt-3.5 border-t border-grey-divider">
                    <div className="flex items-center justify-between bg-grey-fields-100 rounded-10 py-2 px-2.5 mt-3.5">
                      <div className="flex items-center">
                        <figure className="h-6 w-6 bg-accent-yellow-500 flex items-center justify-center text-white rounded-full">
                          {/* prettier-ignore */}
                          <svg width="55%" viewBox="0 0 24 24" fill="none">
                      <path d="M18.0204 12.33L16.8004 11.11C16.5104 10.86 16.3404 10.49 16.3304 10.08C16.3104 9.63 16.4904 9.18 16.8204 8.85L18.0204 7.65C19.0604 6.61 19.4504 5.61 19.1204 4.82C18.8004 4.04 17.8104 3.61 16.3504 3.61H5.90039V2.75C5.90039 2.34 5.56039 2 5.15039 2C4.74039 2 4.40039 2.34 4.40039 2.75V21.25C4.40039 21.66 4.74039 22 5.15039 22C5.56039 22 5.90039 21.66 5.90039 21.25V16.37H16.3504C17.7904 16.37 18.7604 15.93 19.0904 15.14C19.4204 14.35 19.0404 13.36 18.0204 12.33Z" fill="currentColor" />
                    </svg>
                        </figure>
                        <span className="text-dark text-sm font-medium inline-block ml-2">
                          What are product options?
                        </span>
                      </div>
                      <button
                        className="flex items-center text-primary-500 font-medium text-1xs"
                        onClick={() => toggleModal("v_explainer")}
                        type="button"
                      >
                        Learn More
                        {/* prettier-ignore */}
                        <svg className="w-[9px] ml-1" viewBox="0 0 10 10" fill="none">
                    <path d="M1.24264 0.24265V1.73818L7.16643 1.74348L0.71231 8.1976L1.77297 9.25826L8.22709 2.80414L8.23239 8.72793H9.72792V0.24265H1.24264Z" fill="#332089" />
                  </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </DataAccordion>
            </div>
          )}
          <div className="mt-5 border-t border-grey-divider pt-3.5" onClick={(e) => e.preventDefault()}>
            <MangageProductQuantity store={store} form={thisForm} />
          </div>
          <ManageBlocksInProducts
            form={thisForm}
            infoBlocks={infoBlocksData.blocks}
            blocksLoading={infoBlocksData.loading}
            setInfoBlocks={infoBlocksData.setBlocks}
            blocksError={infoBlocksData.error}
          />
          <div className="mt-5 border-t border-grey-divider pt-3.5" onClick={(e) => e.preventDefault()}>
            <DataAccordion
              isClosed={true}
              title={
                <span className="text-base font-bold font-body tracking-normal">
                  <span className="font-display">Optional Product Settings</span>
                </span>
              }
            >
              <div className="mt-3.5">
                <InputField
                  type="number"
                  label="What's the min. quantity customers can buy?"
                  {...getFieldvalues("minimum_order_quantity", thisForm)}
                  inputMode="numeric"
                />
                <DatePickerInput
                  label="Expiry date (Food, Drugs, e.t.c)"
                  {...getFieldvalues("expiry_date", thisForm)}
                />
              </div>
            </DataAccordion>
          </div>
        </div>
        <ProductUploadFooter
          {...{
            form,
            index,
            isLoading,
            productImageUploading,
            back: () => changeView("backwards"),
            next: () => scrollPageToTop(),
            showFooter: true,
          }}
        />
      </form>
      <Portal>
        <ProductCategoriesModal show={modals.categories.show} toggle={() => toggleModal("categories")} />
        <ManageVariants
          show={modals.variants.show}
          toggle={() => toggleModal("variants")}
          title=""
          product={thisForm.values}
          saveVariants={(variants: VariantForm) => {
            thisForm.setFieldValue("variants", variants);
            updateForm({ ...thisForm.values, variants });
          }}
          templates={variantTemplates}
        />
        <MediaCarouselModal
          images={thisForm.values.images.map((i) => i.src)}
          videos={thisForm.values.videos?.map((v) => v.src ?? v.url)}
          show={modals.images.show}
          toggle={() => toggleModal("images")}
          title="Selected Medias"
        />
        <DeleteOptionsConfirmation
          show={modals.delete_options.show}
          toggle={() => toggleModal("delete_options")}
          deleteOptions={deleteOptions}
        />
        <VariantsExplainerModal title="" show={modals.v_explainer.show} toggle={() => toggleModal("v_explainer")} />
        <SelectPlaceholder
          onComplete={(i) => saveImages([i])}
          show={modals.placeholders.show}
          toggle={() => toggleModal("placeholders")}
        />
        <SelectPricingTier
          show={modals.select_pricing_tier.show}
          toggle={() => toggleModal("select_pricing_tier")}
          allTiers={allTiers}
          productTier={thisForm.values.tiered_pricing}
          setProductTier={(tier) => {
            thisForm.setFieldValue("tiered_pricing", tier);
          }}
          openCreatePricingTierModal={openCreatePricingTierModal}
        />
        {thisForm.values.videos?.length > 0 && (
          <>
            <ProcessVideoModal
              currentVideo={thisForm.values.videos?.[currentVideoIndex]}
              onTrim={(start, end) => {
                const newVideoTrim = [...videoTrim];
                newVideoTrim[currentVideoIndex] = { start, end };
                setVideoTrim(newVideoTrim);
              }}
              show={modals.process_video.show}
              toggle={() => toggleModal("process_video")}
              value={videoTrim[currentVideoIndex]}
              transcodeVideo={(dims) => {
                transcodeVideo({
                  dimensions: dims,
                  trim: videoTrim[currentVideoIndex],
                  taskId: thisForm.values.videos[currentVideoIndex].meta.id,
                  file: thisForm.values.videos[currentVideoIndex].file as any,
                  meta: {
                    videoIndex: currentVideoIndex,
                    productIndex: index,
                  },
                });
              }}
              setVideo={(video) => {
                const videosCopy = [...thisForm.values.videos];
                videosCopy[currentVideoIndex] = video;
                thisForm.setFieldValue("videos", videosCopy);
              }}
              callback={() => {
                if (!getProductThumbnail(thisForm.values)) {
                  thisForm.setFieldValue("thumbnail_type", "video");
                }
              }}
            />
          </>
        )}
      </Portal>
    </>
  );
});

export default ProductForm;

ProductForm.displayName = "ProductForm";
