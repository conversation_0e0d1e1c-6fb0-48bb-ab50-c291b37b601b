import InstagramImportModal, { MediaImportConfig } from "@/components/products/modals/instagram-import";

interface Props extends MediaImportConfig {
  show: boolean;
  toggleModal: (state: boolean) => void;
}
const InstagramMediaPicker: React.FC<Props> = ({ show, toggleModal, mediaType, handleMediaSelect }) => {
  return (
    <>
      <InstagramImportModal
        toggle={() => toggleModal(!show)}
        show={show}
        title="Import Instagram Products"
        importConfig={{ mediaType: mediaType, handleMediaSelect }}
        type="SELECT"
      />
    </>
  );
};
export default InstagramMediaPicker;
