import React from "react";
import Modal, { ModalBody, ModalProps } from "../../ui/modal";

interface Props extends ModalProps {}

const PricingTiersExplainer: React.FC<Props> = ({ show, toggle }) => {
  return (
    <Modal {...{ show, toggle }} title="What are Extra Info Blocks?" size="midi">
      <ModalBody>
        <div className="text-dark text-sm">
          <p>
            Info Blocks are a powerful way to share extra details about your products on Catlog. Whether it's care
            instructions, size charts, how-to-use guides, or style variations, Info Blocks help you organize and present
            this information clearly using text or images.
            <br /> <br />
            They make your product pages more helpful, more professional, and more likely to convert visitors into
            customers.
          </p>
          <br />
          <p>You can create Info Blocks in two formats:</p>
          <ul className="list-disc ml-5">
            <li>
              <b>Text-Based Info Blocks:</b> Great for adding written information like: Care instructions (e.g., “Hand
              wash only”), How-to-use tips, Product specifications
            </li>
            <li>
              <b>Image-Based Info Blocks:</b> Perfect for when you want to visually show: Size Charts, Design options
              (e.g., colorways, patterns), Styling ideas, Step-by-step photo guides
            </li>
          </ul>
          <br />
          <p>
            <b>Why Use Info Blocks?</b>
          </p>
          <ul className="list-disc ml-5">
            <li>Help customers make faster decisions by giving them all the details they need</li>
            <li>Highlight key features or selling points of your product</li>
            <li>Build trust with clean, organized, and helpful product pages</li>
            <li>Stand out from other sellers who don’t provide enough information</li>
          </ul>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default PricingTiersExplainer;
