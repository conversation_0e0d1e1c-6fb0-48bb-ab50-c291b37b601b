import React from "react";

const LoadingPlaceholder: React.FC = () => {
  return (
    <div className="grid grid-cols-[repeat(auto-fit,minmax(350px,1fr))] gap-5">
      {Array.from({ length: 6 }).map((_, i) => (
        <div key={i} className="relative h-full">
          <div className="flex flex-col gap-2 px-3.5 bg-white border border-gray-border border-opacity-50 items-center rounded-15 relative h-full">
            <div className="flex justify-between items-center w-full px-0 pt-2.5">
              <div className="flex gap-x-2">
                <figure className="h-10 w-10 bg-grey-fields-200 rounded-full flex items-center justify-center">
                  <div className="w-5 h-5 bg-grey-loader animate-pulse rounded-full" />
                </figure>
                <div className="flex flex-col items-start flex-wrap flex-1 mr-3.75">
                  <div className="h-5 bg-grey-loader animate-pulse rounded-10 w-32 mb-1" />
                  <div className="h-4 bg-grey-loader animate-pulse rounded-10 w-24" />
                </div>
              </div>
              <div className="flex gap-2 items-center">
                <div className="w-9 h-9 bg-grey-loader animate-pulse rounded-full" />
                <div className="w-9 h-9 bg-grey-loader animate-pulse rounded-full" />
              </div>
            </div>
            <div className="flex-1 w-full h-full py-3 my-0 border-t border-gray-border border-opacity-50">
              <div className="flex items-center gap-x-2">
                <figure className="h-9 w-9 p-0.5 border border-gray-border border-opacity-50 rounded-10 flex items-center justify-center">
                  <div className="w-full h-full bg-grey-loader animate-pulse rounded-10" />
                </figure>
                <div className="h-4 bg-grey-loader animate-pulse rounded-10 w-28" />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default LoadingPlaceholder;
