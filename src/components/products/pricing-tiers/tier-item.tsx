import { getFieldvalues } from "@/assets/js/utils/functions";
import useClickOutside from "@/components/hooks/useClickOutside";
import { RoundActionBtn } from "@/components/ui/buttons";
import React, { useRef, useState, useEffect } from "react";
import { DiscountShape, Layer } from "iconsax-react";
import { validateTierProgression } from "./pricing-tier-form";
import { InputField } from "@/components/ui/form-elements";

interface TierFormProps {
  index: number;
  form: any;
  removeTier: (e: React.MouseEvent<HTMLButtonElement>, index: number) => void;
  toggleEditing: (index: number, toggle?: boolean) => void;
}

export const PricingTierItemsForm: React.FC<TierFormProps> = ({ index, form, removeTier, toggleEditing }) => {
  const tierForm = useRef(null);
  const [validationError, setValidationError] = useState<string | null>(null);

  const validateCurrentTier = (currentIndex: number, newValue: any) => {
    const tiers = [...form.values.tiers];
    tiers[currentIndex] = newValue;
    return validateTierProgression(tiers);
  };

  // Auto-dismiss error after 3 seconds
  useEffect(() => {
    if (validationError) {
      const timer = setTimeout(() => {
        setValidationError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [validationError]);

  useClickOutside(tierForm, () => {
    if (form.values.tiers[index].minimum_quantity && form.values.tiers[index].discount_percentage) {
      // Validate before closing
      const error = validateCurrentTier(index, form.values.tiers[index]);
      if (error) {
        setValidationError(error);
        return; // Don't close if validation fails
      }
      setValidationError(null);
      toggleEditing(index, false);
    }
  });

  return (
    <div className="relative h-full" ref={tierForm}>
      <div className="flex flex-col gap-2 bg-grey-fields-100 items-center rounded-15 relative h-full">
        <div className="flex justify-between items-center w-full px-3 pt-2.5">
          <div className="flex space-x-2 justify-start items-center flex-wrap flex-1 mr-3.75">
            <p
              className={`text-[#515151] font-medium text-sm overflow-hidden whitespace-nowrap overflow-ellipsis max-w-[80%]`}
            >
              Tier {index + 1}
            </p>
          </div>
          <div className="flex gap-1 items-center">
            {index > 0 && (
              <RoundActionBtn
                icon="delete"
                white
                size="sm"
                grey={false}
                disabled={index == 0}
                className="bg-white text-accent-red-500"
                onClick={(e) => removeTier(e, index)}
              />
            )}
          </div>
        </div>

        <div className="flex-1 w-full h-full border rounded-15 border-gray-border border-opacity-50 bg-white p-4.5 my-0 ">
          <div className="grid grid-cols-2 gap-x-1">
            <InputField
              className="flex-1"
              label="Minimum Quantity"
              type="number"
              {...getFieldvalues(`tiers.${index}.minimum_quantity`, form)}
            />
            <InputField
              className="flex-1 !mt-0"
              label="Discount Percentage"
              type="number"
              {...getFieldvalues(`tiers.${index}.discount_percentage`, form)}
            />
          </div>
          {validationError && (
            <div className="mt-2">
              <p className="text-xs text-accent-red-500">{validationError}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

interface TierSummaryProps {
  quantity: string;
  discount: string;
  index: number;
  editing: boolean[];
  toggleEditing: (index: number, toggle?: boolean) => void;
  deleteAction: (id: number) => void;
  canModify?: boolean;
}

export const PricingTierItemsSummary: React.FC<TierSummaryProps> = ({
  quantity,
  discount,
  index,
  toggleEditing,
  editing,
  deleteAction,
  canModify = true,
}) => {
  const pricingTierBox = useRef(null);
  useClickOutside(pricingTierBox, () => {
    if (editing[index] && canModify) {
      toggleEditing(index, false);
    }
  });

  return (
    <>
      <div className="relative h-full" ref={pricingTierBox}>
        <div className="flex flex-col gap-2 bg-grey-fields-100 items-center rounded-15 relative h-full">
          <div className="flex justify-between items-center w-full px-3 pt-2.5">
            <div className="flex space-x-2 justify-start items-center flex-wrap flex-1 mr-3.75">
              <p
                className={`text-[#515151] font-medium text-sm overflow-hidden whitespace-nowrap overflow-ellipsis max-w-[80%]`}
              >
                Tier {index + 1}
              </p>
            </div>
            {canModify && (
              <div className="flex gap-1 items-center">
                <RoundActionBtn
                  icon="edit"
                  white
                  size="sm"
                  grey={false}
                  className="bg-white text-black-secondary"
                  onClick={() => toggleEditing(index)}
                />
                {index > 0 && (
                  <RoundActionBtn
                    icon="delete"
                    white
                    size="sm"
                    grey={false}
                    className="bg-white text-accent-red-500"
                    onClick={() => deleteAction(index)}
                  />
                )}
              </div>
            )}
          </div>

          <div className="flex-1 divide-x divide-gray-border divide-opacity-50 flex gap-2 gap-y-3.75 rounded-15  w-full h-full border border-gray-border border-opacity-50 bg-white p-3 my-0 ">
            <div className="flex-1  flex gap-3 pr-3">
              <figure className="h-9 w-9 rounded-full overflow-hidden flex items-center justify-center flex-shrink-0 bg-grey-fields-100 text-[#AAAAAA]">
                <Layer size={20} variant="Bold" />
              </figure>
              <div className="flex flex-col gap-y-0.5 flex-1 items-start overflow-hidden">
                <span className="text-dark text-xs">Quantity</span>
                <p className="text-black font-medium overflow-hidden text-sm max-w-full whitespace-nowrap overflow-ellipsis leading-tight">
                  {quantity}
                </p>
              </div>
            </div>

            <div className="flex-1 flex gap-3 pl-3">
              <figure className="h-9 w-9 rounded-full overflow-hidden flex items-center justify-center flex-shrink-0 bg-grey-fields-100 text-[#AAAAAA]">
                <DiscountShape size={20} variant="Bold" />
              </figure>
              <div className="flex flex-col gap-y-0.5 flex-1 items-start overflow-hidden">
                <span className="text-dark text-xs">Discount</span>
                <p className="text-black font-medium overflow-hidden text-sm max-w-full whitespace-nowrap overflow-ellipsis leading-tight">
                  {discount}%
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
