// import EmojiPicker from "emoji-picker-react";
import dynamic from "next/dynamic";
import React, { useEffect, useRef } from "react";
import { useState } from "react";
import { CreateStoreInfoBlocksParams } from "@/api/interfaces/stores.interface";
import { CreateStoreInfoBlock } from "@/api/stores";
import { RequestInterface, useFetcher, useRequest } from "@/api/utils";
import { PricingTierInterface, ProductItemInterface } from "@/assets/interfaces";
import ErrorLabel from "@/components/ui/error-label";
import SuccessLabel from "@/components/ui/success-label";
import { toast } from "@/components/ui/toast";
import { InputField } from "@/components/ui/form-elements";
import { useFormik } from "formik";
import * as Yup from "yup";
import { getFieldvalues } from "@/assets/js/utils/functions";
import LazyImage from "../../lazy-image";
import { PricingTierItemsForm, PricingTierItemsSummary } from "./tier-item";
import { useModals } from "@/components/hooks/useModals";
import { CreateTieredPricingParams, UpdateTieredPricingParams } from "@/api/interfaces";
import { CreateTieredPricing, UpdateTieredPricing } from "@/api";
import { getItemThumbnail, getSelectedProductText } from "@/assets/js/utils/utils";
import Portal from "@/components/portal";
import SelectSpecificProductsModal from "../modals/select-specific-products";
import { Add, Edit2, InfoCircle } from "iconsax-react";
import Toggle from "@/components/ui/toggle";

interface Props {
  submitBtnRef: React.MutableRefObject<HTMLButtonElement>;
  setIsLoading?: (val: boolean) => void;
  onSuccess?: () => void;
  updateTiers: (tier: PricingTierInterface) => void;
  allItems: ProductItemInterface[];
  fetchItemsReq: RequestInterface<any>;
  getItem: (id: string) => ProductItemInterface | undefined;
  // Edit mode props
  initialData?: PricingTierInterface;
  isEditing?: boolean;
  setFormDirty?: (dirty: boolean) => void;
  toggle: (show: boolean) => void;
  fromProductForm?: boolean;
}

const PricingTierForm: React.FC<Props> = ({
  submitBtnRef,
  setIsLoading,
  updateTiers,
  allItems,
  fetchItemsReq,
  getItem,
  initialData,
  setFormDirty,
  toggle,
  fromProductForm,
}) => {
  const isEditing = !!initialData;
  const {
    isLoading,
    makeRequest,
    error,
    response: successResponse,
  } = useRequest<CreateTieredPricingParams | UpdateTieredPricingParams>(
    isEditing ? UpdateTieredPricing : CreateTieredPricing
  );
  const [errorText, setErrorText] = useState<string>(null);
  const [editing, setEditing] = useState<boolean[]>([true]);
  const formRef = useRef<HTMLFormElement>(null);
  const { modals, toggleModal } = useModals(["selectSpecificProducts"]);

  useEffect(() => {
    setIsLoading && setIsLoading(isLoading);
  }, [isLoading]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    form.submitForm();
  };

  const form = useFormik<CreateTieredPricingParams | UpdateTieredPricingParams>({
    initialValues: initialData || {
      label: "",
      active: true,
      items: [],
      tiers: [
        {
          minimum_quantity: 5,
          discount_percentage: 10,
        },
      ],
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        // Additional validation for tier progression
        const tierValidationError = validateTierProgression(values.tiers);
        if (tierValidationError) {
          setErrorText(tierValidationError);
          formRef.current?.scrollTo({ top: 0, behavior: "smooth" });
          return;
        }

        let response, error;

        if (isEditing && initialData) {
          [response, error] = await makeRequest({
            id: initialData.id,
            ...values,
          });
        } else {
          [response, error] = await makeRequest(values);
        }

        if (error) {
          if (error.fields && Object.keys(error.fields).length > 0) {
            form.setErrors(error.fields);
          } else {
            setErrorText(error.message || `Failed to ${isEditing ? "update" : "create"} tiered pricing rule`);
          }
        } else {
          updateTiers(response.data);
          toast.success({
            title: "Success",
            message: `Tiered pricing rule ${isEditing ? "updated" : "created"} successfully!`,
          });
          if (!isEditing) {
            form.resetForm();
          }
          toggle(false);
        }
      } catch (err) {
        setErrorText("An unexpected error occurred");
      }
    },
    enableReinitialize: isEditing, // Reinitialize form when editing
  });

  // Track form dirty state for edit mode
  useEffect(() => {
    if (setFormDirty) {
      setFormDirty(form.dirty);
    }
  }, [form.dirty, setFormDirty]);

  const selectedItems = form.values.items;

  const handleItemsSelect = (items: string[]) => {
    form.setFieldValue("items", items);
    if (form.values.tiers.length === 0) {
      form.setFieldValue("tiers", [
        {
          minimum_quantity: 5,
          discount_percentage: 10,
        },
      ]);
    }
    toggleModal("selectSpecificProducts");
  };

  const toggleEditing = (index: number) => {
    const editingCopy = [...editing];

    editingCopy[index] = !editing[index];
    setEditing(editingCopy);
  };
  const deleteTier = (id: number) => {
    const tiers = [...form.values.tiers];
    tiers.splice(id, 1);
    form.setFieldValue("tiers", tiers);
  };

  const removeTier = (e: React.MouseEvent<HTMLButtonElement>, index: number) => {
    const tiers = [...form.values.tiers];
    tiers.splice(index, 1);
    form.setFieldValue("tiers", tiers);
  };

  const addNewTier = () => {
    const lastTier = form.values.tiers[form.values.tiers.length - 1];

    if (lastTier.minimum_quantity < 1 || lastTier.discount_percentage < 1) {
      toast.error({ message: "Please fill in the previous tier first", title: "Error" });
      return;
    }

    // Validate that the last tier has valid progression values
    if (form.values.tiers.length > 1) {
      const previousTier = form.values.tiers[form.values.tiers.length - 2];
      if (lastTier.minimum_quantity <= previousTier.minimum_quantity) {
        toast.error({
          message: `Minimum quantity must be greater than ${previousTier.minimum_quantity}`,
          title: "Invalid Tier Progression",
        });
        return;
      }
      if (lastTier.discount_percentage <= previousTier.discount_percentage) {
        toast.error({
          message: `Discount percentage must be greater than ${previousTier.discount_percentage}%`,
          title: "Invalid Tier Progression",
        });
        return;
      }
    }

    const newTiers = [
      ...form.values.tiers,
      {
        minimum_quantity: lastTier ? lastTier?.minimum_quantity + 5 : 5,
        discount_percentage: 0,
      },
    ];
    form.setFieldValue("tiers", newTiers);
    toggleEditing(newTiers.length - 1);
  };

  const getItemImage = (product: string) => {
    const item = getItem(product);
    return getItemThumbnail(item);
  };

  return (
    <>
      <form onSubmit={handleSubmit} className="w-full pb-20" ref={formRef}>
        <ErrorLabel error={errorText || error?.message} />
        <SuccessLabel
          message={successResponse ? `Pricing Tier ${isEditing ? "Updated" : "Created"} successfully!` : ""}
        />
        {isEditing && (
          <div className="bg-grey-fields-100 p-2 rounded-lg text-placeholder font-medium text-xs flex items-center mb-5">
            <figure className="h-8 w-8 rounded-full bg-white flex items-center justify-center text-accent-yellow-500 flex-shrink-0 mr-1.5">
              <InfoCircle variant="Bold" size={18} />
            </figure>
            <span>
              Editing the pricing tier will update the pricing tier for all products linked to it. If you don't want
              this create a new pricing tier.
            </span>
          </div>
        )}
        <div className="flex flex-col items-start gap-3.75">
          <InputField label={`Label e.g Clothing Wholesale Prices`} {...getFieldvalues("label", form)} />

          {/* Activity Toggle - only show in edit mode */}
          {isEditing && !fromProductForm && (
            <div className="w-full flex items-center justify-between py-2">
              <span className="text-sm font-medium text-black">Active Status</span>
              <Toggle
                intialState={form.values.active}
                onChange={(state) => form.setFieldValue("active", state)}
                size="sm"
                onLabel="Active"
                offLabel="Inactive"
              />
            </div>
          )}

          {allItems.length > 0 && (
            <>
              {selectedItems.length == 0 && !fromProductForm && (
                <div
                  className="w-full bg-grey-fields-100 py-2.5 px-4 rounded-[12px] cursor-pointer"
                  role="button"
                  onClick={() => toggleModal("selectSpecificProducts")}
                >
                  <span className="text-1xs sm:text-sm text-black font-medium inline-block mr-3">
                    Assign Products(Optional)
                  </span>
                  <button className={`rounded-lg bg-white text-sm p-1 px-2`} type="button">
                    <span className="text-1xs sm:text-sm block text-primary-500 font-medium">Click Here</span>
                  </button>
                </div>
              )}

              {selectedItems.length > 0 && (
                <div className="flex w-full flex-col bg-grey-fields-200 rounded-10">
                  <div className="flex flex-col gap-y-2 px-3 md:px-3.75 py-2 md:py-3 items-center rounded-t-10 justify-between cursor-pointer">
                    <div className="flex justify-between w-full">
                      <h5 className="font-medium text-1xs md:text-sm text-black-placeholder">Selected Products</h5>

                      <div className="flex gap-3">
                        {!fromProductForm && (
                          <button
                            onClick={() => toggleModal("selectSpecificProducts")}
                            type="button"
                            className="rounded-full flex items-center p-0 font-medium text-primary-500 text-xs lg:text-1xs flex-shrink-0"
                          >
                            <Edit2 size={16} />
                            <span className="ml-1.5">Edit Selection</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col px-3 md:px-3.75 py-3 md:py-3 rounded-10 bg-white cursor-pointer border border-grey-border border-opacity-50 gap-y-2.5">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center cursor-pointer overflow-hidden">
                        <figure className="h-[33px] w-[33px] sm:h-10 sm:w-10 rounded-8 overflow-hidden flex items-center justify-center flex-shrink-0">
                          <LazyImage
                            className="h-full w-full object-cover rounded-md relative z-10"
                            src={getItemImage(selectedItems[0])}
                          />
                        </figure>
                        <div className="ml-2 flex flex-col gap-y-1 flex-1 items-center overflow-hidden">
                          <p className="text-black-secondary font-medium overflow-hidden text-sm max-w-full whitespace-nowrap overflow-ellipsis leading-tight">
                            {getSelectedProductText(
                              selectedItems.map((i) => i),
                              allItems
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          <hr className="w-full border-gray-border border-opacity-50" />

          <div className="flex flex-col gap-4 w-full">
            <PricingTierItemsSummary
              index={-1}
              {...{ toggleEditing, editing, deleteAction: deleteTier }}
              quantity={`1 - ${form.values.tiers[0] ? form.values.tiers[0].minimum_quantity - 1 : "Unlimited"} Items`}
              discount={"0"}
              canModify={false}
            />
            {form.values.tiers.map((tier, _i) => (
              <div key={_i} className="flex flex-col">
                {editing[_i] ? (
                  <PricingTierItemsForm index={_i} form={form} removeTier={removeTier} toggleEditing={toggleEditing} />
                ) : (
                  <PricingTierItemsSummary
                    index={_i}
                    {...{ toggleEditing, editing, deleteAction: deleteTier }}
                    quantity={`${tier.minimum_quantity} - ${
                      form.values?.tiers[_i + 1]?.minimum_quantity > 1
                        ? form.values.tiers[_i + 1].minimum_quantity - 1
                        : "Unlimited"
                    } Items`}
                    discount={tier.discount_percentage.toString()}
                  />
                )}
              </div>
            ))}
          </div>

          <button
            className="text-primary-500 text-sm font-medium inline-flex items-center gap-0.5"
            type="button"
            onClick={addNewTier}
          >
            <Add size={20} />
            <span className="text-sm">Add New Tier</span>
          </button>
        </div>

        <button type="submit" className="hidden" ref={submitBtnRef}></button>
      </form>

      <Portal>
        <SelectSpecificProductsModal
          onSearch={() => {}}
          onSave={handleItemsSelect}
          items={allItems}
          show={modals.selectSpecificProducts.show}
          toggle={() => toggleModal("selectSpecificProducts")}
          value={selectedItems.map((i) => i)}
          loadingItems={fetchItemsReq?.isLoading}
        />
      </Portal>
    </>
  );
};

export const validateTierProgression = (tiers: any[]): string | null => {
  if (tiers.length <= 1) return null;

  for (let i = 1; i < tiers.length; i++) {
    const currentTier = tiers[i];
    const previousTier = tiers[i - 1];

    if (currentTier.minimum_quantity <= previousTier.minimum_quantity) {
      return `Tier ${i + 1} minimum quantity must be greater than Tier ${i} minimum quantity`;
    }

    if (currentTier.discount_percentage <= previousTier.discount_percentage) {
      return `Tier ${i + 1} discount percentage must be greater than Tier ${i} discount percentage`;
    }
  }

  return null;
};

// Validation schema
const validationSchema = Yup.object().shape({
  label: Yup.string().required("Label is required"),
  tiers: Yup.array()
    .of(
      Yup.object().shape({
        minimum_quantity: Yup.number()
          .min(1, "Minimum quantity must be at least 1")
          .required("Minimum quantity is required"),
        discount_percentage: Yup.number()
          .min(1, "Discount percentage must be at least 1")
          .max(100, "Discount percentage cannot exceed 100")
          .required("Discount percentage is required"),
      })
    )
    .min(1, "At least one tier is required"),
});

export default PricingTierForm;
