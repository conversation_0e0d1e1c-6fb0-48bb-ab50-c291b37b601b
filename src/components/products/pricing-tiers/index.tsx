import { useEffect, useMemo, useRef, useState } from "react";
import {
  Category,
  InfoBlockInterface,
  PricingTierInterface,
  ProductItemInterface,
  StrippedItem,
} from "../../../assets/interfaces";
import { emit, useListenerState } from "../../hooks/useListener";
import { StoreProductCategories } from "../../store";
import { AppBtn, RoundActionBtn } from "../../ui/buttons";
import { productPageIcons } from "../../ui/layouts/product";
import Modal, { ModalBody, ModalFooter } from "../../ui/modal";
import Table, { TableBody, TableCell, TableHead, TableHeadItem, TableRow } from "../../ui/table";
import authContext from "../../../contexts/auth-context";
import ContentState from "../../ui/content-state";
import { useFetcher, useRequest } from "../../../api/utils";
import { GetTieredPricing, DeleteTieredPricing, AssignItems, RemoveItems, GetAssignedItems } from "../../../api";
import RoundActionButton from "../../ui/buttons/round-action-btn";
import useCopyClipboard from "../../hooks/useCopyClipboard";
import { enumToHumanFriendly, subdomainStoreLink, toAppUrl, toCurrency } from "../../../assets/js/utils/functions";
import useSearchParams from "../../hooks/useSearchParams";
import Portal from "../../portal";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import ErrorBox from "../../ui/error";
import CreateInfoBlocks from "./pricing-tier-form";
import { useFormik } from "formik";
import ErrorLabel from "../../ui/error-label";
import SuccessLabel from "../../ui/success-label";
import { ModalState, useModals } from "../../hooks/useModals";

import LazyImage from "../../lazy-image";
import {
  AssignItemsParams,
  DeleteStoreInfoBlockParams,
  UpdateStoreInfoBlocksParams,
  GetTieredPricingParams,
} from "@/api/interfaces";
import Badge from "../../ui/badge";
import Dropdown, { DropdownItem } from "@/components/ui/dropdown-new";
import ProductsSelectorModal from "@/components/products/modals/select-products-modal";
import AssignedItemsModal from "./modals/assigned-items";
import { toast } from "@/components/ui/toast";
import CreatePricingTier from "./pricing-tier-form";
import { Layer, Stacks } from "iconsax-react";
import Toggle from "@/components/ui/toggle";
import PricingTierModal from "./modals/view-tier-pricing";
import { useStorefrontItems } from "@/components/hooks/useStoreItems";
import { getItemThumbnail } from "@/assets/js/utils/utils";

// Import new components
import { TierInfo, CreateTierModal, EditTierModal, DeleteTierModal, LoadingPlaceholder } from "./imports";

interface Props {
  modals: ModalState;
  toggleModal: (modal: string) => void;
}

const PricingTiersMain: React.FC<Props> = ({ modals, toggleModal }) => {
  const { store, subscription } = authContext.useContainer();
  const { search } = useSearchParams(["search"]);
  const [pricingTiers, setPricingTiers] = useState<any[]>([]);
  const { items: storeItems, fetchItemsReq, getItem } = useStorefrontItems(store?.id);

  const [selected, setSelected] = useState<{
    edit: number;
    delete: number;
    assignToItems: number;
    assignedItems: number;
    view: number;
  }>({
    edit: null,
    delete: null,
    assignToItems: null,
    assignedItems: null,
    view: null,
  });

  let canManageBlocks = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS,
  });

  const { response, isLoading, error, makeRequest } = useFetcher(GetTieredPricing, {
    filter: {},
    page: 1,
    per_page: 50,
  });

  const filteredTiers = useMemo(() => {
    if (search && search !== null && search !== "") {
      const query = search.toLowerCase();
      return pricingTiers.filter(
        (tier) => tier.label.toLowerCase().includes(query) // Match label
      );
    }
    return pricingTiers; // Return all tiers if no search query
  }, [search, pricingTiers]);

  useEffect(() => {
    if (response?.data) {
      setPricingTiers(response?.data);
    }
  }, [response]);

  const addNewTier = (tier: PricingTierInterface) => {
    setPricingTiers((prevTiers) => [tier, ...prevTiers]);
  };

  const removeTier = (index: number) => {
    setPricingTiers(pricingTiers.filter((_, i) => i !== index));
  };

  const updateTier = (data: Partial<PricingTierInterface>, index: number) => {
    const tiers = [...pricingTiers];
    tiers[index] = { ...tiers[index], ...data };
    setPricingTiers(tiers);
  };

  const clearSearch = () => {
    const params = new URLSearchParams(window.location.search);
    params.delete("search");
    window.history.replaceState({}, "", `${window.location.pathname}`);
  };

  const openEditModal = (index: number) => {
    setSelected((prev) => ({
      ...prev,
      edit: index,
    }));

    toggleModal("edit");
  };

  const openDeleteModal = (index: number) => {
    setSelected((prev) => ({
      ...prev,
      delete: index,
    }));

    toggleModal("delete");
  };

  const openViewModal = (index: number) => {
    setSelected((prev) => ({
      ...prev,
      view: index,
    }));
    toggleModal("view");
  };

  const openAssignedItemsModal = (index: number) => {
    setSelected((prev) => ({
      ...prev,
      assignedItems: index,
    }));

    toggleModal("assigned_items");
  };

  if (!canManageBlocks) {
    return (
      <ErrorBox title="Upgrade required" message="Upgrade to the basic or business plus plan to manage pricing tiers">
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  return (
    <>
      <div className="pb-20">
        {isLoading ? (
          <ContentState
            isLoading={isLoading}
            loadingText="Loading pricing tiers..."
            loadingPlaceholder={<LoadingPlaceholder />}
          />
        ) : error ? (
          <ContentState
            errorTitle="Failed to load pricing tiers"
            error={error}
            errorMessage="We couldn't load your pricing tiers please reload page"
            errorAction={
              <AppBtn size="md" onClick={() => makeRequest()}>
                Reload Pricing Tiers
              </AppBtn>
            }
          />
        ) : !pricingTiers || pricingTiers.length < 1 || !filteredTiers || filteredTiers.length < 1 ? (
          <>
            <ContentState
              isLoading={isLoading}
              loadingText="Loading pricing tiers..."
              errorTitle="Failed to load pricing tiers"
              error={error}
              errorMessage="We couldn't load your pricing tiers please reload page"
              errorAction={
                <AppBtn size="md" onClick={() => makeRequest()}>
                  Reload Pricing Tiers
                </AppBtn>
              }
              isEmpty={filteredTiers.length < 1}
              emptyIcon={
                <div className="w-8.75 h-8.75 text-grey-muted flex items-center justify-center">
                  <Layer size={30} variant="Bulk" />
                </div>
              }
              title="No pricing tiers"
              description={
                search && search !== null && search !== ""
                  ? "Your Search Results didn't yield any matches"
                  : "Create a pricing tier"
              }
              loadingPlaceholder={<LoadingPlaceholder />}
            >
              {!search || search == null || search === "" ? (
                <AppBtn onClick={() => toggleModal("create")} size="md" className="max-w-[240px] m-auto">
                  Create Pricing Tier
                </AppBtn>
              ) : (
                <AppBtn onClick={() => clearSearch()} size="md" className="max-w-[240px] m-auto">
                  Clear Search Results
                </AppBtn>
              )}
            </ContentState>
          </>
        ) : (
          <div className="grid grid-cols-[repeat(auto-fill,minmax(300px,1fr))] gap-5">
            {filteredTiers.map((tier, i) => (
              <TierInfo
                key={i}
                data={tier}
                deleteAction={() => openDeleteModal(i)}
                editAction={() => openEditModal(i)}
                viewAction={() => openViewModal(i)}
                firstItem={tier.items.length > 0 ? getItem(tier.items[0]) : null}
                updateTier={(data: Partial<PricingTierInterface>) => updateTier(data, i)}
              />
            ))}
          </div>
        )}
      </div>
      <Portal>
        {selected.view !== null && (
          <PricingTierModal
            show={modals?.view.show}
            toggle={() => toggleModal("view")}
            rule={pricingTiers[selected.view]}
            editTier={() => openEditModal(selected.view)}
            viewItems={() => openAssignedItemsModal(selected.view)}
          />
        )}
        {selected.edit !== null && (
          <EditTierModal
            show={modals?.edit.show}
            toggle={() => toggleModal("edit")}
            tier={pricingTiers[selected.edit]}
            updateTier={(tier: Partial<PricingTierInterface>) => updateTier(tier, selected.edit)}
            allItems={storeItems}
            fetchItemsReq={fetchItemsReq}
            getItem={getItem}
          />
        )}
        {selected.delete !== null && (
          <DeleteTierModal
            show={modals?.delete.show}
            toggle={() => toggleModal("delete")}
            tier={pricingTiers[selected.delete]}
            removeTier={(block: any) => {
              removeTier(selected.delete);
            }}
            tiers={pricingTiers}
          />
        )}

        {selected.assignedItems !== null && (
          <AssignedItemsModal
            show={modals.assigned_items.show}
            toggle={() => toggleModal("assigned_items")}
            items={pricingTiers[selected.assignedItems].items}
            formatAsCurrency={toCurrency}
            allItems={storeItems}
            fetchItemsReq={fetchItemsReq}
            getItem={getItem}
            tierId={pricingTiers[selected.assignedItems]?.id}
            updateTier={(data: Partial<PricingTierInterface>) => updateTier(data, selected.assignedItems)}
          />
        )}

        <CreateTierModal
          show={modals?.create.show}
          toggle={() => toggleModal("create")}
          updateTiers={(tier: any) => addNewTier(tier)}
          allItems={storeItems}
          fetchItemsReq={fetchItemsReq}
          getItem={getItem}
        />
      </Portal>
    </>
  );
};

export default PricingTiersMain;
