import { GetStoreItems, AssignItems } from "@/api";
import { GetStoreItemsParams, AssignItemsParams } from "@/api/interfaces";
import { RequestInterface, useFetcher, useRequest } from "@/api/utils";
import {
  CartInterface,
  CartItem,
  CURRENCIES,
  PricingTierInterface,
  ProductItemInterface,
  StrippedItem,
} from "@/assets/interfaces";
import { checkItemAvailability, getActualPrice, getItemThumbnail } from "@/assets/js/utils/utils";
import { useModals } from "@/components/hooks/useModals";
import useStoreItems, { useStorefrontItems } from "@/components/hooks/useStoreItems";
import LazyImage from "@/components/lazy-image";
import Portal from "@/components/portal";
import { AppBtn } from "@/components/ui/buttons";
import BaseBtn from "@/components/ui/buttons/base-btn";
import ContentState from "@/components/ui/content-state";
import Checkbox from "@/components/ui/form-elements/checkbox";
import ProductsSelectorModal from "@/components/ui/form-elements/products-selector-modal";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import SearchBar from "@/components/ui/search-bar-new";
import { FormatCurrencyFun } from "@/contexts/cart-context";
import { toast } from "@/components/ui/toast";

import React, { useEffect, useState } from "react";
import SelectSpecificProductsModal from "../../modals/select-specific-products";
import authContext from "@/contexts/auth-context";

interface Props {
  show: boolean;
  toggle: (show: boolean) => void;
  items: string[];
  formatAsCurrency: FormatCurrencyFun;
  allItems: ProductItemInterface[];
  fetchItemsReq: RequestInterface<any>;
  getItem: (id: string) => ProductItemInterface | undefined;
  tierId: string;
  updateTier: (data: Partial<PricingTierInterface>) => void;
  fromProductForm?: boolean;
}

const AssignedItemsModal: React.FC<Props> = ({
  show,
  toggle,
  items = [],
  formatAsCurrency,
  allItems,
  fetchItemsReq,
  getItem,
  tierId,
  updateTier,
  fromProductForm,
}) => {
  const [selectedItems, setSelectedItems] = useState<string[]>(items);
  const { modals, toggleModal } = useModals(["select_items"]);

  const {
    isLoading: assignLoading,
    makeRequest: makeAssignRequest,
    error: assignError,
  } = useRequest<AssignItemsParams>(AssignItems);

  useEffect(() => {
    if (items?.length < 1 && show) {
      toggle(false);
    }

    setSelectedItems(items);
  }, [items, show]);

  const handleAssignItems = async (newItems: string[]) => {
    // Update local state immediately for better UX
    setSelectedItems(newItems);
    toggleModal("select_items");
  };

  const handleRemoveItem = (itemId: string) => {
    // Only update local state, don't make API call
    setSelectedItems(selectedItems.filter((id) => id !== itemId));
  };

  const handleSaveChanges = async () => {
    if (!tierId) {
      toast.error({
        title: "Error",
        message: "Tier ID is required to save changes",
      });
      return;
    }

    const [res, err] = await makeAssignRequest({
      id: tierId,
      items: selectedItems,
    });

    if (err) {
      toast.error({
        title: "Failed to save changes",
        message: err.message || "An error occurred while saving changes.",
      });
      return;
    }

    toast.success({
      title: "Changes saved successfully",
      message: "Your changes have been saved successfully.",
    });

    updateTier({ items: selectedItems });

    toggle(false);
  };

  return (
    <Portal>
      <Modal show={show} toggle={toggle} title="Assigned Products" size="md">
        <ModalBody className="relative !pt-0 p-5 sm:p-6" noPadding>
          {allItems?.length < 1 && fetchItemsReq?.isLoading && (
            <div className="py-10 flex flex-col items-center justify-center">
              <div className="spinner text-primary spinner--sm"></div>
              <span className="text-1sm text-black-muted mt-3.75">Loading...</span>
            </div>
          )}
          {allItems && allItems?.length > 0 && (
            <>
              <p className="text-1sm text-black-muted mt-3.75">
                View and manage products assigned to this pricing tier
              </p>

              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3.75 mt-3.75">
                {selectedItems.map((id, index) => {
                  const item = getItem(id);
                  if (!item) return null;

                  return (
                    <div
                      key={index}
                      className="w-full flex items-start last:border-0 cursor-pointer"
                      onClick={() => !fromProductForm && handleRemoveItem(item.id)}
                      role="button"
                    >
                      <div className="flex flex-col items-center w-full overflow-hidden">
                        <figure className="flex-shrink-0 w-full rounded-10 overflow-hidden relative border border-grey-fields-200 pt-[100%] box-content">
                          <LazyImage
                            src={getItemThumbnail(item)}
                            className="absolute top-0 left-0 h-full w-full object-cover rounded-10 z-10"
                            alt={item.name}
                          />
                          {!fromProductForm && (
                            <button
                              type="button"
                              className="ml-auto mt-1 absolute top-1.5 right-2 bg-white rounded-full z-[10] border border-grey-divider"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveItem(item.id);
                              }}
                            >
                              {/* prettier-ignore */}
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" >
                              <rect width="24" height="24" rx="12" fill="white" />
                              <path d="M15.3337 8.66797L8.66699 15.3346M8.66699 8.66797L15.3337 15.3346" stroke="#BF0637" strokeWidth="1.15556" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            </button>
                          )}
                        </figure>
                        <div className="w-full">
                          <span className="text-sm sm:text-1sm mt-0.75 overflow-hidden whitespace-nowrap overflow-ellipsis text-dark font-bold font-display block -mb-0.5">
                            {item.name}
                          </span>
                          <span className="text-xxs md:text-xs font-bold text-black">
                            {formatAsCurrency(getActualPrice(item))}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </>
          )}
        </ModalBody>
        {!fromProductForm && (
          <ModalFooter>
            <div className="flex items-center gap-x-2.5 w-full">
              <AppBtn
                isBlock
                color="neutral"
                onClick={() => toggleModal("select_items")}
                size="lg"
                className="flex-1"
                disabled={assignLoading}
              >
                Manage Items
              </AppBtn>
              <AppBtn
                isBlock
                color="primary"
                onClick={handleSaveChanges}
                size="lg"
                className="flex-1"
                disabled={assignLoading}
              >
                {assignLoading ? "Saving..." : "Save Changes"}
              </AppBtn>
            </div>
          </ModalFooter>
        )}
      </Modal>

      <SelectSpecificProductsModal
        onSearch={() => {}}
        onSave={handleAssignItems}
        items={allItems ?? []}
        show={modals.select_items.show}
        toggle={() => toggleModal("select_items")}
        value={selectedItems}
        loadingItems={fetchItemsReq?.isLoading}
      />
    </Portal>
  );
};

export default AssignedItemsModal;
