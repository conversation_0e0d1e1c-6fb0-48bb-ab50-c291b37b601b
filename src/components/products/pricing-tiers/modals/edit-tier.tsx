import React, { useRef, useState } from "react";
import { AppBtn } from "@/components/ui/buttons";
import <PERSON>dal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import PricingTierForm from "../pricing-tier-form";
import { PricingTierInterface } from "@/assets/interfaces";

interface Edit {
  show: boolean;
  toggle: (status: boolean) => void;
  tier: PricingTierInterface;
  updateTier: (tier: PricingTierInterface) => void;
  allItems: any[];
  fetchItemsReq: any;
  getItem: (id: string) => any;
  fromProductForm?: boolean;
}

const EditTierModal: React.FC<Edit> = ({
  toggle,
  show,
  tier,
  updateTier,
  allItems,
  fetchItemsReq,
  getItem,
  fromProductForm,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [formDirty, setFormDirty] = useState(false);
  const submitBtnRef = useRef<HTMLButtonElement>(null);

  const updateAction = () => {
    submitBtnRef.current?.click();
  };

  return (
    <Modal title={`Edit Pricing Tier`} toggle={toggle} show={show} size="midi">
      <ModalBody>
        <PricingTierForm
          submitBtnRef={submitBtnRef}
          setIsLoading={setIsLoading}
          updateTiers={updateTier}
          allItems={allItems}
          fetchItemsReq={fetchItemsReq}
          getItem={getItem}
          initialData={tier}
          setFormDirty={setFormDirty}
          toggle={toggle}
          fromProductForm={fromProductForm}
        />
      </ModalBody>
      <ModalFooter>
        <AppBtn
          id="update-pricing-tier"
          isBlock
          disabled={!formDirty || isLoading}
          onClick={() => updateAction()}
          size="lg"
        >
          {isLoading ? "Updating..." : "Update Tier"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default EditTierModal;
