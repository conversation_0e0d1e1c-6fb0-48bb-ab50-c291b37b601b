import React, { useRef, useState } from "react";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { <PERSON>dalBody, ModalFooter } from "@/components/ui/modal";
import CreatePricingTier from "../pricing-tier-form";
import { PricingTierInterface, ProductItemInterface } from "@/assets/interfaces";
import { RequestInterface } from "@/api/utils";

interface ModalProps {
  toggle: (state: boolean) => void;
  show: boolean;
  updateTiers: (tier: PricingTierInterface) => void;
  allItems: ProductItemInterface[];
  fetchItemsReq: RequestInterface<any>;
  getItem: (id: string) => ProductItemInterface | undefined;
}

const CreateTierModal: React.FC<ModalProps> = ({ toggle, show, updateTiers, allItems, fetchItemsReq, getItem }) => {
  const submitBtnRef = useRef<HTMLButtonElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  const createAction = () => {
    submitBtnRef.current?.click();
  };

  return (
    <Modal title="Create Pricing Tiers" toggle={toggle} show={show} size="midi">
      <ModalBody>
        <CreatePricingTier {...{ submitBtnRef, setIsLoading, updateTiers, allItems, fetchItemsReq, getItem, toggle }} />
      </ModalBody>
      <ModalFooter>
        <AppBtn id="create-pricing-tier" isBlock disabled={isLoading} onClick={() => createAction()} size="lg">
          {isLoading ? "Creating..." : "Create Tiers"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default CreateTierModal;
