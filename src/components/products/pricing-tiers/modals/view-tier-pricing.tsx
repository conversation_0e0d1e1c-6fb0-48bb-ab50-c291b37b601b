import React from "react";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "@/components/ui/modal";
import { AppBtn } from "@/components/ui/buttons";
import { Layer, DiscountShape } from "iconsax-react";
import Badge from "@/components/ui/badge";
import { PricingTierInterface } from "@/assets/interfaces";
import { toCurrency } from "@/assets/js/utils/functions";

interface Props {
  show: boolean;
  toggle: (show: boolean) => void;
  rule: PricingTierInterface;
  editTier: () => void;
  viewItems: () => void;
  fromProductForm?: boolean;
  itemPrice?: number;
}

const PricingTierModal: React.FC<Props> = ({ show, toggle, rule, editTier, viewItems, fromProductForm, itemPrice }) => {
  // return null;
  if (!rule) return null;

  const getTierSummary = (tiers: any[]) => {
    if (!tiers || tiers.length === 0) return "No tiers";
    const minTier = tiers.reduce((min, tier) => (tier.minimum_quantity < min.minimum_quantity ? tier : min));
    const maxTier = tiers.reduce((max, tier) => (tier.discount_percentage > max.discount_percentage ? tier : max));
    return `${tiers.length} tiers (${minTier.minimum_quantity}+ qty, up to ${maxTier.discount_percentage}% off)`;
  };

  return (
    <Modal show={show} toggle={toggle} title="Pricing Tier Details" size="md">
      <ModalBody>
        <div className="space-y-6">
          {/* Basic Info */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <figure className="h-12 w-12 bg-grey-fields-200 rounded-full flex items-center justify-center text-grey-muted">
                <Layer size={24} variant="Bulk" />
              </figure>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-black">{rule.label}</h3>
                <p className="text-sm text-black-muted">{getTierSummary(rule.tiers)}</p>
              </div>
              <Badge text={rule.active ? "Active" : "Inactive"} size="md" color={rule.active ? "green" : "red"} />
            </div>
          </div>

          {/* Tiers Details */}
          <div className="space-y-3">
            <h4 className="text-base text-black">Pricing Tiers</h4>
            <DisplayTiers rule={rule} className="bg-grey-fields-100" iconBgClass="bg-white" itemPrice={itemPrice} />
          </div>

          {/* Assigned Items */}
          {fromProductForm && rule?.items?.length === 0 ? null : (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-base font-medium text-black">Assigned Items</h4>
              </div>
              {rule?.items && rule?.items?.length > 0 ? (
                <div className="p-3 bg-grey-fields-100 rounded-lg flex items-center justify-between">
                  <p className="text-sm text-black-muted">{rule.items.length} items assigned</p>
                  <button className="flex items-center cursor-pointer select-none" onClick={viewItems}>
                    <span className={`mr-2 font-semibold text-primary-500 text-1xs sm:text-sm`}>View Items</span>
                    {/* prettier-ignore */}
                    <svg viewBox="0 0 14 15" fill="none" className="w-3 transform rotate-[-45deg] -ml-1">
                    <path d="M1 7.5L13 7.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M7 13.5L13 7.5L7 1.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  </button>
                </div>
              ) : (
                <div className="p-3 bg-grey-fields-100 rounded-lg">
                  <p className="text-sm text-black-muted">No items assigned to this pricing tier</p>
                </div>
              )}
            </div>
          )}
        </div>
      </ModalBody>
      <ModalFooter>
        <div className="flex items-center gap-x-2.5 w-full">
          {!fromProductForm && (
            <AppBtn isBlock color="neutral" onClick={() => {}} size="lg" className="flex-1 text-accent-red-500">
              Delete Tier
            </AppBtn>
          )}
          <AppBtn isBlock color="primary" onClick={editTier} size="lg" className="flex-1">
            Edit Tier
          </AppBtn>
        </div>
      </ModalFooter>
    </Modal>
  );
};

interface DisplayTiersProps {
  rule: PricingTierInterface;
  className?: string;
  iconBgClass?: string;
  itemPrice?: number;
}

export const DisplayTiers: React.FC<DisplayTiersProps> = ({ rule, className, iconBgClass, itemPrice }) => {
  return (
    <div className={`rounded-15 divide-y divide-grey-border divide-opacity-50 overflow-hidden ${className}`}>
      <div className="flex items-center justify-between p-3">
        <div className="flex items-center gap-3">
          <figure
            className={`h-8 w-8 rounded-full flex items-center justify-center text-accent-green-500 ${iconBgClass}`}
          >
            <DiscountShape size={20} variant="Bold" />
          </figure>
          <div>
            <p className="text-1xs text-black-muted">1 - {rule.tiers[0].minimum_quantity - 1} items</p>
            <p className="text-sm font-medium text-black">
              No Discount {itemPrice ? `(${toCurrency(itemPrice)})` : ""}
            </p>
          </div>
        </div>
      </div>
      {rule.tiers?.map((tier: any, index: number) => (
        <div key={index} className="flex items-center justify-between p-3">
          <div className="flex items-center gap-3">
            <figure
              className={`h-8 w-8 rounded-full flex items-center justify-center text-accent-green-500 ${iconBgClass}`}
            >
              <DiscountShape size={20} variant="Bold" />
            </figure>
            <div>
              <p className="text-1xs font-medium text-black-muted">
                {tier.minimum_quantity} - {rule.tiers[index + 1]?.minimum_quantity - 1 || "Unlimited"} items
              </p>
              <p className="text-sm font-medium text-black">
                {tier.discount_percentage}% Discount{" "}
                {itemPrice ? `(${toCurrency(itemPrice * (1 - tier.discount_percentage / 100))})` : ""}
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
export default PricingTierModal;
