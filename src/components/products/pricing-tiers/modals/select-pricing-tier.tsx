import LazyImage from "@/components/lazy-image";
import { InfoBlockInterface, PricingTierInterface, ProductItemInterface } from "@/assets/interfaces";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import { ModalState, useModals } from "@/components/hooks/useModals";
import Portal from "@/components/portal";
import CreatePricingTier from "./create-tier";
import { DisplayTiers } from "./view-tier-pricing";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  allTiers: PricingTierInterface[];
  productTier: PricingTierInterface;
  setProductTier: (tier: PricingTierInterface) => void; //set all blocks in product
  openCreatePricingTierModal: () => void;
}

const SelectPricingTier: React.FC<Props> = ({
  show,
  toggle,
  setProductTier,
  allTiers,
  productTier,
  openCreatePricingTierModal,
}) => {
  return (
    <>
      <Modal {...{ show, toggle }} title="Add Pricing Tiers" size="midi">
        <ModalBody>
          <p className="text-black-muted text-xs sm:text-1xs mb-3.75">
            Give discounts for bulk purchases. Choose from existing pricing tiers or create a new one
          </p>
          <AppBtn isBlock color="neutral" onClick={openCreatePricingTierModal}>
            + Create New Pricing Tier
          </AppBtn>
          <hr className="w-full border-gray-border border-opacity-50 my-3.75" />
          {allTiers && allTiers?.length > 0 && (
            <div className="flex flex-col gap-3 my-3.75">
              {allTiers?.map((tier, i) => (
                <PricingTier
                  key={i}
                  index={i}
                  data={tier}
                  selectTier={() => setProductTier(tier)}
                  isSelected={productTier?.id === tier.id}
                />
              ))}
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <AppBtn isBlock color="primary" onClick={() => toggle(false)} size="lg" disabled={!productTier}>
            Proceed with Selections
          </AppBtn>
        </ModalFooter>
      </Modal>
    </>
  );
};

interface TierItemProps {
  data?: PricingTierInterface;
  index: number;
  isSelected: boolean;
  selectTier: VoidFunction;
}

const PricingTier: React.FC<TierItemProps> = ({ data, selectTier, index, isSelected }) => {
  return (
    <div className="bg-grey-fields-100 rounded-t-15">
      <div className="flex items-center justify-between p-3 w-full">
        <div className="flex-1">
          <span className="text-black font-medium text-sm inline-block overflow-hidden whitespace-nowrap overflow-ellipsis max-w-[85%]">
            {data.label}
          </span>
        </div>

        <button className="h-4.5 w-4.5 sm:w-5 sm:h-5 rounded-full bg-grey-fields-100" onClick={selectTier}>
          {isSelected && ( // prettier-ignore
            <svg width="100%" viewBox="0 0 18 18" fill="none">
              <rect width="18" height="18" rx="9" fill="#39B588" />
              <path
                d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z"
                fill="white"
              />
            </svg>
          )}

          {!isSelected && ( // prettier-ignore
            <svg width={24} className="text-grey-border" height={24} viewBox="0 0 24 24" fill="none">
              <rect
                x="2.5"
                y="2.5"
                width={19}
                height={19}
                rx="9.5"
                stroke="currentColor"
                strokeOpacity="0.5"
                strokeWidth={4}
              />
            </svg>
          )}
        </button>
      </div>
      <div className="bg-white rounded-t-15">
        <DisplayTiers
          rule={data}
          className="bg-white border border-gray-border border-opacity-50"
          iconBgClass="bg-grey-fields-100"
        />
      </div>
    </div>
  );
};

export default SelectPricingTier;
