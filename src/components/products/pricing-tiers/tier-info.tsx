import React from "react";
import { PricingTierInterface, ProductItemInterface } from "@/assets/interfaces";
import { AppBtn, RoundActionBtn } from "@/components/ui/buttons";
import LazyImage from "@/components/lazy-image";
import { Layer } from "iconsax-react";
import { getItemThumbnail } from "@/assets/js/utils/utils";
import Toggle from "@/components/ui/toggle";
import { useRequest } from "@/api/utils";
import { UpdateTieredPricing } from "@/api";
import { UpdateTieredPricingParams } from "@/api/interfaces";
import { toast } from "@/components/ui/toast";

interface TierItemProps {
  data: any;
  editAction: VoidFunction;
  deleteAction: VoidFunction;
  viewAction: VoidFunction;
  firstItem: ProductItemInterface;
  updateTier: (data: Partial<PricingTierInterface>) => void;
}

const TierInfo: React.FC<TierItemProps> = ({ data, editAction, deleteAction, viewAction, firstItem, updateTier }) => {
  const { makeRequest, isLoading } = useRequest<UpdateTieredPricingParams>(UpdateTieredPricing);

  const getTierSummary = (tiers: any[]) => {
    if (!tiers || tiers.length === 0) return "No tiers";
    const minTier = tiers.reduce((min, tier) => (tier.minimum_quantity < min.minimum_quantity ? tier : min));
    const maxTier = tiers.reduce((max, tier) => (tier.discount_percentage > max.discount_percentage ? tier : max));
    return `${tiers.length} tiers (${minTier.minimum_quantity}+ qty, up to ${maxTier.discount_percentage}% off)`;
  };

  const handleToggleActive = async (state: boolean) => {
    const request = async () => {
      const [res, err] = await makeRequest({
        id: data.id,
        active: state,
      });

      if (res) {
        data.active = state;
        if (updateTier) {
          updateTier({ active: state });
        }
        return Promise.resolve(res);
      }
      return Promise.reject(err);
    };

    toast.promise(request, {
      loading: {
        title: "Updating tier status",
        message: "Please wait...",
      },
      success: {
        title: "Tier status updated!",
        message: `Tier is now ${state ? "active" : "inactive"}`,
      },
      error: {
        title: "Failed to update tier status",
        message: "Something went wrong! Please retry",
      },
    });
  };

  return (
    <div className="relative h-full cursor-pointer max-w-[450px] w-full overflow-hidden" onClick={viewAction}>
      <div className="flex flex-col gap-2 px-3.5 bg-white border border-gray-border border-opacity-50 items-center rounded-15 relative h-full w-full">
        <div className="flex justify-between items-start w-full px-0 pt-2.5">
          <div className="flex gap-x-2 flex-1 min-w-0">
            <figure className="h-10 w-10 bg-grey-fields-200 rounded-full flex items-center justify-center text-grey-muted">
              <Layer size={22} variant="Bulk" />
            </figure>
            <div className="flex flex-col items-start flex-wrap mr-3.75 min-w-0">
              <h2
                className={`text-black font-semibold text-base overflow-hidden whitespace-nowrap overflow-ellipsis max-w-[80%] inline-block min-w-0`}
              >
                {data.label}
              </h2>
              <p className="text-black-muted text-1xs overflow-ellipsis whitespace-pre-line">
                {data.tiers?.length || 0} Tiers
              </p>
            </div>
          </div>
          <div className="flex gap-2 items-center flex-shrink-0" onClick={(e) => e.stopPropagation()}>
            <RoundActionBtn
              icon="edit"
              grey={false}
              className="bg-grey-fields-200 text-black-secondary"
              onClick={editAction}
            />
            <RoundActionBtn
              icon="delete"
              grey={false}
              className="bg-grey-fields-200 text-accent-red-500"
              onClick={deleteAction}
            />
          </div>
        </div>

        <div className="flex-1 flex justify-between items-center w-full h-full py-3 my-0 border-t border-gray-border border-opacity-50">
          <div className="flex items-center gap-x-2">
            {firstItem && (
              <figure className="h-9 w-9 p-0.5 border border-gray-border border-opacity-50 rounded-8 flex items-center justify-center relative">
                <LazyImage
                  src={getItemThumbnail(firstItem)}
                  alt={""}
                  className="w-full h-full object-cover rounded-8"
                  loaderClasses="rounded-8"
                />
              </figure>
            )}
            <span className="text-black-muted text-1xs overflow-ellipsis whitespace-pre-line">
              {data.items?.length || 0} Items Assigned
            </span>
          </div>

          <div className="flex items-center gap-x-2 ml-auto" onClick={(e) => e.stopPropagation()}>
            <Toggle
              intialState={data.active}
              onChange={handleToggleActive}
              size="sm"
              onLabel="Active"
              offLabel="Inactive"
              disabled={isLoading}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TierInfo;
