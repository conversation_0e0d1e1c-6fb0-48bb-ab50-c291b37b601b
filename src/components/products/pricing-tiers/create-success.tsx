import { AppBtn } from "../../ui/buttons";
import SuccessAnimation from "../../ui/success-animation";

interface Props {
  label: string;
  path: string;
}

const CreateSuccess: React.FC<Props> = ({ label, path }) => {
  return (
    <div className="w-full h-full mt-16">
      <div className="flex flex-col items-center w-full">
        <SuccessAnimation />
        <h2 className="text-black text-center text-lg sm:text-xl lg:text-2lg font-semibold max-w-[250px] my-6.25">
          You&apos;ve successfully created a new {label}
        </h2>
        <AppBtn href={path} className="max-w-xs sm:max-w-sm w-full" size="lg" isBlock>
          View all {label}s
        </AppBtn>
      </div>
    </div>
  );
};

export default CreateSuccess;
