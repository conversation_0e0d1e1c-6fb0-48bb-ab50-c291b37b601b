import Router from "next/router";
import { useEffect, useState } from "react";
import { GetStoreItemsParams, ImportItemsParams } from "../../api/interfaces/items.interface";
import { GetStoreItems, ImportItems } from "../../api/items";
import { useRequest } from "../../api/utils";
import { ProductItemInterface, VariantItem } from "../../assets/interfaces";
import authContext from "../../contexts/auth-context";
import LazyImage from "../lazy-image";
import { AppBtn } from "../ui/buttons";
import Checkbox from "../ui/form-elements/checkbox";
import Radio from "../ui/form-elements/radio";
import Modal, { ModalBody, ModalFooter } from "../ui/modal";
import SearchBar from "../ui/search-bar";
import StoreLogo from "../ui/store-logo";
import SuccessAnimation from "../ui/success-animation";

interface Props {
  show: boolean;
  toggle: (status: boolean) => void;
}

const ProductImportController: React.FC<Props> = ({ show, toggle }) => {
  const { stores, store, updateUser, user, storeIndex } = authContext.useContainer();
  const [step, setStep] = useState<"select-store" | "select-products" | "importing" | "success" | "error">(
    "select-store"
  );
  const [selectedStore, setSelectedStore] = useState(undefined);
  const [filteredItems, setFilteredItems] = useState<ProductItemInterface[]>([]);
  const [items, setItems] = useState([]);
  const [selected, setSelected] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const hasSelectedAll = selected.length == filteredItems.length;
  const getItemsRequest = useRequest<GetStoreItemsParams>(GetStoreItems);
  const importItemsRequest = useRequest<ImportItemsParams>(ImportItems);
  const [count, setCount] = useState(3);
  const [importProgress, setImportProgress] = useState(0);

  useEffect(() => {
    if (step === "select-products") getItems();
    if (step === "success") {
      let count = 3;
      let id = setInterval(() => {
        if (count >= 0) {
          setCount(count--);
          return;
        }

        //update user store item_count
        const storesCopy = [...stores];
        storesCopy[storeIndex].item_count = (store?.item_count ?? 0) + selected.length;
        updateUser({ stores: storesCopy });

        //route to dashboard
        toggle(false);
        clearInterval(id);
        setCount(3);
        Router.push("/dashboard");
      }, 1000);
    }
  }, [step]);

  const handleToggle = (status: boolean) => {
    if (step !== "success") toggle(status);
    if (!status) setStep("select-store");
  };

  const getItems = async () => {
    const [res, error] = await getItemsRequest.makeRequest({
      filter: { store: selectedStore?.id, search: "" },
      showUnavailableItems: true,
      separateFeaturedItems: false,
    });
    if (res) {
      setItems(res.data.items);
    }
  };

  const importProducts = async () => {
    let count = 0;
    //const items = filteredItems.filter((item) => selected.includes(item.id)).map((item) => item.id);
    importItemsRequest.makeRequest({ store_id: selectedStore?.id, items: [...selected] });

    const id = setInterval(
      async () => {
        if (count < 90) {
          setImportProgress(count);
          count += 10;
        } else {
          if (!importItemsRequest.isLoading || importItemsRequest.response) {
            setImportProgress(100);
            clearInterval(id);
            setStep("success");
          }
        }
      },
      selected.length < 10 ? 75 : 150
    );
  };

  const handleContinue = async () => {
    switch (step) {
      case "select-store":
        setStep("select-products");
        break;

      case "select-products":
        setStep("importing");
        await importProducts();
        break;
    }
  };

  const isSelectedItem = (id: string) => selected.includes(id);

  /* ITEM TOGGLE LOGIC */
  const toggleItem = (item: ProductItemInterface) => {
    const selectedIndex = selected.indexOf(item.id);
    const selectedCopy = [...selected];

    if (selectedIndex > -1) {
      selectedCopy.splice(selectedIndex, 1);
    } else {
      selectedCopy.push(item.id);
    }
    setSelected(selectedCopy);
  };
  const handleSelectAll = () => {
    let newSelected = [];

    if (!hasSelectedAll) {
      newSelected = items.map((i) => i.id);
    }

    setSelected(newSelected);
  };

  /* SEARCH LOGIC */
  useEffect(() => {
    if (searchQuery === "") {
      setFilteredItems(items);
      return;
    }

    searchQuery.trim();
    const splitQueries = searchQuery.split(" ");

    const filteredItems = items.filter((item) => {
      let match = false;
      splitQueries.map((query) => {
        if (item.name.toLocaleLowerCase().includes(query)) match = true;
      });

      return match;
    });

    setFilteredItems(filteredItems);
  }, [searchQuery, items]);

  return (
    <Modal {...{ show, toggle: handleToggle }} title={step === "select-store" ? "Select Store" : "Import Products"}>
      <ModalBody>
        {step === "select-store" && (
          <div>
            <span>Select store you want to import products from</span>
            {stores.map(
              (s, index) =>
                s.id !== store.id && (
                  <div
                    className="flex items-center w-full p-2 rounded-lg bg-gray-50 my-2 cursor-pointer"
                    onClick={() => setSelectedStore(s)}
                    key={index}
                  >
                    <Radio
                      small
                      name={s.name}
                      value={s.id}
                      onChange={() => setSelectedStore(s)}
                      chosen={selectedStore?.id}
                    ></Radio>
                    <StoreLogo storeName={s.name} className="h-7.5 w-7.5 rounded-full mr-2.5 ml-1" logo={s.logo} />
                    <span>{s.name}</span>
                  </div>
                )
            )}
          </div>
        )}

        {step === "select-products" && (
          <div>
            <div className="">Select products you want to import</div>
            <div className="sticky -top-7 z-50 bg-white pt-3">
              <SearchBar {...{ searchQuery, setSearchQuery }} />
              <div className="flex items-center justify-between mt-1">
                <div className="flex items-center w-[fit-content]">
                  <Checkbox
                    onChange={handleSelectAll}
                    checked={hasSelectedAll}
                    name="select_all"
                    className="mr-1"
                    id="select_all"
                    // neutral
                  ></Checkbox>
                  <span className="pt-1 text-sm text-gray-500">Select All</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 mt-2">
              {filteredItems.map((item, index) => (
                <div
                  key={index}
                  className="w-fit flex items-center py-4 pr-4 border-b-[1px] border-gray-100 last:border-0"
                >
                  <figure className=" flex-shrink-0 h-9 w-9 rounded-5 overflow-hidden mr-3 relative">
                    <LazyImage
                      src={item.images[0]}
                      className="h-full w-full object-cover rounded-5 relative z-10"
                      alt={item.name}
                    />
                  </figure>
                  <span className="w-[60%] mt-0.75 overflow-hidden whitespace-nowrap overflow-ellipsis">
                    {item.name}
                  </span>
                  <Checkbox
                    onChange={() => toggleItem(item)}
                    checked={isSelectedItem(item.id)}
                    id={item.id}
                    name={item.name}
                    // neutral
                    className="ml-auto"
                  ></Checkbox>
                </div>
              ))}
            </div>
          </div>
        )}

        {step === "importing" && (
          <div className="px-32 py-8">
            <h1 className=" text-3xl text-center">{`${importProgress}%`}</h1>
            <span className="text-dark block text-center text-sm mb-8">Importing your products</span>
            <div
              className={`rounded-2xl py-1 bg-primary-400 duration-200`}
              style={{ width: `${importProgress}%` }}
            ></div>
          </div>
        )}

        {step === "success" && (
          <div className="my-8">
            <div className="w-[fit-content] m-auto">
              <SuccessAnimation />
            </div>
            <span className="mt-2 block text-center">
              You’ve successfully imported
              <br />
              <span className="font-semibold ">{selected.length} products </span>
              from {selectedStore?.name} store
            </span>
            <div className="my-6 bg-purple-50 p-1.5 rounded-lg mx-24">
              <span className="text-center block text-xs text-primary-500 mt-1">
                Taking you to the dashboard in {count}
              </span>
            </div>
          </div>
        )}
      </ModalBody>
      {(step === "select-products" || step === "select-store") && (
        <ModalFooter>
          <AppBtn className="w-full" onClick={handleContinue}>
            {step === "select-store" ? "Continue" : "Import Products"}
          </AppBtn>
        </ModalFooter>
      )}
    </Modal>
  );
};

export default ProductImportController;
