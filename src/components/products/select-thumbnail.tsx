import { calculateSize } from "@/assets/js/utils/image-selection";
import { convertAwsToCloudFront } from "@/components/lazy-image";
import LazyVideo from "@/components/lazy-video";
import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from "react";
import { AppBtn } from "../ui/buttons";
import SliderInput from "../ui/form-elements/slider-input";

const MIME_TYPE = "image/jpeg";
const QUALITY = 0.7;
const MAX_SIZE = 1500;

interface Props {
  videoFile?: File | Blob;
  videoUrl?: string;
  setDimensions: React.Dispatch<
    React.SetStateAction<{
      width: number;
      height: number;
    }>
  >;
  onSelectThumbnail: (blob: Blob) => void;
}

export interface SelectThumbnailRef {
  handleSelectThumbnail: () => void;
}

const SelectThumbnailMain = forwardRef<SelectThumbnailRef, Props>(
  ({ videoFile, videoUrl, setDimensions, onSelectThumbnail }, ref) => {
    const videoRef = useRef<HTMLVideoElement>(null);
    const canvasRef = useRef<HTMLCanvasElement>(null);

    const [scrubValue, setScrubValue] = useState(0);
    const [videoURL, setVideoURL] = useState<string>();

    const handleSelectThumbnail = () => {
      const [newWidth, newHeight] = calculateSize(
        { width: videoRef.current.videoWidth, height: videoRef.current.videoHeight } as any,
        MAX_SIZE
      );
      const canvas = document.createElement("canvas");
      canvas.width = newWidth;
      canvas.height = newHeight;
      const ctx = canvas.getContext("2d");
      ctx.drawImage(videoRef.current, 0, 0, newWidth, newHeight);

      canvas.toBlob(
        (blob) => {
          onSelectThumbnail(blob);
        },
        MIME_TYPE,
        QUALITY
      );
    };

    useImperativeHandle(ref, () => ({
      handleSelectThumbnail,
    }));

    useEffect(() => {
      if (videoUrl) {
        setVideoURL(convertAwsToCloudFront(videoUrl));
        return;
      }
      if (videoFile) {
        setVideoURL(URL.createObjectURL(videoFile));
      }
    }, [videoFile, videoUrl]);

    useEffect(() => {
      const videoPlayer = videoRef.current;
      if (videoPlayer) {
        videoPlayer.src = videoURL;
        videoPlayer.addEventListener("loadedmetadata", function () {
          setDimensions({
            width: videoPlayer.videoWidth,
            height: videoPlayer.videoHeight,
          });
        });

        videoPlayer.addEventListener("canplaythrough", function () {
          if (canvasRef.current) {
            canvasRef.current.width = videoPlayer.videoWidth;
            canvasRef.current.height = videoPlayer.videoHeight;
            canvasRef.current
              .getContext("2d")
              .drawImage(videoPlayer, 0, 0, videoPlayer.videoWidth, videoPlayer.videoHeight);
          }
          if (videoPlayer.currentTime === 0) {
            videoPlayer.currentTime = 0.1;
          }
        });
      }
    }, [videoRef]);

    const handleScrub = (value: number) => {
      const player = videoRef.current;
      setScrubValue(value);
      const currentTime = player.duration * (value / 100);
      if (player && !Number.isNaN(currentTime) && player.isConnected)
        player.currentTime = player.duration * (value / 100);

      canvasRef.current
        .getContext("2d")
        .drawImage(videoRef.current, 0, 0, videoRef.current.videoWidth, videoRef.current.videoHeight);
    };

    return (
      <div className="w-full">
        <div className="p-2.5 rounded-xl border border-grey-divider">
          <div className="w-full max-w-[3800px] mx-auto  cursor-pointer relative block transition-all ease-out h-[35vh]">
            <div className={`${videoURL ? "" : "hidden"} rounded-10 overflow-hidden bg-white h-full w-full relative`}>
              <div className="relative w-full h-full flex flex-col items-center overflow-hidden">
                <LazyVideo
                  muted
                  autoPlay
                  preload="auto"
                  src={videoURL}
                  className="h-full overflow-hidden z-20"
                  playsInline crossOrigin="anonymous"
                  ref={videoRef}>
                </ LazyVideo>
                <div
                  className={`absolute top-0 left-0 w-full h-full z-10 backdrop-blur-md bg-black bg-opacity-5 ${videoRef ? "" : "hidden"
                    }`}
                ></div>
                <canvas className={`w-full left-0 overflow-hidden top-0 absolute rounded-10`} ref={canvasRef}></canvas>
              </div>
            </div>
          </div>

          {videoURL && (
            <div className="mt-2.5 p-2.5 bg-grey-fields-100 rounded-lg">
              {
                <ul className="flex items-center text-[10px] mb-1.25 text-black-placeholder justify-between">
                  {new Array(7).fill(null).map((_, i) => (
                    <li className="" key={i}>
                      {(((videoRef?.current?.duration ?? 0) / 7.0) * i).toFixed(1)}s
                    </li>
                  ))}
                  <li className="">{videoRef?.current?.duration?.toFixed(1) ?? 0}s</li>
                </ul>
              }
              <SliderInput
                customTrack={
                  <div className="h-8.75 w-full relative overflow-hidden rounded-lg pointer-events-none">
                    <video
                      src={videoURL}
                      className="w-full absolute top-1/2 -translate-y-1/2 opacity-30"
                      playsInline
                    ></video>
                  </div>
                }
                customHandle={
                  <div className="h-full w-5 border-[2.5px] rounded-lg border-primary-500 cursor-[ew-resize] "></div>
                }
                debounceTimeout={10}
                showLabel={false}
                max={100}
                min={0}
                start={0}
                onChange={handleScrub}
                value={scrubValue}
              />
            </div>
          )}
        </div>
        {/* <div className="p-5 fixed bottom-0 left-0 w-full border-t border-grey-divider">
          <AppBtn className="" disabled={!Boolean(videoFile)} onClick={handleSelectThumbnail} isBlock color="primary">
            Select Thumbnail
          </AppBtn>
        </div> */}
      </div>
    );
  }
);

export default SelectThumbnailMain;

SelectThumbnailMain.displayName = "SelectThumbnailMain";
