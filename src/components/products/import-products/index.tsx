import Router from "next/router";
import { useEffect, useState } from "react";
import { GetStoreItemsParams, ImportItemsParams } from "../../../api/interfaces/items.interface";
import { GetStoreItems, ImportItems } from "../../../api/items";
import { useRequest } from "../../../api/utils";
import authContext from "../../../contexts/auth-context";
import { AppBtn } from "../../ui/buttons";
import Modal, { ModalBody, ModalFooter } from "../../ui/modal";
import SuccessAnimation from "../../ui/success-animation";
import SelectProducts from "./select-products";
import SelectStore from "./select-store";

interface Props {
  show: boolean;
  toggle: (status: boolean) => void;
  success: {
    label: string;
    route: string;
  };
}

const ImportProducts: React.FC<Props> = ({ show, toggle, success }) => {
  const { stores, store, updateUser, user, storeIndex, updateStore } = authContext.useContainer();
  const [step, setStep] = useState<"select-store" | "select-products" | "importing" | "success" | "error">(
    "select-store"
  );
  const userStores = stores.filter((s) => s.owner === user?.id).filter((s) => s.id !== store.id);
  const [selectedStore, setSelectedStore] = useState("");
  const [items, setItems] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);
  const getItemsRequest = useRequest<GetStoreItemsParams>(GetStoreItems);
  const importItemsRequest = useRequest<ImportItemsParams>(ImportItems);
  // const [count, setCount] = useState(3);
  const [importProgress, setImportProgress] = useState(0);
  const showFooter = step === "select-products" || step === "select-store";

  useEffect(() => {
    if (userStores.length < 2 && userStores.length > 0) {
      setSelectedStore(userStores[0].id);
      setStep("select-products");
    }
  }, []);

  useEffect(() => {
    if (step === "select-products") getItems();
    if (step === "success") {
      updateStore({ item_count: (store?.item_count ?? 0) + selectedItems.length });
    }
  }, [step]);

  const handleToggle = (status: boolean) => {
    if (step !== "success") toggle(status);
    if (!status) setStep("select-store");
  };

  const getItems = async () => {
    const [res, error] = await getItemsRequest.makeRequest({
      filter: { store: selectedStore, search: "" },
      page: 1,
      per_page: 1000,
      showUnavailableItems: true,
      separateFeaturedItems: false,
      fromDashboard: true,
    });

    if (res) {
      setItems([...(res?.data?.items?.featured_items ?? []), ...(res?.data?.items?.other_items ?? [])]);
    }
  };

  const importProducts = async () => {
    let count = 0;
    //const items = filteredItems.filter((item) => selected.includes(item.id)).map((item) => item.id);
    importItemsRequest.makeRequest({ store_id: selectedStore, items: [...selectedItems] });

    const id = setInterval(
      async () => {
        if (count < 90) {
          setImportProgress(count);
          count += 10;
        } else {
          if (!importItemsRequest.isLoading || importItemsRequest.response) {
            setImportProgress(100);
            clearInterval(id);
            setStep("success");
          }
        }
      },
      selectedItems.length < 10 ? 75 : 150
    );
  };

  const next = async () => {
    switch (step) {
      case "select-store":
        setStep("select-products");
        break;

      case "select-products":
        setStep("importing");
        await importProducts();
        break;
    }
  };

  const proceed = () => {
    //route to dashboard
    toggle(false);
    Router.push(success.route);
  };

  return (
    <Modal {...{ show, toggle: handleToggle }} title={step === "select-store" ? "Select Store" : "Import Products"}>
      <ModalBody>
        {step === "select-store" && <SelectStore {...{ stores: userStores, setSelectedStore, selectedStore }} />}

        {step === "select-products" && <SelectProducts {...{ selectedItems, setSelectedItems, items }} />}

        {step === "importing" && (
          <div className="px-32 py-8">
            <h1 className=" text-3xl text-center">{`${importProgress}%`}</h1>
            <span className="text-dark block text-center text-sm mb-8">Importing your products</span>
            <div
              className={`rounded-2xl py-1 bg-primary-400 duration-200`}
              style={{ width: `${importProgress}%` }}
            ></div>
          </div>
        )}

        {step === "success" && (
          <div className="my-8 flex flex-col items-center">
            <div className="w-[fit-content] m-auto">
              <SuccessAnimation />
            </div>
            <span className="mt-2 block text-center">
              You’ve successfully imported
              <br />
              <span className="font-semibold ">{selectedItems.length} products </span>
            </span>
            <AppBtn size="lg" className="mt-6 !px-24" onClick={proceed}>
              Proceed
            </AppBtn>
          </div>
        )}
      </ModalBody>
      {showFooter && (
        <ModalFooter>
          <AppBtn className="w-full" onClick={next} size="lg">
            {step === "select-store" ? "Continue" : "Import Products"}
          </AppBtn>
        </ModalFooter>
      )}
    </Modal>
  );
};

export default ImportProducts;
