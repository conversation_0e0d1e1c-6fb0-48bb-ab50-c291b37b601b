import LazyImage from "@/components/lazy-image";
import { InfoBlockInterface, ProductItemInterface } from "@/assets/interfaces";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { <PERSON>dalB<PERSON>, ModalFooter } from "@/components/ui/modal";
import { useModals } from "@/components/hooks/useModals";
import CreateInfoBlock from "./create-info-block";
import Badge from "@/components/ui/badge";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  // addBlock: VoidFunction;
  allBlocks: InfoBlockInterface[];
  productBlocks: InfoBlockInterface[];
  setProductBlocks: (blocks: InfoBlockInterface[]) => void; //set all blocks in product
  setAllBlocks: (blocks: InfoBlockInterface[]) => void; //set all blocks in store
}

const SelectInfoBlock: React.FC<Props> = ({
  show,
  toggle,
  setProductBlocks,
  allBlocks,
  productBlocks,
  setAllBlocks,
}) => {
  const { modals, toggleModal } = useModals(["create_info_block"]);
  const createBlock = (block: InfoBlockInterface) => {
    setAllBlocks([...allBlocks, block]);
    setProductBlocks([...productBlocks, block]);
  };

  const toggleBlock = (block: InfoBlockInterface) => {
    const isPresent = productBlocks.some((b) => b.id === block.id);
    if (isPresent) {
      // Remove the block if it is already in productBlocks
      const updatedBlocks = productBlocks.filter((b) => b.id !== block.id);
      setProductBlocks(updatedBlocks);
    } else {
      // Add the block if it is not in productBlocks
      setProductBlocks([...productBlocks, block]);
    }
  };

  return (
    <>
      <Modal {...{ show, toggle }} title="Add Product Info Blocks" size="midi">
        <ModalBody>
          <p className="text-black-muted text-xs sm:text-1xs mb-3.75">
            Choose from existing product info blocks or create a new one
          </p>
          <AppBtn isBlock color="neutral" onClick={(e) => toggleModal("create_info_block")}>
            + Create New Info Block
          </AppBtn>
          {allBlocks && allBlocks?.length > 0 && (
            <div className="flex flex-col gap-3 my-3.75">
              {allBlocks?.map((block, i) => (
                <InfoBlock
                  key={i}
                  index={i}
                  data={block}
                  toggleAction={toggleBlock}
                  selectedBlocks={productBlocks.map((b) => b.id)}
                />
              ))}
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <AppBtn isBlock color="primary" onClick={() => toggle(false)} size="lg">
            Proceed with Selections
          </AppBtn>
        </ModalFooter>
      </Modal>
      <CreateInfoBlock
        show={modals.create_info_block.show}
        toggle={() => toggleModal("create_info_block")}
        createBlock={(block: InfoBlockInterface) => createBlock(block)}
        onSuccess={() => toggleModal("create_info_block")}
      />
    </>
  );
};

interface BlockItemProps {
  data?: InfoBlockInterface;
  index: number;
  selectedBlocks: string[];
  toggleAction: (block: InfoBlockInterface) => void;
}

const InfoBlock: React.FC<BlockItemProps> = ({ data, toggleAction, index, selectedBlocks }) => {
  const isPresent = selectedBlocks.some((block) => block === data.id);

  return (
    <div className="relative h-full">
      <div className="flex flex-col gap-2 bg-grey-fields-100 items-center rounded-10 relative h-full">
        <div className="flex justify-between items-center w-full px-3 pt-2.5">
          <div className="flex gap-2 justify-between items-center flex-wrap">
            <h2 className="text-black font-semibold text-base">{data.title}</h2>
            {data.tag && data.tag && (
              <Badge
                greyBg={false}
                color="primary"
                size="sm"
                uppercase={false}
                className="text-1xs font-medium"
                text={data.tag}
              />
            )}
          </div>{" "}
          <div className="flex gap-2 items-center">
            <button
              className="h-4.5 w-4.5 sm:w-5 sm:h-5 rounded-full bg-grey-fields-100"
              onClick={() => toggleAction(data)}
            >
              {isPresent && ( // prettier-ignore
                <svg width="100%" viewBox="0 0 18 18" fill="none">
                  <rect width="18" height="18" rx="9" fill="#39B588" />
                  <path
                    d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z"
                    fill="white"
                  />
                </svg>
              )}

              {!isPresent && ( // prettier-ignore
                <svg width={24} className="text-grey-border" height={24} viewBox="0 0 24 24" fill="none">
                  <rect
                    x="2.5"
                    y="2.5"
                    width={19}
                    height={19}
                    rx="9.5"
                    stroke="currentColor"
                    strokeOpacity="0.5"
                    strokeWidth={4}
                  />
                </svg>
              )}
            </button>
          </div>
        </div>

        {data.content_type === "TEXT" && (
          <div className="flex-1  w-full h-full border rounded-10 border-gray-border border-opacity-50 bg-white p-3 my-0">
            <p className="text-black-muted text-1xs overflow-ellipsis">{data.text_content}</p>
          </div>
        )}

        {data.content_type === "IMAGES" && (
          <div className="flex-1  h-full border rounded-10 border-gray-border border-opacity-50 bg-white p-3 my-0 grid items-start w-full grid-cols-[repeat(auto-fit,120px)] sm:grid-cols-[repeat(auto-fit,120px)] gap-2.5">
            {data.image_content.map((url, index) => (
              <div key={index}>
                <figure
                  className="w-28 h-28 sm:h-[100px] border border-grey-fields-100 rounded-10 sm:h-[100px] relative group"
                  key={index}
                >
                  <LazyImage
                    src={url}
                    alt={""}
                    className="w-full h-full object-cover rounded-10"
                    loaderClasses="rounded-10"
                  />
                </figure>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SelectInfoBlock;
