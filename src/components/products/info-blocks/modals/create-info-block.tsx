import LazyImage from "@/components/lazy-image";
import { useRequest } from "@/api/utils";
import { FILE_TYPES, Image, InfoBlockInterface, ProductItemInterface } from "@/assets/interfaces";
import { AppBtn } from "@/components/ui/buttons";
import ErrorLabel from "@/components/ui/error-label";
import * as Yup from "yup";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import SuccessLabel from "@/components/ui/success-label";
import { useEffect, useRef, useState } from "react";
import useImageUploads from "@/components/hooks/useImageUploads";
import { useFormik } from "formik";
import { CreateStoreInfoBlock } from "@/api";
import { getFieldvalues, handleImageSelectionFromFile } from "@/assets/js/utils/functions";
import { InputField, SelectDropdown, TextArea } from "@/components/ui/form-elements";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  createBlock: (block: InfoBlockInterface) => void;
  onSuccess: () => void;
}

const CreateInfoBlock: React.FC<Props> = ({ show, toggle, createBlock, onSuccess }) => {
  const {
    isLoading,
    makeRequest,
    error,
    response: successResponse,
  } = useRequest<InfoBlockInterface>(CreateStoreInfoBlock);
  const [errorText, setErrorText] = useState<string>(null);
  const imagePicker = useRef<HTMLInputElement>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    form.submitForm();
  };

  const form = useFormik<InfoBlockInterface>({
    initialValues: {
      title: "",
      text_content: "",
      content_type: "TEXT",
      image_content: [],
      is_visible: true,
    },
    validationSchema: Yup.object().shape({
      title: Yup.string().required("Title is required"),
      content_type: Yup.string().oneOf(["TEXT", "IMAGES"], "Invalid content type").required("Content type is required"),
      text_content: Yup.string().when("content_type", {
        is: "TEXT",
        then: Yup.string().required("Text content is required"),
        otherwise: Yup.string().notRequired(),
      }),
    }),
    onSubmit: async (values) => {
      if (values.content_type === "IMAGES" && values.image_content.length < 1) {
        setErrorText("At least one image URL is required");
        return;
      }
      const [response, error] = await makeRequest(values);
      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors(error.fields);
        }
      } else {
        form.resetForm();
        createBlock(response?.data);
        onSuccess();
      }
    },
  });
  const [images, setImages] = useState<Image[]>(
    form.values.image_content.map((url) => ({
      url,
      file: null,
      isUploading: false,
      uploadProgress: 100,
      name: "",
      src: "",
      lastModified: 0,
    }))
  );

  useEffect(() => {
    form.setFieldValue(
      "image_content",
      images.map((img) => img.url)
    );
  }, [images]);
  useImageUploads(images, FILE_TYPES.ITEMS, setImages);

  const removeImage = (e: React.MouseEvent<HTMLButtonElement>, index: number) => {
    e.preventDefault();
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };
  return (
    <Modal {...{ show, toggle }} title="Add Product Info Blocks" size="midi">
      <ModalBody>
        <form onSubmit={handleSubmit} className="w-full pb-20">
          <ErrorLabel error={errorText || error?.message} />
          <SuccessLabel message={successResponse ? "Info Block Created successfully!" : ""} />

          <InputField label={`Block Title`} {...getFieldvalues("title", form)} />

          <SelectDropdown
            label="Content Type"
            options={[
              { value: "TEXT", text: "Text" },
              { value: "IMAGES", text: "Images" },
            ]}
            {...getFieldvalues("content_type", form)}
            onChange={(e) => {
              form.setFieldValue("content_type", e.target.value);
            }}
          />
          {form.values.content_type === "TEXT" && (
            <TextArea label={`Content`} rows={4} {...getFieldvalues("text_content", form)} />
          )}
          {form.values.content_type === "IMAGES" && (
            <>
              <div className="grid mt-3.75 items-start w-full grid-cols-[repeat(auto-fit,80px)] sm:grid-cols-[repeat(auto-fit,120px)] gap-2.5">
                <input
                  type="file"
                  ref={imagePicker}
                  name="product-images"
                  multiple
                  accept="image/*,.heic"
                  id="product-images"
                  className="hidden"
                  onChange={(e) =>
                    handleImageSelectionFromFile({
                      e,
                      images: images,
                      saveImages: (images: Image[]) => setImages(images),
                    })
                  }
                />

                {images.map(({ name, src, url, isUploading, uploadProgress, error }, index) => (
                  <div key={index}>
                    <figure
                      className="w-20 h-20 md:h-[100px] md:w-[100px] border border-grey-fields-100 rounded-10 sm:h-[100px] relative group"
                      key={index}
                    >
                      {src || url ? (
                        <>
                          <LazyImage
                            src={src || url}
                            alt={name}
                            className="w-full h-full object-cover rounded-10"
                            loaderClasses="rounded-10"
                          />
                        </>
                      ) : (
                        <div className="h-full w-full rounded-10 bg-grey-loader animate-pulse flex items-center justify-center">
                          <div className="spinner spinner--sm"></div>
                        </div>
                      )}
                      <button
                        className="h-6 w-6 bg-black bg-opacity-80 flex items-center justify-center absolute transform -top-2 -right-2 rounded-full transition-all opacity-0 group-hover:opacity-100 z-30"
                        onClick={(e) => removeImage(e, index)}
                        type="button"
                      >
                        {/* prettier-ignore */}
                        <svg width="12" height="12" viewBox="0 0 15 15" fill="none">
                      <path d="M11.25 3.75L3.75 11.25" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M3.75 3.75L11.25 11.25" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                      </button>
                    </figure>
                    {(isUploading || uploadProgress > 0) && (
                      <div
                        className={`mt-1.5 h-1 w-full rounded-10 overflow-hidden ${
                          error ? "bg-accent-red-500 bg-opacity-10" : "bg-grey-divider"
                        }`}
                      >
                        <div
                          className={`h-full transition-all duration-200 ease-out ${
                            error ? "bg-accent-red-500" : "bg-accent-green-500"
                          }`}
                          style={{ width: `${uploadProgress}%` }}
                        ></div>
                      </div>
                    )}
                  </div>
                ))}

                <button
                  className="no-outline w-20 h-20 sm:w-[100px] sm:h-[100px] rounded-10 border border-dashed border-grey-subtext text-grey-subtext hover:border-primary-500 hover:text-primary-500 transition-all flex items-center justify-center"
                  onClick={() => imagePicker.current.click()}
                  type="button"
                >
                  {/* prettier-ignore */}
                  <svg width="20" viewBox="0 0 30 30" fill="none">
                <path d="M15 6.25V23.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M6.24976 15H23.7498" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
                </button>
              </div>
            </>
          )}
        </form>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock color="primary" disabled={isLoading} onClick={() => form.submitForm()}>
          {isLoading ? "Adding...." : "Add To Product"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default CreateInfoBlock;
