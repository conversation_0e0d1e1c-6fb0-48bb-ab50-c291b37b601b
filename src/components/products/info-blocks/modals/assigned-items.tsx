import { StrippedItem } from "@/assets/interfaces";
import { getActualPrice } from "@/assets/js/utils/utils";
import useStoreItems from "@/components/hooks/useStoreItems";
import LazyImage from "@/components/lazy-image";
import Portal from "@/components/portal";
import { AppBtn } from "@/components/ui/buttons";
import BaseBtn from "@/components/ui/buttons/base-btn";
import ContentState from "@/components/ui/content-state";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import { FormatCurrencyFun } from "@/contexts/cart-context";

import React, { useEffect, useState } from "react";
interface Props {
  show: boolean;
  toggle: (show: boolean) => void;
  items: string[];
  formatAsCurrency: FormatCurrencyFun;
  removeItem: (id: string) => void;
  addNew: () => void;
  loadingState: boolean;
}

const AssignedItemsModal: React.FC<Props> = (props) => {
  const { show, toggle, items, formatAsCurrency, addNew, removeItem, loadingState } = props;
  const { items: storeItems, fetchItemsReq } = useStoreItems();

  const storeItemsMap = React.useMemo(() => {
    const map: Record<string, StrippedItem> = {};
    storeItems.forEach((item) => {
      map[item.id] = item;
    });
    return map;
  }, [storeItems]);

  useEffect(() => {
    if (items.length < 1 && show) {
      toggle(false);
    }
  }, [items]);

  return (
    <Portal>
      <Modal show={show} toggle={toggle} title="Assigned Products" size="md">
        <ModalBody className="relative !pt-0 p-5 sm:p-6" noPadding>
          {storeItems.length < 1 && (
            <ContentState
              loadingText="Loading Products..."
              isLoading={fetchItemsReq.isLoading}
              isEmpty={items.length < 1}
              emptyIcon={
                // prettier-ignore
                <svg width="45%" viewBox="0 0 24 24" fill="none">
              <path opacity="0.4" d="M12 2C9.38 2 7.25 4.13 7.25 6.75C7.25 9.32 9.26 11.4 11.88 11.49C11.96 11.48 12.04 11.48 12.1 11.49C12.12 11.49 12.13 11.49 12.15 11.49C12.16 11.49 12.16 11.49 12.17 11.49C14.73 11.4 16.74 9.32 16.75 6.75C16.75 4.13 14.62 2 12 2Z" fill="#AAAAAA"/>
              <path d="M17.08 14.1499C14.29 12.2899 9.74002 12.2899 6.93002 14.1499C5.66002 14.9999 4.96002 16.1499 4.96002 17.3799C4.96002 18.6099 5.66002 19.7499 6.92002 20.5899C8.32002 21.5299 10.16 21.9999 12 21.9999C13.84 21.9999 15.68 21.5299 17.08 20.5899C18.34 19.7399 19.04 18.5999 19.04 17.3599C19.03 16.1299 18.34 14.9899 17.08 14.1499Z" fill="#AAAAAA"/>
            </svg>
              }
              title="No Items Assigned"
              errorMessage="We couldn't load your items, click on the button to retry"
              errorTitle="Fetching items failed"
              error={fetchItemsReq.error}
              errorAction={
                <AppBtn size="sm" onClick={() => fetchItemsReq.makeRequest()}>
                  Reload Items
                </AppBtn>
              }
            ></ContentState>
          )}
          {storeItems && storeItems.length > 0 && (
            <>
              <p className="text-1 text-black-muted mt-3.75">View and manage products assigned to this info block</p>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-x-5">
                {items.map((id, index) => {
                  const item = storeItemsMap[id];
                  if (!item) return null;
                  return (
                    <div
                      key={index}
                      className="w-full flex items-start py-4 last:border-0 cursor-pointer"
                      onClick={() => removeItem(item.id)}
                      role="button"
                    >
                      <div className="flex flex-col items-center w-full overflow-hidden">
                        <figure className="flex-shrink-0 h-[110px] md:h-[130px] w-full rounded-10 overflow-hidden relative border border-grey-fields-200">
                          <LazyImage
                            src={item.image}
                            className="h-full w-full object-cover rounded-10 relative z-10"
                            alt={item.name}
                          />
                          <button
                            type="button"
                            className="ml-auto mt-1 absolute top-1.5 right-2 bg-white rounded-full z-[10] border border-grey-divider"
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <rect width="24" height="24" rx="12" fill="white" />
                              <path
                                d="M15.3337 8.66797L8.66699 15.3346M8.66699 8.66797L15.3337 15.3346"
                                stroke="#BF0637"
                                strokeWidth="1.15556"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </button>
                        </figure>
                        <div className="w-full">
                          <span className="text-sm sm:text-1sm mt-0.75 overflow-hidden whitespace-nowrap overflow-ellipsis text-dark font-bold font-display block -mb-0.5">
                            {item.name}
                          </span>
                          <span className="text-xxs md:text-xs font-bold text-black">
                            {formatAsCurrency(getActualPrice(item))}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              <hr className="border-1 border-divider mb-3.75" />
              <BaseBtn
                onClick={addNew}
                disabled={loadingState}
                className="font-medium text-primary-500 text-1sm font-medium"
              >
                Add New Products
              </BaseBtn>
            </>
          )}
        </ModalBody>
        <ModalFooter>
          <div className="w-full">
            <AppBtn isBlock size="lg" color="neutral" disabled={loadingState} onClick={() => toggle(false)}>
              Close
            </AppBtn>
          </div>
        </ModalFooter>
      </Modal>
    </Portal>
  );
};

export default AssignedItemsModal;
