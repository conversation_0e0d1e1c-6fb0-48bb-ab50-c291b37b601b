import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import DeleteHighlight from "./delete-product-highlights";
import CreateHighlight from "@/components/products/highlights/modals/create-product-highlight";
import EditHighlight from "@/components/products/highlights/modals/edit-product-highlight";
import { useEffect, useState } from "react";
import { GetHighlights } from "../../../api";
import { useFetcher, useRequest } from "../../../api/utils";
import { HighlightInterface } from "../../../assets/interfaces";
import useSearchParams from "../../hooks/useSearchParams";
import { AppBtn } from "../../ui/buttons";
import ContentState from "../../ui/content-state";
import { productPageIcons } from "../../ui/layouts/product";
import authContext from "../../../contexts/auth-context";
import { ModalState } from "../../hooks/useModals";
import Portal from "../../portal";
import ErrorBox from "../../ui/error";
import HighLightItem from "./highlight-item";
import usePagination from "@/components/hooks/usePagination";
import Pagination from "@/components/ui/pagination";
import HighlightDetailsModal from "./modals/highlight-details";
import useInstagramAuth from "@/components/hooks/useInstagramAuth";

interface Props {
  modals: ModalState;
  toggleModal: (modal: string) => void;
}

const PER_PAGE = 10;
const Highlights: React.FC<Props> = ({ modals, toggleModal }) => {
  const { search, page } = useSearchParams(["search", "page"]);
  const { store, subscription } = authContext.useContainer();
  const { currentPage, perPage, setPerPage, goNext, goPrevious, setPage } = usePagination();

  useInstagramAuth();

  const [highlights, setHighlights] = useState<HighlightInterface[]>([]);
  const [selected, setSelected] = useState<{
    edit: HighlightInterface;
    delete: HighlightInterface;
    details: HighlightInterface;
  }>({
    edit: null,
    delete: null,
    details: null,
  });

  const { response, isLoading, error, makeRequest } = useFetcher(GetHighlights, {
    filter: { search: search ? search : "" },
    page: page ? page : currentPage,
    per_page: perPage,
  });
  // const highlights: HighlightInterface[] = response?.data?.items ?? [];

  const deleteHighlightReq = useRequest(DeleteHighlight);

  let canManageHighlights = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_HIGHLIGHTS,
  });

  useEffect(() => {
    if (response?.data?.items) {
      setHighlights(response?.data?.items);
    }
  }, [response?.data?.items]);

  const editHighlight = (id: string) => {
    setSelected((prev) => ({
      ...prev,
      edit: highlights.find((h) => h.id === id),
    }));

    toggleModal("edit");
  };

  if (!canManageHighlights) {
    return (
      <ErrorBox
        title="Upgrade required"
        message="Upgrade to the basic or business plus plan to create product highlights"
      >
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  return (
    <>
      <div className="pb-20">
        {isLoading || !highlights || highlights?.length < 1 ? (
          <>
            <ContentState
              isLoading={isLoading}
              loadingText="Loading product highlights..."
              errorTitle="Failed to load product highlights"
              error={error}
              errorMessage="We couldn't load your product highlights please reload page"
              errorAction={
                <AppBtn size="md" onClick={() => makeRequest()}>
                  Reload Product Highlights
                </AppBtn>
              }
              isEmpty={highlights.length < 1}
              emptyIcon={<div className="w-8.75 h-8.75 text-grey-muted">{productPageIcons.productHighlights}</div>}
              title="No Highlights"
              description="Create your first highlight"
              loadingPlaceholder={<LoadingPlaceholder />}
            >
              <AppBtn onClick={() => toggleModal("create")} size="md" className="max-w-[240px] m-auto">
                Create Highlight
              </AppBtn>
            </ContentState>
          </>
        ) : (
          <div className="flex flex-col">
            <div className="grid grid-cols-[repeat(auto-fill,minmax(275px,1fr))] gap-5">
              {highlights.map((highlight, i) => (
                <HighLightItem
                  key={i}
                  data={highlight}
                  deleteHighlight={() => {
                    setSelected((prev) => ({ ...prev, delete: highlight }));
                    toggleModal("delete");
                  }}
                  editHighlight={() => {
                    setSelected((prev) => ({ ...prev, edit: highlight }));
                    toggleModal("edit");
                  }}
                  openDetails={() => {
                    setSelected((prev) => ({ ...prev, details: highlight }));
                    toggleModal("details");
                  }}
                  updateHighlight={(highlight) =>
                    setHighlights((highlights) => highlights.map((h) => (h.id === highlight.id ? highlight : h)))
                  }
                />
              ))}
            </div>
            <Pagination
              data={response?.data}
              {...{
                currentPage,
                setPage,
                goNext,
                length: highlights.length,
                label: "highlights",
                goPrevious,
                per_page: perPage,
                setPerPage,
              }}
            />
          </div>
        )}
      </div>
      <Portal>
        <CreateHighlight
          show={modals?.create.show}
          toggle={() => toggleModal("create")}
          addHighlight={(highlight) => setHighlights([highlight, ...highlights])}
        />
        {selected.edit && (
          <EditHighlight
            show={modals?.edit.show}
            initialHighlight={selected.edit}
            toggle={() => toggleModal("edit")}
            updateHighlight={(highlight) =>
              setHighlights((highlights) => highlights.map((h) => (h.id === highlight.id ? highlight : h)))
            }
          />
        )}
        {selected.delete && (
          <DeleteHighlight
            show={modals?.delete.show}
            toggle={() => toggleModal("delete")}
            highlight={selected.delete}
            deletionComplete={(highlight) => {
              setHighlights((highlights) => highlights.filter((h) => h.id !== highlight.id));
            }}
          />
        )}
        {selected.details && (
          <HighlightDetailsModal
            show={modals?.details.show}
            toggle={() => toggleModal("details")}
            highlight={selected.details}
            openEditModal={() => editHighlight(selected.details.id)}
          />
        )}
      </Portal>
    </>
  );
};

export default Highlights;

const LoadingPlaceholder = () => {
  return (
    <div className="grid grid-cols-[repeat(auto-fill,minmax(275px,1fr))] gap-5">
      {Array.from({ length: 6 }).map((_, i) => (
        <div key={i} className="w-full p-1.5 rounded-15 border border-grey-border border-opacity-50">
          <div className="bg-grey-loader animate-pulse rounded-10 h-36"></div>

          <div className="flex flex-col mt-3.75">
            <div className="flex items-center justify-between">
              <div className="h-5 bg-grey-loader animate-pulse rounded-15 w-32"></div>
              <div className="h-5 bg-grey-loader animate-pulse rounded-15 w-8"></div>
            </div>
            <div className="h-3 bg-grey-loader animate-pulse rounded-15 w-40 mt-1.5"></div>
            <div className="border-t border-grey-divider mt-2.5 pt-2.5 flex items-center justify-between">
              <div className="h-6 bg-grey-loader animate-pulse rounded-15 w-25"></div>
              <div className="h-10 bg-grey-loader animate-pulse rounded-full w-10"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
