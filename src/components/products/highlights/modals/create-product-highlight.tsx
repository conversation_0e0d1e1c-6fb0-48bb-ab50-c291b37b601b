import { HighlightInterface } from "@/assets/interfaces";
import { AppEvent } from "@/assets/js/utils/event-types";
import useInstagramAuth from "@/components/hooks/useInstagramAuth";
import { emit, useListenerState } from "@/components/hooks/useListener";
import useSearchParams from "@/components/hooks/useSearchParams";
import useSteps from "@/components/hooks/useSteps";
import CreateProductHighlights from "@/components/products/highlights/product-highlight-form";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { ModalBody, ModalFooter, ModalProps } from "@/components/ui/modal";
import authContext from "@/contexts/auth-context";
import { useEffect, useRef, useState } from "react";
import CreateHighlightSuccess from "./create-highlight-success";
interface Props {
  toggle: (state: boolean) => void;
  show: boolean;
  addHighlight: (highlight: HighlightInterface) => void;
}

const CreateHighlight: React.FC<Props> = ({ toggle, show, addHighlight }) => {
  const { store, updateStore } = authContext.useContainer();
  const [highlight, setHighlight] = useState<HighlightInterface | null>(null);
  const { step, stepIndex, changeStep, next, previous } = useSteps(["create", "success"], 0);
  const [isLoading, setIsLoading] = useListenerState(AppEvent.STORE_IS_UPDATING, false);
  const submitBtnRef = useRef<HTMLButtonElement>(null);

  const updateCategory = () => {
    emit(AppEvent.UPDATE_CATEGORIES);
    submitBtnRef.current?.click();
  };

  const onSuccess = (highlight: HighlightInterface) => {
    setHighlight(highlight);
    addHighlight(highlight);
    changeStep("success");
  };

  return (
    <Modal title="Create Product Highlight" toggle={toggle} show={show}>
      <ModalBody>
        {step === "create" && (
          <CreateProductHighlights openModal={() => toggle(true)} {...{ submitBtnRef, storeId: store.id, onSuccess }} />
        )}
        {step === "success" && <CreateHighlightSuccess isEditing={false} highlight={highlight} />}
      </ModalBody>
      <ModalFooter>
        {step === "create" && (
          <AppBtn id="update-category" isBlock disabled={isLoading} onClick={() => updateCategory()} size="lg">
            {isLoading ? "Saving..." : "Save Highlight"}
          </AppBtn>
        )}
        {step === "success" && (
          <AppBtn
            isBlock
            disabled={isLoading}
            onClick={() => {
              changeStep("create");
              toggle(false);
            }}
            size="lg"
          >
            Continue
          </AppBtn>
        )}
      </ModalFooter>
    </Modal>
  );
};
export default CreateHighlight;
