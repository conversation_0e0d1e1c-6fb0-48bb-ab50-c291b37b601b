import { HighlightInterface } from "@/assets/interfaces";
import { AppEvent } from "@/assets/js/utils/event-types";
import { emit, useListenerState } from "@/components/hooks/useListener";
import useSteps from "@/components/hooks/useSteps";
import ProductHighlightForm from "@/components/products/highlights/product-highlight-form";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { ModalBody, ModalFooter, ModalProps } from "@/components/ui/modal";
import authContext from "@/contexts/auth-context";
import { useEffect, useRef, useState } from "react";
import CreateHighlightSuccess from "./create-highlight-success";
interface Props {
  toggle: (state: boolean) => void;
  show: boolean;
  updateHighlight: (highlight: HighlightInterface) => void;
  initialHighlight: HighlightInterface;
}

const CreateHighlight: React.FC<Props> = ({ toggle, show, updateHighlight, initialHighlight }) => {
  const { store, updateStore } = authContext.useContainer();
  const [highlight, setHighlight] = useState<HighlightInterface | null>(initialHighlight);
  const { step, stepIndex, changeStep, next, previous } = useSteps(["edit", "success"], 0);
  const [isLoading, setIsLoading] = useListenerState(AppEvent.STORE_IS_UPDATING, false);
  const submitBtnRef = useRef<HTMLButtonElement>(null);

  const updateCategory = () => {
    emit(AppEvent.UPDATE_CATEGORIES);
    submitBtnRef.current?.click();
  };

  const onSuccess = (highlight: HighlightInterface) => {
    setHighlight(highlight);
    updateHighlight(highlight);
    changeStep("success");
  };


  return (
    <Modal title="Edit Product Highlight" toggle={toggle} show={show}>
      <ModalBody>
        {step === "edit" && (
          <ProductHighlightForm
            {...{ submitBtnRef, onSuccess, storeId: store?.id }}
            highlightToEdit={initialHighlight}
            openModal={()=>toggle(true)}
          />
        )}
        {step === "success" && <CreateHighlightSuccess isEditing={true} highlight={highlight} />}
      </ModalBody>
      <ModalFooter>
        {step === "edit" && (
          <AppBtn id="update-category" isBlock disabled={isLoading} onClick={() => updateCategory()} size="lg">
            {isLoading ? "Saving..." : "Update Highlight"}
          </AppBtn>
        )}
        {step === "success" && (
          <AppBtn
            isBlock
            disabled={isLoading}
            onClick={() => {
              changeStep("edit");
              toggle(false);
            }}
            size="lg"
          >
            Continue
          </AppBtn>
        )}
      </ModalFooter>
    </Modal>
  );
};
export default CreateHighlight;
