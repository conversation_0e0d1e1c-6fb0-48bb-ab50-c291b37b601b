// import EmojiPicker from "emoji-picker-react";
import { generateSimpleUUID, getFieldvalues, getFileType, toCurrency } from "@/assets/js/utils/functions";
import useMediaProcessor from "@/components/hooks/useMediaProcessor";
import useVideoTranscode, { ffmpegContext, VideoStepStatus } from "@/components/hooks/useVideoTranscode";
import VideoUploadCard from "@/components/products/create-products/video-upload-card";
import { useFormik, FormikProps, FormikErrors } from "formik";
import React, { useRef, useState, useEffect, useMemo } from "react";
import * as Yup from "yup";
import { useRequest } from "@/api/utils";
import { HighlightInterface, ProductItemInterface, Video } from "@/assets/interfaces";
import { useModals } from "@/components/hooks/useModals";
import Portal from "../../portal";
import { AppBtn } from "@/components/ui/buttons";
import { InputField } from "@/components/ui/form-elements";
import { useStorefrontItems } from "@/components/hooks/useStoreItems";
import ProcessVideoModal from "../modals/process-video";
import useVideoTrim from "@/components/hooks/useVideoTrim";
import SelectSpecificProductsModal from "../modals/select-specific-products";
import { getProductItemThumbnail, getProductThumbnail, generateVideoObjectFromUrl } from "@/assets/js/utils/utils";
import VideoResourcesLoader from "../modals/video-resources-loader";
import { CreateHighlightParams, UpdateHighlightParams } from "@/api/interfaces/items.interface";
import { CreateHighlight, UpdateHighlight } from "@/api/items";
import { toast } from "@/components/ui/toast";
import VideoUploadProgress from "../modals/video-upload-progress";
import { AppEvent } from "@/assets/js/utils/event-types";
import { emit } from "@/components/hooks/useListener";
import Toggle from "@/components/ui/toggle";
import LazyImage from "@/components/lazy-image";
import { AddCircle, DocumentUpload, Instagram, ShoppingBag, Trash } from "iconsax-react";
import Dropdown, { DropdownItem } from "@/components/ui/dropdown-new";
import InstagramMediaPicker from "@/components/products/create-products/instagram-media-picker";
import { generateKey } from "@/assets/js/utils/image-selection";

interface Props {
  submitBtnRef: React.MutableRefObject<HTMLButtonElement>;
  storeId?: string;
  onSuccess: (highlight: HighlightInterface) => void;
  highlightToEdit?: HighlightInterface;
  openModal: VoidFunction;
}

interface FormProps {
  title: string;
  videos: { video: Video; active?: boolean; products: string[] }[];
  active?: boolean;
  id: string;
}

const ProductHighlightForm: React.FC<Props> = ({ submitBtnRef, storeId, onSuccess, openModal, highlightToEdit }) => {
  const isEdit = !!highlightToEdit;
  const formRef = useRef<FormikProps<FormProps>>(null);
  const mediaPicker = useRef<HTMLInputElement>(null);
  const [currentVideo, setCurrentVideo] = useState<number>(0);
  const [processingVideoFiles, setProcessingVideoFiles] = useState(false);
  const { modals, toggleModal } = useModals([
    "select_items",
    "process_video",
    "loader",
    "progresses",
    "select_video_items",
    "instagram",
  ]);

  const preloadedItems = useMemo(() => {
    if (!highlightToEdit) return [];
    return highlightToEdit?.videos.map((v) => v.products as ProductItemInterface[]).flat();
  }, [highlightToEdit]);

  const { items: storeItems, fetchItemsReq, getItem } = useStorefrontItems(storeId, {}, preloadedItems);
  const {
    items: videoItems,
    fetchItemsReq: fetchVideoItemsReq,
    getItem: getVideoItem,
  } = useStorefrontItems(storeId, { videosOnly: true }, preloadedItems);
  const createHighlightReq = useRequest<CreateHighlightParams>(CreateHighlight);
  const updateHighlightReq = useRequest<UpdateHighlightParams>(UpdateHighlight);

  const form = useFormik<FormProps>({
    initialValues: isEdit
      ? getInitialValues(highlightToEdit)
      : {
          title: "",
          videos: [],
          active: true,
          id: generateSimpleUUID(),
        },
    validationSchema,
    onSubmit: async (values) => {
      let result: any[];

      if (values.videos.length === 0) {
        toast.error({
          title: `Cannot ${isEdit ? "update" : "create"} highlight without videos`,
          message: "Please add at least one video",
        });
        return;
      }

      if (values.videos.some((v) => v.products.length === 0)) {
        toast.error({
          title: `Cannot ${isEdit ? "update" : "create"} highlight without products`,
          message: "Please add at least one product to each video",
        });
        return;
      }

      if (isEdit) {
        result = await updateHighlightReq.makeRequest({
          id: values.id,
          title: values.title,
          active: values.active,
          videos: values.videos.map((v) => ({
            url: v.video.url,
            active: v.active,
            thumbnail: v.video.thumbnail,
            products: v.products,
          })),
        });
      } else {
        result = await createHighlightReq.makeRequest({
          title: values.title,
          videos: values.videos.map((v) => ({
            url: v.video.url,
            active: true,
            thumbnail: v.video.thumbnail,
            products: v.products,
          })),
        });
      }

      const [response, error] = result;

      if (error) {
        toast.error({
          title: `Couldn't ${isEdit ? "update" : "create"} highlight`,
          message: error?.message ?? "Something went wrong, please try again",
        });
      } else {
        onSuccess(response.data);
      }
    },
  });

  // console.log(form)

  useEffect(() => {
    emit(AppEvent.STORE_IS_UPDATING, createHighlightReq.isLoading);
  }, [createHighlightReq.isLoading]);

  // Update formRef whenever form changes
  useEffect(() => {
    formRef.current = form;
  }, [form]); //Hacky but there's an issue with cached values in the useVideoTranscoder callback

  const { videoTrim, setVideoTrim } = useVideoTrim(form.values.videos.map((v) => v.video));

  const { processFiles } = useMediaProcessor(
    (videos) => {
      const videosCopy = form.values.videos;
      videos.forEach(({ type, ...m }) => {
        videosCopy.push({
          video: { ...m },
          products: [],
          active: true,
        });
      });
      form.setFieldValue("videos", videosCopy);
    },
    [],
    null
  );

  const { ffmpegRef, ffmpegLoading, loadProgress } = ffmpegContext.useContainer();
  const { removeVideoProgress, retryVideoTask, videoProgresses, transcodeVideo } = useVideoTranscode(
    (task, url, video) => {
      const currentForm = formRef.current;
      const videosCopy = [...currentForm.values.videos];
      const videoIndex = videosCopy.findIndex((v) => v.video.meta.id === task);

      if (videoIndex !== -1) {
        videosCopy[videoIndex].video.url = url;
        currentForm.setFieldValue("videos", videosCopy);
      }
    },
    { ffmpegRef }
  );

  const removeVideo = (index: number) => {
    const videosCopy = form.values.videos;
    const videoDataCopy = { ...videosCopy[index] };

    removeVideoProgress(videoDataCopy.video.meta.id);
    videosCopy.splice(index, 1);
    form.setFieldValue("videos", videosCopy);
  };

  const handleOpenVideoModal = (m: string, i: number) => {
    if (ffmpegLoading) {
      toggleModal("loader");
      return;
    }
    setCurrentVideo(i);
    toggleModal(m as any);
  };

  /**
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *  */
  function openSelectItems(e: React.MouseEvent<HTMLButtonElement>, index: number) {
    setCurrentVideo(index);
    toggleModal("select_items");
  }

  function removeProduct(e: React.MouseEvent<HTMLButtonElement>, videoIndex: number, productId: string) {
    const updatedVideos = [...form.values.videos];
    const updatedProducts = updatedVideos[videoIndex].products.filter((product) => product !== productId);
    updatedVideos[videoIndex].products = updatedProducts;
    form.setFieldValue("videos", updatedVideos);
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // const isValid = await form.validateForm();
    // console.log({isValid});
    // if (Object.keys(isValid).length > 0) return;
    const videosAreProcessing = Object.values(videoProgresses?.value ?? {}).some(
      (taskProgress) => taskProgress.step !== VideoStepStatus.SUCCESS
    );

    if (videosAreProcessing) {
      toggleModal("progresses");
      return;
    }
    formRef.current.handleSubmit();
  };

  const extractFormVideosFromProducts = (ids: string[]) => {
    const products = ids.map((id) => getVideoItem(id));

    const videos: FormProps["videos"] = products.flatMap((product) => {
      if (product.videos.length > 0) {
        const productVideos = product.videos.map((video) => ({
          video: generateVideoObjectFromUrl(video.url, video.thumbnail),
          products: [product.id],
          active: true,
          thumbnail: video.thumbnail,
        }));
        return productVideos;
      }
      return [];
    });

    form.setFieldValue("videos", [...form.values.videos, ...videos]);
  };

  const handleInstagramVideos = async (urls: string[]) => {
    toggleModal("instagram");

    const filesPromise = Promise.all(
      urls.map(async (url, index) => {
        const blob = await fetch(
          process.env.NEXT_PUBLIC_API_URL + `/utils/download-file?url=${btoa(unescape(encodeURIComponent(url)))}`,
          {
            mode: "cors",
          }
        ).then((r) => r.blob());
        const file = new File([blob], `video-${index.toString()}.mp4`, { type: "video/mp4" });
        return file;
      })
    );

    toast.promise(() => filesPromise, {
      error: {
        title: "Error",
        message: "Something went wrong processing files!",
      },
      loading: {
        title: "Processing...",
        message: "Processing files. Please wait",
      },
      success: {
        title: "Processing Complete",
        message: "Files processed successfully",
      },
    });
    setProcessingVideoFiles(true);
    const files = await filesPromise;
    const videosCopy = form.values.videos;

    files.forEach((file) => {
      videosCopy.push({
        video: {
          name: file.name,
          lastModified: file.lastModified,
          file: file,
          src: URL.createObjectURL(file),
          thumbnail: "",
          key: generateKey(file),
          meta: {
            id: generateSimpleUUID(),
          },
        },
        products: [],
        active: true,
      });
    });

    form.setFieldValue("videos", videosCopy);
    setProcessingVideoFiles(false);
  };

  const addVideoOptions: DropdownItem[] = [
    {
      text: "Upload Manually",
      onClick: () => mediaPicker.current?.click(),
      icon: <DocumentUpload size={16} variant="Outline" className="text-dark" />,
    },
    {
      text: "Select Products With Videos",
      onClick: () => toggleModal("select_video_items"),
      icon: <AddCircle size={16} variant="Outline" className="text-dark" />,
    },
    {
      text: "Upload from Instagram",
      onClick: () => toggleModal("instagram"),
      icon: <Instagram size={16} variant="Outline" className="text-dark" />,
    },
  ];

  return (
    <form onSubmit={handleSubmit} className="w-full pb-20">
      <InputField label={`Highlight Title`} {...getFieldvalues("title", form)} />

      {form.values.videos.map((data, index) => {
        const videoProgress = videoProgresses?.value?.[data.video.meta.id];

        return (
          <div className="relative h-full mt-3.75" key={index}>
            <div className="flex flex-col gap-2 bg-grey-fields-100 items-center rounded-10 relative h-full">
              <div className="flex justify-between items-center w-full px-3 pt-2.5">
                <h2 className="text-black-placeholder text-sm font-semibold">Video {index + 1}</h2>

                {isEdit && (
                  <div className="flex items-center space-x-2.5">
                    <Toggle
                      intialState={form.values.videos[index].active}
                      onChange={(value) => {
                        const updatedVideos = [...form.values.videos];
                        updatedVideos[index].active = value;
                        form.setFieldValue("videos", updatedVideos);
                      }}
                    />

                    {!form.values.videos[index].active && (
                      <button
                        type="button"
                        onClick={() => removeVideo(index)}
                        className="h-6.25 w-6.25 bg-white flex items-center justify-center rounded-full"
                      >
                        <Trash size={15} variant="Outline" className="text-accent-red-500" />
                      </button>
                    )}
                  </div>
                )}
              </div>

              <div className="flex-1 w-full h-full border rounded-10 border-gray-border border-opacity-50 bg-white px-3 py-2.5 my-0 transition-all duration-300 ease-out">
                {!form.values.videos[index].active ? (
                  <div className="h-full">
                    <div
                      className={`w-full flex gap-2 mb-2.5 text-black-muted text-1xs font-normal py-2.5 px-3 rounded-10  bg-accent-yellow-pastel`}
                    >
                      {/* prettier-ignore */}
                      <svg width={16} height={16} viewBox="0 0 16 16" fill="none">
                      <path d="M8.00016 15.1673C4.04683 15.1673 0.833496 11.954 0.833496 8.00065C0.833496 4.04732 4.04683 0.833984 8.00016 0.833984C11.9535 0.833984 15.1668 4.04732 15.1668 8.00065C15.1668 11.954 11.9535 15.1673 8.00016 15.1673ZM8.00016 1.83398C4.60016 1.83398 1.8335 4.60065 1.8335 8.00065C1.8335 11.4007 4.60016 14.1673 8.00016 14.1673C11.4002 14.1673 14.1668 11.4007 14.1668 8.00065C14.1668 4.60065 11.4002 1.83398 8.00016 1.83398Z" fill="currentColor" />
                      <path d="M8 9.16732C7.72667 9.16732 7.5 8.94065 7.5 8.66732V5.33398C7.5 5.06065 7.72667 4.83398 8 4.83398C8.27333 4.83398 8.5 5.06065 8.5 5.33398V8.66732C8.5 8.94065 8.27333 9.16732 8 9.16732Z" fill="currentColor" />
                      <path d="M8.00016 11.3339C7.9135 11.3339 7.82683 11.3139 7.74683 11.2806C7.66683 11.2472 7.5935 11.2006 7.52683 11.1406C7.46683 11.0739 7.42016 11.0072 7.38683 10.9206C7.3535 10.8406 7.3335 10.7539 7.3335 10.6672C7.3335 10.5806 7.3535 10.4939 7.38683 10.4139C7.42016 10.3339 7.46683 10.2606 7.52683 10.1939C7.5935 10.1339 7.66683 10.0872 7.74683 10.0539C7.90683 9.98724 8.0935 9.98724 8.2535 10.0539C8.3335 10.0872 8.40683 10.1339 8.4735 10.1939C8.5335 10.2606 8.58016 10.3339 8.6135 10.4139C8.64683 10.4939 8.66683 10.5806 8.66683 10.6672C8.66683 10.7539 8.64683 10.8406 8.6135 10.9206C8.58016 11.0072 8.5335 11.0739 8.4735 11.1406C8.40683 11.2006 8.3335 11.2472 8.2535 11.2806C8.1735 11.3139 8.08683 11.3339 8.00016 11.3339Z" fill="currentColor" />
                    </svg>
                      <span className="flex-1 inline-block text-xs sm:text-1xs">
                        This video will not be shown to customers on your highlight.
                      </span>
                    </div>
                    <div className="opacity-50 cursor-not-allowed pointer-events-none">
                      <VideoUploadCard
                        key={index}
                        retryTask={() => retryVideoTask(data.video.meta.id)}
                        videoProgress={videoProgress}
                        openVideoModal={(m) => handleOpenVideoModal(m, index)}
                        removeVideo={() => removeVideo(index)}
                        video={data.video}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="h-full">
                    <VideoUploadCard
                      key={index}
                      retryTask={() => retryVideoTask(data.video.meta.id)}
                      videoProgress={videoProgress}
                      openVideoModal={(m) => handleOpenVideoModal(m, index)}
                      removeVideo={() => removeVideo(index)}
                      video={data.video}
                    />
                    <div className="">
                      {data.products.length < 1 && (
                        <div
                          className={`w-full flex gap-2 my-3.75 text-black-muted text-1xs font-normal py-2.5 px-3 rounded-10 bg-accent-yellow-pastel `}
                        >
                          {/* prettier-ignore */}
                          <svg width={16} height={16} viewBox="0 0 16 16" fill="none">
                        <path d="M8.00016 15.1673C4.04683 15.1673 0.833496 11.954 0.833496 8.00065C0.833496 4.04732 4.04683 0.833984 8.00016 0.833984C11.9535 0.833984 15.1668 4.04732 15.1668 8.00065C15.1668 11.954 11.9535 15.1673 8.00016 15.1673ZM8.00016 1.83398C4.60016 1.83398 1.8335 4.60065 1.8335 8.00065C1.8335 11.4007 4.60016 14.1673 8.00016 14.1673C11.4002 14.1673 14.1668 11.4007 14.1668 8.00065C14.1668 4.60065 11.4002 1.83398 8.00016 1.83398Z" fill="currentColor" />
                        <path d="M8 9.16732C7.72667 9.16732 7.5 8.94065 7.5 8.66732V5.33398C7.5 5.06065 7.72667 4.83398 8 4.83398C8.27333 4.83398 8.5 5.06065 8.5 5.33398V8.66732C8.5 8.94065 8.27333 9.16732 8 9.16732Z" fill="currentColor" />
                        <path d="M8.00016 11.3339C7.9135 11.3339 7.82683 11.3139 7.74683 11.2806C7.66683 11.2472 7.5935 11.2006 7.52683 11.1406C7.46683 11.0739 7.42016 11.0072 7.38683 10.9206C7.3535 10.8406 7.3335 10.7539 7.3335 10.6672C7.3335 10.5806 7.3535 10.4939 7.38683 10.4139C7.42016 10.3339 7.46683 10.2606 7.52683 10.1939C7.5935 10.1339 7.66683 10.0872 7.74683 10.0539C7.90683 9.98724 8.0935 9.98724 8.2535 10.0539C8.3335 10.0872 8.40683 10.1339 8.4735 10.1939C8.5335 10.2606 8.58016 10.3339 8.6135 10.4139C8.64683 10.4939 8.66683 10.5806 8.66683 10.6672C8.66683 10.7539 8.64683 10.8406 8.6135 10.9206C8.58016 11.0072 8.5335 11.0739 8.4735 11.1406C8.40683 11.2006 8.3335 11.2472 8.2535 11.2806C8.1735 11.3139 8.08683 11.3339 8.00016 11.3339Z" fill="currentColor" />
                      </svg>
                          <span className="flex-1 inline-block text-xs sm:text-1xs">
                            Link the products in the video so customers can buy easily.
                          </span>
                        </div>
                      )}
                      {data?.products.length > 0 && (
                        <div className="mt-3.75 mb-1 flex gap-2 flex-wrap">
                          {data.products.map((product, _index) => (
                            <div
                              key={_index}
                              className="flex flex-1 min-w-[220px] bg-grey-fields-200 rounded-15 p-1.5 items-center rounded-t-10 border-grey-border border-opacity-50 justify-between gap-x-0.5"
                            >
                              <div className="flex items-center overflow-hidden">
                                <figure className="h-[34px] w-[34px] sm:h-10 sm:w-10 rounded-10 overflow-hidden flex-shrink-0 relative">
                                  <LazyImage
                                    src={getProductItemThumbnail(getItem(product))}
                                    alt=""
                                    className="h-full w-full object-cover"
                                    loaderClasses="w-full h-full"
                                  />
                                </figure>
                                <div className="ml-2 flex flex-col gap-y-0.5 flex-1 overflow-hidden justify-start items-start">
                                  <h5 className="text-black-muted font-display font-bold overflow-hidden text-1xs max-w-full whitespace-nowrap overflow-ellipsis mb-[-2px]">
                                    {getItem(product)?.name}{" "}
                                  </h5>
                                  <p className="text-black font-semibold overflow-hidden text-xs max-w-full whitespace-nowrap overflow-ellipsis ">
                                    {toCurrency(getItem(product)?.price)}{" "}
                                  </p>
                                </div>
                              </div>
                              <button
                                className="flex items-center justify-center  rounded-full disabled:text-grey-muted group flex-shrink-0 bg-white w-7.5 h-7.5 text-red-500"
                                type="button"
                                onClick={(e) => removeProduct(e, index, product)}
                              >
                                {/* prettier-ignore */}
                                <svg width="50%" viewBox="0 0 15 16" fill="none">
                              <path d="M11.25 4.25L3.75 11.75M3.75 4.25L11.25 11.75" stroke="#BF0637" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                      <button
                        type="button"
                        className="flex items-center justify-center font-medium whitespace-nowrap bg-white hover:text-primary-700 text-primary-700 h-9 mt-1 text-1xs sm:text-sm rounded-[8px] px-0 font-action !outline-none transition-all cursor-pointer box-border group flex-shrink-0"
                        onClick={(e) => openSelectItems(e, index)}
                      >
                        + Add Products
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}

      <input
        type="file"
        ref={mediaPicker}
        name="product-medias"
        accept={"video/mp4,webm,video/quicktime,ogg"}
        id="product-medias"
        className="hidden"
        multiple={true}
        onChange={(e) => processFiles(e.target.files)}
      />

      <div className=" mt-5">
        <Dropdown items={addVideoOptions} className="w-full" fullWidth>
          <AppBtn disabled={processingVideoFiles} size="md" color="neutral" className="w-full dropdown-toggle relative">
            {processingVideoFiles ? "Processing..." : "+ Add New Video"}
          </AppBtn>
        </Dropdown>
      </div>
      <button type="submit" className="hidden" ref={submitBtnRef}></button>

      <Portal>
        {currentVideo !== null && (
          <SelectSpecificProductsModal
            onSearch={() => {}}
            onSave={(items: string[]) => {
              form.setFieldValue(`videos[${currentVideo}].products`, items);
              toggleModal("select_items");
            }}
            items={storeItems}
            show={modals.select_items.show}
            toggle={() => toggleModal("select_items")}
            value={form.values.videos[currentVideo]?.products || []}
            loadingItems={fetchItemsReq?.isLoading}
          />
        )}

        {videoItems.length > 0 && (
          <SelectSpecificProductsModal
            onSearch={() => {}}
            onSave={(items: string[]) => {
              extractFormVideosFromProducts(items);
              toggleModal("select_video_items");
            }}
            items={videoItems}
            show={modals.select_video_items.show}
            toggle={() => toggleModal("select_video_items")}
            value={[]}
            loadingItems={fetchItemsReq?.isLoading}
          />
        )}

        {form.values.videos?.length > 0 && form.values.videos?.[currentVideo]?.video !== null && (
          <ProcessVideoModal
            currentVideo={form.values.videos?.[currentVideo]?.video}
            onTrim={(start, end) => {
              const newVideoTrim = [...videoTrim];
              newVideoTrim[currentVideo] = { start, end };
              setVideoTrim(newVideoTrim);
            }}
            show={modals.process_video.show}
            toggle={() => toggleModal("process_video")}
            value={videoTrim[currentVideo]}
            setVideo={(video) => {
              const videosCopy = [...form.values.videos];
              videosCopy[currentVideo] = { ...videosCopy[currentVideo], video };
              form.setFieldValue("videos", videosCopy);
            }}
            transcodeVideo={(dims) => {
              const video = form.values.videos[currentVideo]?.video;
              transcodeVideo({
                dimensions: dims,
                trim: videoTrim[currentVideo],
                taskId: video.meta.id,
                file: video.file as any,
                meta: {
                  videoIndex: currentVideo,
                },
              });
            }}
          />
        )}

        <VideoResourcesLoader
          toggle={() => toggleModal("loader")}
          show={modals.loader.show}
          loadProgress={loadProgress}
        />

        <VideoUploadProgress
          retryTranscode={retryVideoTask}
          // productForm={form}
          videos={form.values.videos.map((v) => v.video)}
          progresses={videoProgresses.value}
          show={modals.progresses.show}
          toggle={() => toggleModal("progresses")}
        />

        <InstagramMediaPicker
          mediaType="VIDEO"
          show={modals.instagram.show}
          toggleModal={(s) => toggleModal("instagram", s)}
          handleMediaSelect={handleInstagramVideos}
        />
      </Portal>
    </form>
  );
};

export default ProductHighlightForm;

const validationSchema = Yup.object({
  title: Yup.string().required("Please provide highlight title"),
  videos: Yup.array().of(
    Yup.object({
      products: Yup.array()
        .of(Yup.string().required())
        .min(1, "Please select at least one product")
        .required("Please select at least one product"),
    })
  ),
});

const ensureProductsAreStrings = (products: any[]) => {
  return typeof products[0] === "string" ? products : products.map((p) => p.id);
};

const getInitialValues = (highlight: HighlightInterface) => {
  return {
    title: highlight?.title,
    videos: highlight?.videos.map((v) => ({
      video: generateVideoObjectFromUrl(v.url, v.thumbnail),
      products: ensureProductsAreStrings(v.products),
      thumbnail: v.thumbnail,
      active: v.active,
    })),
    active: highlight.active,
    id: highlight.id,
  };
};
