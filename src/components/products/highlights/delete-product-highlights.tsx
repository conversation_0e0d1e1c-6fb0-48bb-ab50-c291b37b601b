// import EmojiPicker from "emoji-picker-react";
import <PERSON><PERSON>, { <PERSON>dal<PERSON>ody, ModalFooter } from "@/components/ui/modal";
import React from "react";
import { HighlightInterface } from "@/assets/interfaces";
import { AppBtn } from "@/components/ui/buttons";
import { useRequest } from "@/api/utils";
import { DeleteHighlightParams } from "@/api/interfaces";
import { DeleteHighlight as DeleteHighlightApi } from "@/api/items";
import ErrorLabel from "@/components/ui/error-label";

interface DelProps {
  show: boolean;
  toggle: (status: boolean) => void;
  highlight: HighlightInterface;
  deletionComplete: (highlight: HighlightInterface) => void;
}

const DeleteHighlight: React.FC<DelProps> = ({ show, toggle, highlight, deletionComplete }) => {
  const { makeRequest, isLoading, error, response } = useRequest(DeleteHighlightApi);

  const handleItemDelete = async () => {
    const [res, err] = await makeRequest({ id: highlight.id });

    if (!err) {
      deletionComplete(highlight);
      toggle(false);
    }
  };

  return (
    <Modal {...{ show, toggle }} title="Delete Product Highlight" size="sm">
      <ModalBody>
        <ErrorLabel error={error?.message} />
        {/* <SuccessLabel message={response ? "Block deleted successfully!" : ""} /> */}
        <div className="text-center py-3.5">
          <h4 className="text-black text-base font-semibold">Do you want to delete this highlight?</h4>
          <p className="text-sm text-grey-subtext mt-1 max-w-xs mx-auto">
            This highlight would be completely removed from the list.
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock color="danger" onClick={handleItemDelete} disabled={isLoading} size="lg">
          {isLoading ? "Deleting..." : "Delete Highlight"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default DeleteHighlight;
