import { useEffect, useState } from "react";
import { GetItemsParams } from "../../api/interfaces/items.interface";
import { GetItems, GetTieredPricing, GetVariantTemplates } from "../../api/items";
import { useFetcher, useRequest } from "../../api/utils";
import { InfoBlockInterface, PricingTierInterface, ProductItemInterface } from "../../assets/interfaces";
import { reloadPage } from "../../assets/js/utils/functions";
import { AppBtn } from "../../components/ui/buttons";
import Table, { TableBody, TableHead, TableHeadItem } from "../../components/ui/table";
import authContext from "../../contexts/auth-context";
import ClearSearch from "../clear-search";
import usePagination from "../hooks/usePagination";
import useScreenSize from "../hooks/useScreenSize";
import useSearchParams from "../hooks/useSearchParams";
import ContentState from "../ui/content-state";
import { productPageIcons } from "../ui/layouts/product";
import Pagination from "../ui/pagination";
import ProductItem from "./product-item";
import TableContent from "./product-table-content";
import ItemCard from "./item-card-mobile";
import { Product } from "@/pages/products/create";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import ErrorBox from "../ui/error";
import SelectAffiliate from "./modals/select-affiliate";
import { useModals } from "../hooks/useModals";
import { GetStoreInfoBlocks } from "@/api";
import { useStorefrontItems } from "../hooks/useStoreItems";

const ProductTableContent = TableContent(ProductItem);
const ProductTableContentMobile = TableContent(ItemCard);

interface Props {}
const ItemsList: React.FC<Props> = ({}) => {
  const { currentPage, perPage, setPerPage, goNext, goPrevious, setPage } = usePagination();
  const [items, setItems] = useState<ProductItemInterface[]>([]);
  const { modals, toggleModal } = useModals(["select_affiliate"]);
  const { search } = useSearchParams(["search"]);
  const { response, error, isLoading, makeRequest } = useRequest<GetItemsParams>(GetItems);
  const { width, isSmall } = useScreenSize();
  const { categories, store, subscription } = authContext.useContainer();
  const itemsHaveQuantity = items.some((i) => i.is_always_available || i.quantity > -1);
  const [selectedProduct, setSelectedProduct] = useState<ProductItemInterface | null>(null);

  const getvariantTemplatesReq = useFetcher(GetVariantTemplates, {});
  const templates: Product["variants"][] = getvariantTemplatesReq.response?.data?.templates || [];

  const { items: storeItems, fetchItemsReq, getItem } = useStorefrontItems(store?.id);
  const blocksReq = useFetcher(GetStoreInfoBlocks, { id: store?.id });
  const pricingTiersReq = useFetcher(GetTieredPricing, {
    filter: {},
    page: 1,
    per_page: Number.MAX_SAFE_INTEGER,
  });
  const [pricingTiers, setPricingTiers] = useState<PricingTierInterface[]>([]);
  const [infoBlocks, setInfoBlocks] = useState<InfoBlockInterface[]>([]);
  const [isDuplication, setIsDuplication] = useState(false);

  useEffect(() => {
    if (blocksReq.response?.data?.info_blocks) {
      setInfoBlocks(blocksReq.response?.data?.info_blocks);
    }
  }, [blocksReq.response]);

  useEffect(() => {
    if (pricingTiersReq.response?.data) {
      setPricingTiers(pricingTiersReq.response?.data);
    }
  }, [pricingTiersReq.response]);

  /*  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    const [res, err] = await getvariantTemplatesReq.makeRequest({});
    if (res && !err) {
      const templates = res?.data?.templates;
      if (templates && templates.length > 0) {
        setTemplates(templates);
      }
    }
  }; */

  useEffect(() => {
    const s = new URLSearchParams(search).get("search");

    if (currentPage) {
      fetchItems(s ?? search);
    }
  }, [currentPage, search, perPage]);

  useEffect(() => {
    setItems(response?.data.items || []);
  }, [response]);

  const updateItem = (item: ProductItemInterface, index: number) => {
    const itemsCopy = [...items];
    itemsCopy[index] = item;

    setItems(itemsCopy);
  };

  const updateItemList = (item: ProductItemInterface, isDuplicate = false) => {
    if (isDuplicate === true) {
      const itemsCopy = [item, ...items];
      setItems(itemsCopy);
      return;
    }
    const itemsCopy = [...items];
    itemsCopy.push(item);
    setItems(itemsCopy);
  };

  async function fetchItems(search: string) {
    const [res, err] = await makeRequest({
      filter: { store: store?.id, search: search ? search : "" },
      page: currentPage,
      per_page: perPage,
      sort: "asc",
    });
  }

  const deleteItem = (index: number) => {
    const itemsCopy = [...items];
    itemsCopy.splice(index, 1);

    setItems(itemsCopy);
  };

  const canManageItems = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_ITEMS,
  });

  const selectAffiliateLink = (product: ProductItemInterface) => {
    setSelectedProduct(product);
    toggleModal("select_affiliate");
  };

  let canManageAffiliates = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_AFFILIATES,
  });

  if (!canManageItems) {
    return (
      <ErrorBox title="Upgrade required" message="Upgrade to the basic or business plus plan to manage products">
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  if (isLoading || error || !response?.data || items.length < 1) {
    return (
      <>
        <ClearSearch search={search} />
        <ContentState
          isLoading={isLoading}
          loadingText="Loading products"
          errorTitle="Couldn't load items"
          error={error}
          errorMessage="We couldn't load your items please click the button to retry"
          errorAction={
            <AppBtn size="md" onClick={reloadPage}>
              Reload Items
            </AppBtn>
          }
          isEmpty={items.length < 1}
          emptyIcon={
            // prettier-ignore
            <svg width="45%" viewBox="0 0 20 20" fill="none">
              <path d="M17.534 5.99169L10.0006 10.35L2.46729 5.99169C2.80062 5.37502 3.28395 4.83335 3.82562 4.53335L8.27562 2.06669C9.22562 1.53335 10.7756 1.53335 11.7256 2.06669L16.1756 4.53335C16.7173 4.83335 17.2006 5.37502 17.534 5.99169Z" fill="#F0F0F0"/>
              <path d="M10.0005 10.35V18.3333C9.37552 18.3333 8.75052 18.2 8.27552 17.9333L3.82552 15.4666C2.81719 14.9083 1.99219 13.5083 1.99219 12.3583V7.64164C1.99219 7.1083 2.17552 6.52497 2.46719 5.99164L10.0005 10.35Z" fill="#B1B1B1"/>
              <path d="M18.0088 7.64164V12.3583C18.0088 13.5083 17.1838 14.9083 16.1755 15.4666L11.7255 17.9333C11.2505 18.2 10.6255 18.3333 10.0005 18.3333V10.35L17.5338 5.99164C17.8255 6.52497 18.0088 7.1083 18.0088 7.64164Z" fill="#858484"/>
            </svg>
          }
          title="No Products to show"
          description="Create a new product"
        >
          <AppBtn size="md" className="max-w-[240px] m-auto" href={`/products/create`}>
            Create Product
          </AppBtn>
        </ContentState>
      </>
    );
  }

  if (response?.data && items.length > 0) {
    return (
      <>
        <ClearSearch search={search} />
        {!isSmall && (
          <Table>
            <TableHead>
              <TableHeadItem>Item</TableHeadItem>
              {itemsHaveQuantity ? (
                <TableHeadItem className=" hidden sm:table-cell">Quantity</TableHeadItem>
              ) : (
                <TableHeadItem className=" hidden sm:table-cell">category</TableHeadItem>
              )}
              <TableHeadItem className=" hidden sm:table-cell">price</TableHeadItem>
              <TableHeadItem className=" hidden sm:table-cell">Options</TableHeadItem>
              <TableHeadItem>Actions</TableHeadItem>
              <TableHeadItem>availabilty</TableHeadItem>
            </TableHead>
            <TableBody>
              {items.map((product, index) => (
                <ProductTableContent
                  product={product}
                  key={product.id}
                  categories={categories}
                  updateItem={(item) => updateItem(item, index)}
                  updateItemList={updateItemList}
                  deleteItem={() => deleteItem(index)}
                  itemsHaveQuantity={itemsHaveQuantity}
                  templates={templates}
                  selectAffiliate={selectAffiliateLink}
                  canManageAffiliates={canManageAffiliates}
                  infoBlocks={infoBlocks}
                  blocksLoading={blocksReq.isLoading}
                  blocksError={blocksReq.error}
                  setInfoBlocks={setInfoBlocks}
                  pricingTiers={pricingTiers}
                  setPricingTiers={setPricingTiers}
                  storeItems={storeItems}
                  fetchItemsReq={fetchItemsReq}
                  getItem={getItem}
                />
              ))}
            </TableBody>
          </Table>
        )}
        {isSmall && (
          <ul className="block sm:hidden">
            {items.map((product, index) => {
              return (
                <ProductTableContentMobile
                  product={product}
                  key={product.id}
                  categories={categories}
                  updateItemList={updateItemList}
                  updateItem={(item) => updateItem(item, index)}
                  deleteItem={() => deleteItem(index)}
                  itemsHaveQuantity={itemsHaveQuantity}
                  templates={templates}
                  selectAffiliate={selectAffiliateLink}
                  canManageAffiliates={canManageAffiliates}
                  infoBlocks={infoBlocks}
                  blocksLoading={blocksReq.isLoading}
                  blocksError={blocksReq.error}
                  setInfoBlocks={setInfoBlocks}
                  pricingTiers={pricingTiers}
                  setPricingTiers={setPricingTiers}
                  storeItems={storeItems}
                  fetchItemsReq={fetchItemsReq}
                  getItem={getItem}
                />
              );
            })}
          </ul>
        )}
        <Pagination
          data={response}
          {...{
            currentPage,
            setPage,
            goNext,
            length: items.length,
            label: "products",
            goPrevious,
            per_page: perPage,
            setPerPage,
          }}
        />
        <SelectAffiliate
          show={modals.select_affiliate.show}
          toggle={() => toggleModal("select_affiliate")}
          productSlug={selectedProduct?.slug}
        />
      </>
    );
  }

  return null;
};

export default ItemsList;
