import { FormikErrors, FormikProps } from "formik";
import { Trash } from "iconsax-react";
import React, { useEffect, useState } from "react";
import { VariantItem, Image } from "../../../assets/interfaces";
import { getFieldvalues, toCurrency } from "../../../assets/js/utils/functions";
import { Product } from "../../../pages/products/create";
import LazyImage from "../../lazy-image";
import { AppBtn } from "../../ui/buttons";
import ErrorLabel from "../../ui/error-label";
import Table, { TableHeadItem, TableBody, TableCell, TableHead, TableRow } from "../../ui/table";
import Toggle from "../../ui/toggle";
import { ExtraOptionsFormType } from "./image-variants";

interface Props {
  product: Product;
  images: Image[];
  form: FormikProps<ExtraOptionsFormType>;
  setFieldValue: (
    field: string,
    value: any,
    shouldValidate?: boolean
  ) => Promise<FormikErrors<ExtraOptionsFormType>> | Promise<void>;
}

const ImageVariantsExtraOption: React.FC<Props> = ({ form, setFieldValue, product, images }) => {
  const name = form.values.extraOption.name;
  const values = form.values.extraOption.values;
  const variantOptions = form.values.options;
  const [error, setError] = useState("");
  const [editing, setEditing] = useState(null);

  useEffect(() => {
    const actualValues = values.filter((v) => v !== "");

    if (actualValues.length < 1) {
      return;
    }

    const variants: VariantItem[] = [];

    actualValues.forEach((v) => {
      images.forEach((i) => {
        const existingOption = variantOptions.find((o) => o.image === i.url && o.values[name] === v);

        if (existingOption) {
          variants.push(existingOption);
        } else {
          variants.push({
            image: i.url,
            price: Number(product.price),
            is_available: true,
            values: { [name]: v },
          });
        }
      });
    });

    setFieldValue("options", variants);
  }, [values]);

  const handleValueChange = (value: string, index: number) => {
    const valuesCopy = [...values];

    valuesCopy[index] = value;
    setFieldValue("extraOption.values", valuesCopy);
  };

  const removeValue = (index: number) => {
    const valuesCopy = [...values];

    if (valuesCopy.length <= 2) {
      setError("You must have at least two options, edit the value instead");
      return;
    }

    valuesCopy.splice(index, 1);
    setFieldValue("extraOption.values", valuesCopy);
  };

  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    //this will add a new value input
    if (e.code === "Enter") {
      const target = e.target as HTMLInputElement;
      e.preventDefault();
      e.stopPropagation();

      if (index === values.length - 1) {
        //create new input under the specified type
        handleValueChange("", values.length);
      } else {
        target.blur();
      }
    }

    //this will delete a value input
    if (e.code === "Backspace") {
      const target = e.target as HTMLInputElement;

      if (target.value === "") {
        e.preventDefault();
        e.stopPropagation();

        //remove this input
        removeValue(index);
      }
    }
  };

  const togglePriceEdit = (index: number) => {
    if (index === editing) {
      setEditing(null);
      return;
    }

    setEditing(index);
  };

  const updateOptions = (value: VariantItem, index: number) => {
    const optionsCopy = [...variantOptions];

    optionsCopy[index] = value;
    setFieldValue("options", optionsCopy);
  };

  const getInputPrice = (price: any) => {
    price = Number(price);
    return price === 0 ? null : price;
  };

  const unsetExtraOption = () => {
    setFieldValue("extraOption.name", "");
    setFieldValue("extraOption.values", []);
  };

  return (
    <div className="mt-4">
      <ErrorLabel error={error} setError={setError} />
      <div className="relative">
        <input
          {...getFieldvalues("extraOption.name", form)}
          placeholder="Enter option group name e.g. Size, Weight"
          className="w-full h-10 border border-grey-input focus:border-primary-500 no-outline text-1xs px-4 rounded-lg transition-all ease-out duration-150"
          autoFocus
        />
        <button
          onClick={unsetExtraOption}
          type="button"
          className="absolute hidden  items-center justify-center right-0 top-0 h-10 w-10 text-grey-subtext hover:text-dark"
        >
          <Trash size={15} variant="Outline" className="" />
        </button>
      </div>

      {name !== "" && (
        <div>
          {values.map((value, index) => (
            <div className="flex items-center space-x-3 mt-3.75 pl-5 relative" key={index}>
              <input
                value={value}
                name={`value-${index}`}
                onChange={(e) => handleValueChange(e.target.value, index)}
                onKeyDown={(e) => handleKeyDown(e, index)}
                placeholder={`Add a value for ${name}`}
                className="w-full h-10 border border-grey-input focus:border-primary-500 no-outline text-1xs px-4 rounded-lg transition-all ease-out duration-150"
              />
              <button
                className="absolute flex items-center justify-center right-0 h-9 w-9 text-grey-subtext hover:text-dark"
                type="button"
                onClick={() => removeValue(index)}
              >
                {/* prettier-ignore */}
                <svg width="16" viewBox="0 0 15 15" fill="none">
                    <path d="M11.25 3.75L3.75 11.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M3.75 3.75L11.25 11.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
              </button>
            </div>
          ))}

          <div className="flex items-center mt-5">
            <AppBtn size="sm" color="neutral" className="ml-auto" onClick={() => handleValueChange("", values.length)}>
              Add new +
            </AppBtn>
          </div>
        </div>
      )}

      {variantOptions.length > 0 && (
        <Table className="mt-5">
          <TableHead>
            <TableHeadItem>Image</TableHeadItem>
            <TableHeadItem>{name}</TableHeadItem>
            <TableHeadItem>Price</TableHeadItem>
            <TableHeadItem>Availability</TableHeadItem>
          </TableHead>
          <TableBody>
            {variantOptions.map((o, index) => (
              <TableRow key={index}>
                <TableCell className="text-1xs">
                  <figure className="h-8 w-8 rounded-5 overflow-hidden relative">
                    <LazyImage src={o.image} alt="" className="w-full h-full object-cover" />
                  </figure>
                </TableCell>
                <TableCell>{o.values[name]}</TableCell>
                <TableCell stopBubble>
                  {index !== editing && (
                    <div
                      className="text-1xs flex items-center cursor-pointer font-action text-dark"
                      onClick={() => togglePriceEdit(index)}
                    >
                      {toCurrency(o.price)}
                      <svg width="12" viewBox="0 0 12 12" fill="none" className="ml-1.5 -mt-0.5">
                        <path
                          d="M8.5 1.41421C8.63132 1.28289 8.78722 1.17872 8.9588 1.10765C9.13038 1.03658 9.31428 1 9.5 1C9.68572 1 9.86962 1.03658 10.0412 1.10765C10.2128 1.17872 10.3687 1.28289 10.5 1.41421C10.6313 1.54554 10.7355 1.70144 10.8066 1.87302C10.8776 2.0446 10.9142 2.2285 10.9142 2.41421C10.9142 2.59993 10.8776 2.78383 10.8066 2.95541C10.7355 3.12699 10.6313 3.28289 10.5 3.41421L3.75 10.1642L1 10.9142L1.75 8.16421L8.5 1.41421Z"
                          stroke="currentColor"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  )}
                  {index === editing && (
                    <input
                      autoFocus
                      type="number"
                      inputMode="numeric"
                      onBlur={() => togglePriceEdit(index)}
                      value={o.price}
                      onChange={(e) => updateOptions({ ...o, price: getInputPrice(e.target.value) }, index)}
                      className="border-dark border border-opacity-30 focus:border-opacity-60 no-outline h-6 w-20 text-1xs font-action px-2.5 flex items-center bg-transparent rounded-5 text-dark"
                    />
                  )}
                </TableCell>
                <TableCell onClick={(e) => e.stopPropagation()}>
                  <Toggle
                    intialState={o.is_available}
                    onChange={(state) => updateOptions({ ...o, is_available: state }, index)}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
};

export default ImageVariantsExtraOption;
