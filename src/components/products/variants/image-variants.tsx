import { useFormik } from "formik";
import React, { useEffect, useRef, useState } from "react";
import { FILE_TYPES, Image, VariantItem } from "../../../assets/interfaces";
import {
  getFieldvalues,
  getRandString,
  handleImageSelectionFromFile,
  toCurrency,
} from "../../../assets/js/utils/functions";
import { Product } from "../../../pages/products/create";
import useImageUploads from "../../hooks/useImageUploads";
import LazyImage from "../../lazy-image";
import { AppBtn } from "../../ui/buttons";
import ErrorLabel from "../../ui/error-label";
import { SelectDropdown } from "../../ui/form-elements";
import ImageVariantsExtraOption from "./image-extra-options";

interface Props {
  product: Product;
  variants: VariantItem[];
  setVariants: (variants: VariantItem[]) => void;
}

export interface ExtraOptionsFormType {
  hasExtraOption: boolean;
  extraOption: {
    name: string;
    values: string[];
  };
  options: VariantItem[];
}

const ImageVariants: React.FC<Props> = ({ product, variants, setVariants }) => {
  const [imagesUploading, setImagesUploading] = useState(false);
  const imagePicker = useRef<HTMLInputElement>(null);
  const [step, setStep] = useState<"images" | "values">("images");
  const [error, setError] = useState("");
  const [editPrice, setEditPrice] = useState<number>(null);
  const [removedImages, setRemovedImages] = useState<string[]>([]);
  const [images, setImages] = useState<Image[]>(
    (() => {
      let ixs: Image[] = [];

      variants.forEach((v) => {
        if (ixs.findIndex((i) => i.url === v.image) === -1) {
          const [name, lastModified] = [`${getRandString(10)}.JPG`, +new Date()];

          ixs.push({
            url: v.image,
            file: null,
            isUploading: false,
            uploadProgress: 100,
            name,
            src: "",
            lastModified,
          });
        }
      });

      return ixs;
    })()
  );

  const form = useFormik<ExtraOptionsFormType>({
    initialValues: {
      hasExtraOption: false,
      extraOption: {
        name: "",
        values: ["", ""],
      },
      options: [],
    },
    onSubmit: (values) => {
      switch (step) {
        case "images":
          setStep("values");
          break;
        case "values":
          const actualValues = values.extraOption.values.filter((v) => v !== "");

          if (actualValues.length < 2) {
            setError("You should have at least two option values");
          } else {
            // console.log(form.values.options)
            // return
            setVariants(form.values.options);
            submit();
          }
        default:
        //do nothing
      }
    },
  });

  const hasExtraOption = form.values.hasExtraOption;

  useImageUploads(images, FILE_TYPES.ITEMS, setImages);
  //handle updating the extra options state from saved data
  useEffect(() => {
    const extraOptionText = variants[0]?.values ? Object.keys(variants[0]?.values)[0] : null;

    if (extraOptionText) {
      const optionValues = Array.from(new Set(variants.map((v) => Object.values(v.values)[0])));
      const extraOption = {
        name: extraOptionText,
        values: optionValues,
      };

      form.setValues({
        hasExtraOption: true,
        extraOption,
        options: variants,
      });
    }
  }, []);

  useEffect(() => {
    setVariants(form.values.options);
  }, [form.values.options]);

  //loading is used to delay this running until the extra variants have been calculated from stored data
  //if this runs before the extra variants, the extra varaints options will be lost
  useEffect(() => {
    setImagesUploading(images.some((i) => i.isUploading));
    const newVariants = images
      .filter((i) => !removedImages.includes(i.url))
      .map((i) => {
        const variant = variants.find((v) => v.image === i.url);

        if (variant) {
          return variant;
        }

        return {
          image: i.url,
          price: Number(product.price),
          is_available: true,
        };
      });

    setVariants(newVariants);
  }, [images]);

  useEffect(() => {
    if (!form.values.hasExtraOption) {
      setStep("images");
    }
  }, [form.values.hasExtraOption]);

  const removeVariant = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id) => {
    e.stopPropagation();

    if (images.length < 3) {
      setError("You must have at least 2 options, add new images, then remove this variant");
      return;
    }

    const imagesCopy = [...images];
    const removed = imagesCopy.splice(id, 1);

    setRemovedImages([...removedImages, removed[0].url]);
    setImages(imagesCopy);
  };

  const togglePriceEdit = (index: number) => {
    if (index === editPrice) {
      setEditPrice(null);
      return;
    }

    setEditPrice(index);
  };

  const updateVariantPrice = (price: string | number) => {
    const variantsCopy = [...variants];

    price = Number(price);
    variantsCopy[editPrice].price = price === 0 ? null : price;
    setVariants(variantsCopy);
  };

  const updateVariantsFromImages = () => {
    const newImages = [...images];

    product.images.forEach((image) => {
      const imageExists = images.findIndex((i) => i.url === image.url) !== -1;

      if (!imageExists) {
        newImages.push({ ...image });
      }
    });

    setRemovedImages([]);
    setImages(newImages);
  };

  const handleSubmit = () => {
    const newVariants = images
      .filter((i) => !removedImages.includes(i.url))
      .map((i) => {
        const variant = variants.find((v) => v.image === i.url);

        if (variant) {
          delete variant.values;
          return variant;
        }

        return {
          image: i.url,
          price: Number(product.price),
          is_available: true,
        };
      });
    // console.log(newVariants)
    // return
    setVariants(newVariants);
    submit();
  };

  const submit = () => {
    //programatically click on hidden footer to trigger form submit
    const variantsFooterButton = document
      .querySelector(".variants-footer")
      .querySelector("button") as HTMLButtonElement;
    variantsFooterButton.click();
  };

  return (
    <div className="mt-4">
      <ErrorLabel error={error} />
      <SelectDropdown
        label="This product has extra options e.g. Size, Weight?"
        options={[
          { value: "yes", text: "Yes" },
          { value: "no", text: "No" },
        ]}
        {...getFieldvalues("hasExtraOptions", form)}
        onChange={(e) => form.setFieldValue("hasExtraOption", e.target.value === "yes")}
        value={form.values.hasExtraOption === true ? "yes" : "no"}
        className="mb-4"
      />
      {step === "values" && (
        <ImageVariantsExtraOption {...{ images, form, product }} setFieldValue={form.setFieldValue} />
      )}

      {step === "images" && (
        <>
          <div className="grid items-start w-full grid-cols-[repeat(auto-fit,112px)] sm:grid-cols-[repeat(auto-fit,150px)] gap-2.5">
            <input
              type="file"
              ref={imagePicker}
              name="product-images"
              multiple
              accept="image/*,.heic"
              id="product-images"
              className="hidden"
              onChange={(e) =>
                handleImageSelectionFromFile({ e, images: images, saveImages: (images: Image[]) => setImages(images) })
              }
            />

            {images.map(({ name, src, url, isUploading, uploadProgress, error }, index) => (
              <div key={index}>
                <figure className="w-full h-28 sm:h-[150px] relative group" key={index}>
                  {src || url ? (
                    <>
                      <LazyImage
                        src={src || url}
                        alt={name}
                        className="w-full h-full object-cover rounded-10"
                        loaderClasses="rounded-10"
                      />
                      {!hasExtraOption && (
                        <div className="bg-black bg-opacity-50 absolute bottom-0 w-full px-2 py-1.5 rounded-b-10">
                          {editPrice !== index && (
                            <div
                              className="text-xxs sm:text-1xs flex items-center cursor-pointer font-action text-white"
                              onClick={() => togglePriceEdit(index)}
                            >
                              {toCurrency(variants[index]?.price)}
                              {/* prettier-ignore */}
                              <svg viewBox="0 0 12 12" fill="none" className="ml-1.5 -mt-px sm:-mt-0.75 w-2.5 sm:w-3">
                                <path d="M8.5 1.41421C8.63132 1.28289 8.78722 1.17872 8.9588 1.10765C9.13038 1.03658 9.31428 1 9.5 1C9.68572 1 9.86962 1.03658 10.0412 1.10765C10.2128 1.17872 10.3687 1.28289 10.5 1.41421C10.6313 1.54554 10.7355 1.70144 10.8066 1.87302C10.8776 2.0446 10.9142 2.2285 10.9142 2.41421C10.9142 2.59993 10.8776 2.78383 10.8066 2.95541C10.7355 3.12699 10.6313 3.28289 10.5 3.41421L3.75 10.1642L1 10.9142L1.75 8.16421L8.5 1.41421Z" stroke="#fff" strokeLinecap="round" strokeLinejoin="round" />
                              </svg>
                            </div>
                          )}

                          {editPrice === index && (
                            <input
                              type="number"
                              inputMode="numeric"
                              autoFocus
                              onBlur={() => togglePriceEdit(index)}
                              value={variants[index].price}
                              onChange={(e) => updateVariantPrice(e.target.value)}
                              className="border-white border border-opacity-60 no-outline h-[22px] sm:h-6 w-full text-xxs sm:text-1xs font-action px-2 flex items-center bg-transparent rounded-5 text-white"
                            />
                          )}
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="h-full w-full rounded-10 bg-grey-loader animate-pulse flex items-center justify-center">
                      <div className="spinner spinner--sm"></div>
                    </div>
                  )}
                  <button
                    className="h-6 w-6 bg-black bg-opacity-80 flex items-center justify-center absolute transform -top-2 -right-2 rounded-full transition-all opacity-0 group-hover:opacity-100 z-30"
                    onClick={(e) => removeVariant(e, index)}
                    type="button"
                  >
                    {/* prettier-ignore */}
                    <svg width="12" height="12" viewBox="0 0 15 15" fill="none">
                      <path d="M11.25 3.75L3.75 11.25" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M3.75 3.75L11.25 11.25" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </button>
                </figure>
                {(isUploading || uploadProgress > 0) && (
                  <div
                    className={`mt-1.5 h-1 w-full rounded-10 overflow-hidden ${
                      error ? "bg-accent-red-500 bg-opacity-10" : "bg-grey-divider"
                    }`}
                  >
                    <div
                      className={`h-full transition-all duration-200 ease-out ${
                        error ? "bg-accent-red-500" : "bg-accent-green-500"
                      }`}
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                )}
              </div>
            ))}

            <button
              className="no-outline w-full h-30 sm:h-[150px] rounded-10 border border-dashed border-grey-subtext text-grey-subtext hover:border-primary-500 hover:text-primary-500 transition-all flex items-center justify-center"
              onClick={() => imagePicker.current.click()}
              type="button"
            >
              {/* prettier-ignore */}
              <svg width="20" viewBox="0 0 30 30" fill="none">
                <path d="M15 6.25V23.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M6.24976 15H23.7498" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </button>
          </div>
          <AppBtn color="neutral" size="sm" className="mt-5" onClick={updateVariantsFromImages}>
            Update options from product images
            {/* prettier-ignore */}
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" className="ml-1">
              <g clipPath="url(#clip0)">
                <path d="M11.5001 2V5H8.50012" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M10.2451 7.50001C9.92005 8.41997 9.30484 9.20937 8.49214 9.74926C7.67944 10.2892 6.71329 10.5503 5.73927 10.4933C4.76525 10.4363 3.83614 10.0643 3.09194 9.43334C2.34774 8.80237 1.82877 7.94662 1.61324 6.99503C1.39772 6.04345 1.4973 5.0476 1.89699 4.15754C2.29669 3.26748 2.97483 2.53144 3.82924 2.06033C4.68365 1.58921 5.66803 1.40856 6.63404 1.54558C7.60006 1.6826 8.49537 2.12988 9.18506 2.82001L11.5001 5.00001" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
              </g>
              <defs>
                <clipPath id="clip0">
                  <rect width="12" height="12" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </AppBtn>
        </>
      )}

      <div className="pt-3 pb-5 border-t border-grey-divider absolute bottom-0 bg-white z-50 w-full left-0 py-3 sm:py-3.75 px-5 sm:px-6.25 sm:rounded-b-15 flex items-center space-x-3">
        {!hasExtraOption && (
          <AppBtn isBlock onClick={() => handleSubmit()} disabled={imagesUploading} size="lg">
            Save Options
          </AppBtn>
        )}

        {hasExtraOption && (
          <>
            {step === "values" && (
              <div className="flex-1 w-full">
                <AppBtn color="neutral" isBlock onClick={() => setStep("images")}>
                  Back
                </AppBtn>
              </div>
            )}
            <div className="flex-1 w-full">
              <AppBtn isBlock onClick={() => form.handleSubmit()} disabled={imagesUploading} size="lg">
                {step === "images" ? "Next" : "Save Options"}
              </AppBtn>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ImageVariants;
