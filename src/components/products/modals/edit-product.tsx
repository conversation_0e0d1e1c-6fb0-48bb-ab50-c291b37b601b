import SelectVideo from "@/components/products/create-products/select-video";
import VideoUploadCard from "@/components/products/create-products/video-upload-card";
import ProcessVideoModal from "@/components/products/modals/process-video";
import VideoResourcesLoader from "@/components/products/modals/video-resources-loader";
import DataAccordion from "@/components/ui/data-accordion";
import { Product } from "@/pages/products/create";

import useEditProductForm from "@/components/hooks/useEditProductForm";
import ChangeVideoThumbnail from "@/components/products/modals/process-video/change-video-thumbnail";
import { InfoCircle } from "iconsax-react";
import { InfoBlockInterface, PricingTierInterface, ProductItemInterface } from "../../../assets/interfaces";
import { getFieldvalues, getProductsCurrency, toCurrency } from "../../../assets/js/utils/functions";
import { getProductThumbnail } from "../../../assets/js/utils/utils";
import { AppBtn, RoundActionBtn } from "../../ui/buttons";
import ErrorLabel from "../../ui/error-label";
import { DatePickerInput, InputField, MultiSelect, TextArea } from "../../ui/form-elements";
import Modal, { ModalBody, ModalFooter } from "../../ui/modal";
import SuccessLabel from "../../ui/success-label";
import MangageProductQuantity from "../create-products/manage-product-quantity";
import VariantsExplainerModal from "../create-products/variants-explainer";
import ManageBlocksInProducts from "../info-blocks/manage-blocks-in-products";
import PickProductMedia from "../pick-product-media";
import { ManageVariants } from "./";
import DeleteOptionsConfirmation from "./delete-options";
import ProductCategoriesModal from "./product-categories";
import { RequestInterface } from "@/api/utils";
import ManageTieredPricing, { PricingTierModals } from "../create-products/manage-tiered-pricing";
import { emit } from "@/components/hooks/useListener";
import SelectPricingTier from "../pricing-tiers/modals/select-pricing-tier";

export interface EditProductProps {
  show: boolean;
  toggle: (state: boolean) => void;
  item: ProductItemInterface;
  categories: any[];
  updateItem?: (item: ProductItemInterface) => void;
  updateItemList?: (item: ProductItemInterface, isDuplicate?: boolean) => void;
  isDuplication?: boolean;
  templates?: Product["variants"][];
  infoBlocks: InfoBlockInterface[];
  blocksLoading: boolean;
  blocksError: any;
  setInfoBlocks?: (blocks: InfoBlockInterface[]) => void;
  pricingTiers: PricingTierInterface[];
  setPricingTiers: (tiers: PricingTierInterface[]) => void;
  storeItems: ProductItemInterface[];
  fetchItemsReq: RequestInterface<any>;
  getItem: (id: string) => void;
}

const EditProductModal: React.FC<EditProductProps> = (props) => {
  const { pricingTiers, setPricingTiers, storeItems, fetchItemsReq, getItem } = props;
  const {
    canTranscode,
    canUploadVideos,
    changeThumbnail,
    currentVideoIndex,
    deleteOptions,
    errMessage,
    form,
    handleOpenVideoModal,
    loadProgress,
    modals,
    product,
    productImageUploading,
    removePickedMedia,
    removeProductVideo,
    saveImages,
    saveMedias,
    setVideoTrim,
    store,
    toggleModal,
    videoTrim,
    blocksError,
    blocksLoading,
    infoBlocks,
    setInfoBlocks,
    show,
    templates,
    transcodeVideo,
    updateVariants,
    allowVariants,
    isLoading,
    toggle,
    bodyRef,
    createReq,
    error,
    formContent,
    handleChangeThumbnail,
    response,
    retryVideoTask,
    videoProgresses,
    categories,
    modalsType,
  } = useEditProductForm(props);

  return (
    <>
      <Modal {...{ show, toggle }} title={`${formContent.title} Product - ${form.values.name}`} size="midi">
        <form className="flex flex-col flex-auto overflow-hidden" onSubmit={form.handleSubmit}>
          <ModalBody setRef={(ref) => (bodyRef.current = ref.current)}>
            <ErrorLabel error={errMessage ?? error?.message ?? createReq.error?.message} />
            <SuccessLabel message={response ?? createReq.response ? `Item successfully ${formContent.success}!` : ""} />
            <PickProductMedia
              {...{
                product: form.values,
                changeThumbnail,
                removePickedMedia,
                saveMedias: saveMedias,
                thumbnail: product.thumbnail,
                saveImages: saveImages,
                canProcessVideos: canTranscode,
              }}
            />
            {form.values?.videos?.length > 0 && (
              <DataAccordion
                isClosed={false}
                className="border-b border-grey-divider rounded-none"
                title={
                  <span className="text-base font-bold font-body tracking-normal">
                    <span className="font-display">Product Videos</span>
                  </span>
                }
              >
                <div className="space-y-2.5 pt-2.5">
                  {form.values.videos.map((v, i) => (
                    <VideoUploadCard
                      key={i}
                      retryTask={() => retryVideoTask(v.meta.id)}
                      videoProgress={videoProgresses?.value?.[v.meta.id]}
                      openVideoModal={(m: typeof modalsType) => handleOpenVideoModal(m, i)}
                      removeVideo={() => removeProductVideo(i, v.meta.id)}
                      changeThumbnail={() => handleChangeThumbnail(i)}
                      video={v}
                      error={
                        form.touched.videos && (form.errors?.videos?.[i] ? "Please select a thumbnail" : undefined)
                      }
                    />
                  ))}
                  <SelectVideo
                    product={form.values}
                    saveMedias={saveMedias}
                    canUploadVideos={canUploadVideos}
                    canProcessVideos={canTranscode}
                  />
                </div>
              </DataAccordion>
            )}
            {form.values?.videos?.length < 1 && (
              <div className="mt-5 border-t border-grey-divider pt-3.5">
                <SelectVideo
                  product={form.values}
                  saveMedias={saveMedias}
                  canUploadVideos={canUploadVideos}
                  canProcessVideos={canTranscode}
                />
              </div>
            )}
            <InputField label="Product Name" {...getFieldvalues("name", form)} />
            {/* <InputField
              label={`Product Price in ${getProductsCurrency()}`}
              min={1}
              {...getFieldvalues("price", form)}
            /> */}
            <MultiSelect
              label="Product Categories"
              options={[...categories].map((c) => ({ text: `${c.emoji} ${c.name}`, value: c.id }))}
              emptyLabel="No categories to show"
              hasSearch
              searchLabel="Search for categories"
              action={{ label: "Create new categories +", onClick: () => toggleModal("categories") }}
              {...getFieldvalues("tags", form)}
            />
            <TextArea label="Product Description" {...getFieldvalues("description", form)} rows={2} />
            {/* {allowVariants && ( */}

            <div className="mt-5 border-t border-grey-divider pt-3.5" onClick={(e) => e.preventDefault()}>
              <DataAccordion
                isClosed={false}
                title={
                  <span className="text-base font-bold font-body tracking-normal">
                    <span className="font-display">Product Pricing</span>
                  </span>
                }
              >
                <div className="mt-3.5 -mb-3.5">
                  <InputField
                    label={`Product Price ${getProductsCurrency()} (No commas)`}
                    type="number"
                    min={1}
                    {...getFieldvalues("price", form)}
                    inputMode="numeric"
                    step="any"
                  />
                  <InputField
                    label={`Discount Price ${getProductsCurrency()} (Optional)`}
                    type="number"
                    {...getFieldvalues("discount_price", form)}
                    inputMode="numeric"
                  />
                  {form.values.discount_price && form.values.variants?.options.length > 0 && (
                    <div className="bg-grey-fields-100 p-2 rounded-lg text-placeholder font-medium text-xs mt-1.5 flex items-center">
                      <figure className="h-8 w-8 rounded-full bg-white flex items-center justify-center text-accent-yellow-500 flex-shrink-0 mr-1.5">
                        <InfoCircle variant="Bold" size={18} />
                      </figure>
                      <span>
                        When you set a discount price, No need to change option prices — discounts apply automatically.
                      </span>
                    </div>
                  )}
                  <ManageTieredPricing
                    form={form}
                    toggleModal={toggleModal}
                    editPricingTier={() => toggleModal("view_pricing_tier")}
                    deletePricingTier={() => form.setFieldValue("tiered_pricing", null)}
                    viewPricingTier={() => {
                      emit("item-price-change", form?.values?.discount_price || form?.values?.price);
                      toggleModal("view_pricing_tier");
                    }}
                  />
                </div>
              </DataAccordion>
            </div>

            <div className="mt-5 border-t border-grey-divider pt-3.5" onClick={(e) => e.preventDefault()}>
              <DataAccordion
                isClosed={false}
                title={
                  <span className="text-base font-bold font-body tracking-normal">
                    <span className="font-display">Product Options</span>
                  </span>
                }
              >
                <div className="mt-3 flex items-center justify-between">
                  <div className="flex items-center">
                    <AppBtn color="neutral" size="sm" onClick={() => toggleModal("variants")} disabled={!allowVariants}>
                      {form.values.variants?.options.length > 0 ? `Update Options` : "Add Options"}
                    </AppBtn>
                    <span className="text-dark text-1xs inline-block ml-1.5 font-medium">
                      {form.values.variants?.options.length} Options added.
                    </span>
                  </div>
                  {form.values.variants?.options.length > 0 && (
                    <RoundActionBtn
                      icon="delete"
                      className="text-accent-red-500"
                      onClick={() => toggleModal("delete_options")}
                    />
                  )}
                </div>
                <div className="mt-3.5 border-t border-grey-divider">
                  <div className="flex items-center justify-between bg-grey-fields-100 rounded-10 py-2 px-2.5 mt-3.5">
                    <div className="flex items-center">
                      <figure className="h-6 w-6 bg-accent-yellow-500 flex items-center justify-center text-white rounded-full">
                        {/* prettier-ignore */}
                        <svg width="55%" viewBox="0 0 24 24" fill="none">
                      <path d="M18.0204 12.33L16.8004 11.11C16.5104 10.86 16.3404 10.49 16.3304 10.08C16.3104 9.63 16.4904 9.18 16.8204 8.85L18.0204 7.65C19.0604 6.61 19.4504 5.61 19.1204 4.82C18.8004 4.04 17.8104 3.61 16.3504 3.61H5.90039V2.75C5.90039 2.34 5.56039 2 5.15039 2C4.74039 2 4.40039 2.34 4.40039 2.75V21.25C4.40039 21.66 4.74039 22 5.15039 22C5.56039 22 5.90039 21.66 5.90039 21.25V16.37H16.3504C17.7904 16.37 18.7604 15.93 19.0904 15.14C19.4204 14.35 19.0404 13.36 18.0204 12.33Z" fill="currentColor"/>
                    </svg>
                      </figure>

                      <span className="text-dark text-sm font-medium inline-block ml-2">What are product options?</span>
                    </div>
                    <button
                      className="flex items-center text-primary-500 font-medium text-1xs"
                      onClick={() => toggleModal("v_explainer")}
                      type="button"
                    >
                      Learn More
                      {/* prettier-ignore */}
                      <svg className="w-[9px] ml-1" viewBox="0 0 10 10" fill="none">
                        <path d="M1.24264 0.24265V1.73818L7.16643 1.74348L0.71231 8.1976L1.77297 9.25826L8.22709 2.80414L8.23239 8.72793H9.72792V0.24265H1.24264Z" fill="#332089"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </DataAccordion>
            </div>

            <div className="mt-5 border-t border-grey-divider pt-3.75" onClick={(e) => e.preventDefault()}>
              <MangageProductQuantity store={store} form={form} />
            </div>
            <ManageBlocksInProducts
              form={form}
              infoBlocks={infoBlocks}
              blocksLoading={blocksLoading}
              setInfoBlocks={setInfoBlocks}
              blocksError={blocksError}
            />
            <div className="mt-5 border-t border-grey-divider pt-3.5" onClick={(e) => e.preventDefault()}>
              <DataAccordion
                isClosed={true}
                title={
                  <span className="text-base font-bold font-body tracking-normal">
                    <span className="font-display">Optional Product Settings</span>
                  </span>
                }
              >
                <div className="mt-3.5">
                  <InputField
                    label={`How much did you buy/make this product?`}
                    type="number"
                    {...getFieldvalues("cost_price", form)}
                    inputMode="numeric"
                  />
                  <InputField
                    type="number"
                    label="What's the min. quantity customers can buy?"
                    {...getFieldvalues("minimum_order_quantity", form)}
                    inputMode="numeric"
                  />
                  <DatePickerInput label="Expiry date (Food, Drugs, e.t.c)" {...getFieldvalues("expiry_date", form)} />
                </div>
              </DataAccordion>
            </div>
          </ModalBody>
          <ModalFooter>
            <AppBtn isBlock type="submit" disabled={isLoading || productImageUploading} size="lg">
              {productImageUploading && "Uploading images..."}
              {!productImageUploading && (isLoading ? formContent.buttonIsLoading : formContent.button)}
            </AppBtn>
          </ModalFooter>
        </form>
      </Modal>

      <ProductCategoriesModal show={modals.categories.show} toggle={() => toggleModal("categories")} />
      <ManageVariants
        show={modals.variants.show}
        toggle={() => toggleModal("variants")}
        title=""
        product={form.values}
        saveVariants={updateVariants}
        templates={templates}
      />
      <VariantsExplainerModal title="" show={modals.v_explainer.show} toggle={() => toggleModal("v_explainer")} />

      {form.values.videos?.length > 0 && (
        <>
          <ProcessVideoModal
            currentVideo={form.values.videos?.[currentVideoIndex]}
            onTrim={(start, end) => {
              setVideoTrim({ start, end });
            }}
            show={modals.process_video.show}
            toggle={() => toggleModal("process_video")}
            setVideo={(video) => {
              const videosCopy = [...form.values.videos];
              videosCopy[currentVideoIndex] = video;
              form.setFieldValue("videos", videosCopy);
            }}
            callback={() => {
              if (!getProductThumbnail(form.values)) {
                form.setFieldValue("thumbnail_type", "video");
              }
            }}
            transcodeVideo={(dims) => {
              const video = form.values.videos[currentVideoIndex];
              transcodeVideo({
                dimensions: dims,
                trim: videoTrim,
                taskId: video.meta.id,
                file: video.file as any,
                meta: {
                  videoIndex: currentVideoIndex,
                  productIndex: 0,
                },
              });
            }}
            value={videoTrim}
          />
        </>
      )}

      {!isNaN(currentVideoIndex) && (
        <ChangeVideoThumbnail
          currentVideo={form.values.videos?.[currentVideoIndex]}
          setVideo={(v) => {
            const videosCopy = [...form.values.videos];
            videosCopy[currentVideoIndex] = v;
            form.setFieldValue("videos", videosCopy);
          }}
          show={modals.change_thumbnail.show}
          toggle={(s) => toggleModal("change_thumbnail", s)}
        />
      )}

      <VideoResourcesLoader
        toggle={() => toggleModal("loader")}
        show={modals.loader.show}
        loadProgress={loadProgress}
      />
      <DeleteOptionsConfirmation
        show={modals.delete_options.show}
        toggle={() => toggleModal("delete_options")}
        deleteOptions={deleteOptions}
      />
      <SelectPricingTier
        show={modals.select_pricing_tier.show}
        toggle={() => toggleModal("select_pricing_tier")}
        allTiers={pricingTiers}
        productTier={form.values.tiered_pricing}
        setProductTier={(tier) => {
          form.setFieldValue("tiered_pricing", tier);
        }}
        openCreatePricingTierModal={() => toggleModal("create_pricing_tier")}
      />
      <PricingTierModals
        currentPricingTier={form.values.tiered_pricing}
        modals={modals}
        toggleModal={toggleModal}
        toCurrency={toCurrency}
        storeItems={storeItems}
        fetchItemsReq={fetchItemsReq}
        getItem={getItem}
        formRefs={[{ formik: form }]}
        currentProduct={0}
        setCurrentPricingTier={(tier) => {}}
        pricingTiersData={{ tiers: pricingTiers, setTiers: setPricingTiers }}
      />
    </>
  );
};

export default EditProductModal;
