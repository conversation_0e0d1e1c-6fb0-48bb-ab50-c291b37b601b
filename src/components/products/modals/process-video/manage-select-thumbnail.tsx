import { Video } from "@/assets/interfaces";
import SelectThumbnailMain from "../../select-thumbnail";

interface Props {
  currentVideo: Video;
  finalizeVideo: (img: Blob) => void;
  setDimensions: (dimensions: VideoDimensions) => void;
}

export interface VideoDimensions {
  width: number;
  height: number;
}

const SelectVideoThumbnail: React.FC<Props> = ({ currentVideo, finalizeVideo, setDimensions }) => {
  return (
    <SelectThumbnailMain
      onSelectThumbnail={finalizeVideo}
      {...{ videoFile: currentVideo.file, videoUrl: currentVideo.url, setDimensions }}
    />
  );
};

export default SelectVideoThumbnail;
