import { Video } from "@/assets/interfaces";
import useSteps from "@/components/hooks/useSteps";
import { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "@/components/ui/modal";
import Modal from "@/components/ui/modal";
import React, { useState, useRef, useEffect } from "react";
import TrimVideoManager, { VideoDimensions } from "./trim-manager";
import { AppBtn } from "@/components/ui/buttons";
import { uploadImageBlob } from "@/components/hooks/useImageUploads";
import { getProductThumbnail } from "@/assets/js/utils/utils";
import { Product } from "@/pages/products/create";
import { FormikProps } from "formik";
import SelectVideoThumbnail, { SelectThumbnailRef } from "../../select-thumbnail";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  currentVideo: Video;
  onTrim: (start: number, end: number) => void;
  value?: { start: number; end: number };
  transcodeVideo: (dimensions: VideoDimensions) => void;
  setVideo: (videos: Video) => void;
  callback?: () => void;
  onCancel?: VoidFunction;
}

const ProcessVideoModal: React.FC<Props> = ({
  show,
  toggle,
  currentVideo,
  onTrim,
  value,
  transcodeVideo,
  setVideo,
  callback,
  onCancel,
}) => {
  const { steps, changeStep, stepIndex, step, previous, next } = useSteps(["trim", "select-thumbnail"], 0);
  const thumbnailRef = useRef<SelectThumbnailRef>(null);
  const [dimensions, setDimensions] = useState<VideoDimensions>({ width: 0, height: 0 });

  const finalizeVideo = (img: Blob) => {
    toggle(false);

    transcodeVideo(dimensions);
    changeStep("trim");
    // return
    uploadImageBlob(img, (i) => {
      setVideo({ ...currentVideo, thumbnail: i.url, meta: { ...currentVideo.meta, thumbnail: i } });
      callback?.();
    });
  };

  const _toggle = () => {
    onCancel?.();
    toggle(false);
  };

  return (
    <Modal {...{ show, toggle: _toggle }} title="Upload Video" size="md" bgClose={false}>
      <ModalBody noPadding>
        <div className="w-full flex items-center justify-center space-x-1">
          {steps.map((step, index) => (
            <div className="flex-1 h-1.5 bg-grey-fields-100" key={index}>
              {stepIndex >= index && <div className="h-full bg-primary-500 w-full" key={index}></div>}
            </div>
          ))}
        </div>
        <div className="p-5 sm:p-6.25">
          <div className="mb-3.75">
            <h2 className="text-lg sm:text-xl font-bold">
              Step {stepIndex + 1}: {stepMetadata[steps[stepIndex]].title}
            </h2>
            <p className="text-sm text-dark">{stepMetadata[steps[stepIndex]].description}</p>
          </div>

          {step === "trim" && <TrimVideoManager onTrim={onTrim} value={value} currentVideo={currentVideo} />}
          {step === "select-thumbnail" && (
            <SelectVideoThumbnail
              videoFile={currentVideo.file}
              onSelectThumbnail={finalizeVideo}
              setDimensions={setDimensions}
              ref={thumbnailRef}
            />
          )}
        </div>
      </ModalBody>
      <ModalFooter>
        <div className="flex flex-col w-full">
          {stepIndex === 1 && (
            <span className="text-1xs text-placeholder mb-1.5">
              Once you click "Save & Upload", you won't be able to edit this video again
            </span>
          )}
          <div className="flex items-center justify-between w-full space-x-2.5">
            {stepIndex > 0 && (
              <AppBtn className="flex-1" onClick={() => previous()} isBlock color="neutral" size="lg">
                Back
              </AppBtn>
            )}
            <AppBtn
              className="flex-1"
              onClick={() => {
                if (step === "trim") {
                  next();
                } else {
                  thumbnailRef.current?.handleSelectThumbnail();
                }
              }}
              isBlock
              color="primary"
              size="lg"
            >
              {stepMetadata[steps[stepIndex]].buttonText}
            </AppBtn>
          </div>
        </div>
      </ModalFooter>
    </Modal>
  );
};

const stepMetadata = {
  trim: {
    title: "Trim Video",
    description: "Trim video to the desired length (Max 45s allowed)",
    buttonText: "Continue",
  },
  "select-thumbnail": {
    title: "Select Thumbnail",
    description: "Select the thumbnail for the video",
    buttonText: "Save & Upload",
  },
};

export default ProcessVideoModal;
