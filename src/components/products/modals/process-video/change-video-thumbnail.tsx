import { Video } from "@/assets/interfaces";
import { uploadImageBlob } from "@/components/hooks/useImageUploads";
import SelectVideoThumbnail from "@/components/products/modals/process-video/manage-select-thumbnail";
import { VideoDimensions } from "@/components/products/modals/process-video/select-video-thumbnail";
import SelectThumbnailMain, { SelectThumbnailRef } from "@/components/products/select-thumbnail";
import { AppBtn } from "@/components/ui/buttons";

import Modal, { ModalBody, ModalFooter, ModalProps } from "@/components/ui/modal";
import { toast } from "@/components/ui/toast";
import { useEffect, useRef, useState } from "react";

interface Props extends ModalProps {
  currentVideo: Video;
  setVideo: (video: Video) => void;
}
const ChangeVideoThumbnail: React.FC<Props> = ({ show, toggle, currentVideo, setVideo: updateVideo }) => {
  const changeThumbnailRef = useRef<SelectThumbnailRef>(null);
  const [_, setDimensions] = useState<VideoDimensions>({ width: 0, height: 0 });

  const handleSelectThumbnail = (blob: Blob) => {
    toast.promise(
      () => {
        return new Promise((resolve, reject) => {
          uploadImageBlob(blob, (i) => {
            updateVideo({ ...currentVideo, thumbnail: i.url, meta: { ...currentVideo.meta, thumbnail: i } });
            resolve(true);
            toggle(false);
          });
        });
      },
      {
        loading: {
          title: "Uploading thumbnail",
          message: "Please wait...",
        },
        success: {
          title: "Successful",
          message: "Thumbnail uploaded successfully",
        },
        error: {
          title: "Failed",
          message: "We couldn't upload your thumbnail!",
          actionText: "Retry",
          actionFunc: () => handleSelectThumbnail(blob),
        },
      }
    );
  };

  if (!currentVideo) return null;

  return (
    <>
      <Modal {...{ show, toggle }} title="Pick Video Thumbnail" size="md" bgClose={false}>
        <ModalBody noPadding>
          <div className="p-5 sm:p-6.25">
            <SelectThumbnailMain
              ref={changeThumbnailRef}
              onSelectThumbnail={(b) => handleSelectThumbnail(b)}
              {...{ videoFile: currentVideo.file, videoUrl: currentVideo.url, setDimensions }}
            />
          </div>
        </ModalBody>
        <ModalFooter>
          <AppBtn onClick={() => changeThumbnailRef.current?.handleSelectThumbnail()} isBlock>
            Select Thumbnail
          </AppBtn>
        </ModalFooter>
      </Modal>
    </>
  );
};
export default ChangeVideoThumbnail;
