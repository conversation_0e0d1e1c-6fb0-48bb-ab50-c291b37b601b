import App from "next/app";
import React, { useEffect, useState } from "react";
import { ProductItemInterface } from "../../assets/interfaces";
import authContext from "../../contexts/auth-context";
import { AppBtn } from "../ui/buttons";
import Table, { TableHead, TableHeadItem, TableBody } from "../ui/table";
import ProductItem from "./product-item";
import TableContent from "./product-table-content";
import ProductItemMobile from "./item-card-mobile";

const ProductTableContent = TableContent(ProductItem);
const ProductTableContentMobile = TableContent(ProductItemMobile);

interface Props {
  itemsData: ProductItemInterface[];
}

const DashboardItemsList: React.FC<Props> = ({ itemsData }) => {
  const [items, setItems] = useState(itemsData);
  const { categories } = authContext.useContainer();

  useEffect(() => {
    setItems(itemsData);
  }, [itemsData]);

  const updateItem = (item: ProductItemInterface, index: number) => {
    const itemsCopy = [...items];
    itemsCopy[index] = item;

    setItems(itemsCopy);
  };

  const addItem = (item: ProductItemInterface) => {
    setItems([...items, item]);
    return;
  };

  const deleteItem = (index: number) => {
    const itemsCopy = [...items];
    itemsCopy.splice(index, 1);

    setItems(itemsCopy);
  };

  if (!items?.length) {
    return (
      <div>
        <span className={`text-1sm xl:text-xl text-black font-bold mb-5 font-display`}>Top Products</span>
        <div className="w-max mx-auto my-3.75 lg:my-6.25">
          <div className="flex items-center flex-col grow-0 mb-5">
            <div className="w-20 h-20 xl:w-25 xl:h-25 bg-grey-fields-100 rounded-full"></div>
            <span className="text-black text-base xl:text-2xl font-bold mt-2.5 mb-1.25">No product to show</span>
            <span className="text-xs xl:text-sm text-dark">
              Start showcasing your products to customers, add your first product
            </span>
          </div>
          <AppBtn className="mx-auto">+ Add Product</AppBtn>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <h4 className={`text-base sm:text-lg xl:text-xl text-black font-bold`}>Top Products</h4>
        <AppBtn color="neutral" size="sm" className="hidden sm:flex" href="/products">
          See all products
        </AppBtn>
      </div>
      <div className="mt-5">
        <Table className="hidden sm:table">
          <TableHead>
            <TableHeadItem>ITEMS</TableHeadItem>
            <TableHeadItem>Total Orders</TableHeadItem>
            <TableHeadItem className=" hidden sm:table-cell">PRICE</TableHeadItem>
            <TableHeadItem className=" hidden sm:table-cell">OPTIONS</TableHeadItem>
            <TableHeadItem className=" hidden sm:table-cell">ACTIONS</TableHeadItem>
            <TableHeadItem>AVAILABILITY</TableHeadItem>
          </TableHead>
          <TableBody>
            {items.map((product, index) => (
              <ProductTableContent
                product={product}
                key={product.id}
                categories={categories}
                updateItem={(item) => updateItem(item, index)}
                deleteItem={() => deleteItem(index)}
                updateItemList={addItem}
                isStats
              />
            ))}
          </TableBody>
        </Table>
        <ul className="block sm:hidden">
          {items.map((product, index) => {
            return (
              <ProductTableContentMobile
                product={product}
                key={product.id}
                updateItemList={addItem}
                categories={categories}
                updateItem={(item) => updateItem(item, index)}
                deleteItem={() => deleteItem(index)}
                isStats
              />
            );
          })}
          <AppBtn color="neutral" isBlock size="sm" href="/products">
            See all products
          </AppBtn>
        </ul>
      </div>
    </div>
  );
};

export default DashboardItemsList;
