import React, { useState } from "react";
import { ProductItemInterface, VariantForm, VariantItem } from "../../../assets/interfaces";
import { genarateStringFromVariantValues, removeUnderscores, toCurrency } from "../../../assets/js/utils/functions";
import LazyImage from "../../lazy-image";
import Badge from "../../ui/badge";
import { StepNumberInput } from "../../ui/form-elements/step-number-input";
import Toggle from "../../ui/toggle";

interface VariantItemsProps {
  variants: VariantForm;
  product: ProductItemInterface;
  updateOptions: (options: VariantItem[]) => void;
}

const VariantItems: React.FC<VariantItemsProps> = ({ variants, product, updateOptions }) => {
  const options = variants?.options;
  const isImageVariant = variants?.type === "images";
  const extraOption = options[0]?.values ? Object.keys(options[0]?.values)[0] : null;

  const computeAndSaveOptions = (option: VariantItem, index: number) => {
    const optionsCopy = [...options];
    optionsCopy[index] = option;

    updateOptions(optionsCopy);
  };

  return (
    <ul className="grid grid-cols-[repeat(auto-fit,minmax(250px,1fr))] gap-4 py-4 pb-16">
      {options &&
        options.map((o, index) => (
          <li
            className="flex flex-col bg-grey-fields-200 border border-grey-border border-opacity-50 rounded-15 p-3.5 divide-y divide-grey-border divide-opacity-50 w-full"
            key={index}
          >
            {!isImageVariant && (
              <div className="flex flex-col">
                <span className="text-dark text-sm font-medium">{genarateStringFromVariantValues(o?.values)}</span>
                <OptionQuantity option={o} updateOption={(option) => computeAndSaveOptions(option, index)} />
              </div>
            )}

            {isImageVariant && (
              <div className="flex items-center">
                <figure className="h-10 w-10 rounded-5 overflow-hidden bg-grey-ash relative">
                  {o.image && <LazyImage src={o.image} alt="" className="w-full h-full object-cover" />}
                </figure>
                <div className="ml-2.5 flex flex-col">
                  <span className="text-dark text-sm inline-block mb-0.5 capitalize font-medium">
                    {(o?.values && extraOption) ? `${removeUnderscores(extraOption)} ${o.values[extraOption]}` : product?.name}
                  </span>
                  <OptionQuantity option={o} updateOption={(option) => computeAndSaveOptions(option, index)} />
                </div>
              </div>
            )}
            <div className="flex items-center justify-between mt-3 pt-3">
              <Toggle
                intialState={o.is_available}
                onChange={(state) => computeAndSaveOptions({ ...o, is_available: state }, index)}
              />

              {product?.is_always_available || product?.quantity < 0 ? (
                <Badge
                  text={product?.is_always_available ? "INFINITE" : "NO QUANTITY"}
                  color={product?.is_always_available ? "green" : "dark"}
                  greyBg={false}
                />
              ) : (
                <StepNumberInput
                  min={0}
                  value={o.quantity ?? 0}
                  onChange={(quantity) => computeAndSaveOptions({ ...o, quantity }, index)}
                />
              )}
            </div>
          </li>
        ))}
    </ul>
  );
};

interface IProps {
  option: VariantItem;
  updateOption: (o: VariantItem) => void;
}

const OptionQuantity: React.FC<IProps> = ({ option, updateOption }) => {
  const [editing, setEditing] = useState(false);

  const togglePriceEdit = () => {
    setEditing(!editing);
  };

  const getInputPrice = (price: any) => {
    price = Number(price);
    return price === 0 ? null : price;
  };

  return (
    <>
      {!editing && (
        <button className="flex items-center no-outline" onClick={togglePriceEdit}>
          <span className={`font-semibold text-black-secondary text-1xs`}>{toCurrency(option.price)}</span>
          {/* prettier-ignore */}
          <svg width="14" viewBox="0 0 16 16" fill="none" className="ml-1 text-primary-500">
          <path d="M8.83958 2.4L3.36624 8.19334C3.15958 8.41334 2.95958 8.84667 2.91958 9.14667L2.67291 11.3067C2.58624 12.0867 3.14624 12.62 3.91958 12.4867L6.06624 12.12C6.36624 12.0667 6.78624 11.8467 6.99291 11.62L12.4662 5.82667C13.4129 4.82667 13.8396 3.68667 12.3662 2.29334C10.8996 0.913335 9.78624 1.4 8.83958 2.4Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M7.92676 3.36664C8.21342 5.20664 9.70676 6.6133 11.5601 6.79997" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M2 14.6667H14" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
        </button>
      )}
      {editing && (
        <input
          autoFocus
          type="number"
          inputMode="numeric"
          onBlur={togglePriceEdit}
          value={option.price}
          onChange={(e) => updateOption({ ...option, price: getInputPrice(e.target.value) })}
          className="border-grey-border border border-opacity-30 focus:border-opacity-60 no-outline h-6 w-20 text-1xs font-action px-2.5 flex items-center bg-transparent rounded-5 text-black-secondary font-medium bg-white"
        />
      )}
    </>
  );
};

export default VariantItems;
