import useComputeThumbnails from "@/components/hooks/useComputeThumbnails";
import useMediaProcessor from "@/components/hooks/useMediaProcessor";
import { useModals } from "@/components/hooks/useModals";
import useRefState from "@/components/hooks/useRefState";
import useVideoTranscode, {
  ffmpegContext,
  VideoProgresses,
  VideoStepStatus,
} from "@/components/hooks/useVideoTranscode";
import Portal from "@/components/portal";
import ProcessVideoModal from "@/components/products/modals/process-video";
import classNames from "classnames";
import { VideoCircle } from "iconsax-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { EditItemParams } from "../../../api/interfaces/items.interface";
import { RequestInterface } from "../../../api/utils";
import { FILE_TYPES, Media, MediaType, ProductItemInterface } from "../../../assets/interfaces";
import { convertItemToProduct, getProductThumbnail } from "../../../assets/js/utils/utils";
import authContext from "../../../contexts/auth-context";
import { Product } from "../../../pages/products/create";
import useImageUploads, { uploadImageBlob } from "../../hooks/useImageUploads";
import LazyImage from "../../lazy-image";
import { toast } from "../../ui/toast";

interface Props {
  item: ProductItemInterface;
  updateRequest: RequestInterface<EditItemParams>;
  updateProduct: (product: ProductItemInterface) => void;
}
const ProductMedias: React.FC<Props> = ({ item, updateRequest, updateProduct }) => {
  const { isLoading, makeRequest, error } = updateRequest;
  const { categories } = authContext.useContainer();
  const imagePicker = useRef<HTMLInputElement>(null);
  const [productRef, setProduct] = useRefState<Product>({
    ...convertItemToProduct(item),
  });
  const product = productRef.value;

  const { modals, toggleModal } = useModals(["process_video"]);

  /**
   *
   *
   *
   *
   */

  const [currentVideo, setCurrentVideo] = useState<number>();
  const [trim, setTrim] = useState({ start: 0, end: 15 });

  const { ffmpegRef, ffmpegLoading, canTranscode, loadProgress } = ffmpegContext.useContainer();

  const { removeVideoProgress, retryVideoTask, videoProgresses, transcodeVideo } = useVideoTranscode(
    (taskId, url, blob) => {
      if (url) {
        const product = productRef.value;
        const index = product.videos.findIndex((v) => v.meta.id === taskId);
        if (index >= 0) {
          const videosCopy = [...(product.videos ?? [])];
          const newFile = new File([blob], (videosCopy?.[index]?.file as File)?.name ?? "Video");

          videosCopy[index].url = url;
          videosCopy[index].file = newFile;

          updateItem(
            {
              videos: videosCopy.map((v) => ({
                name: (v.file as any).name ?? "video",
                url: v.url,
                thumbnail: v.meta?.thumbnail?.url ?? v.thumbnail,
              })),
            },
            () => {
              setProduct((p) => ({ ...p, videos: videosCopy }));
            }
          );
        }
      }
    },
    {
      ffmpegRef,
    }
  );

  const excludedMediaTypes = canTranscode ? [] : [MediaType.VIDEO];

  const productMedias: Media[] = useMemo(() => {
    const imgMedias = (product.images ?? [])?.map((i) => ({ ...i, type: MediaType.IMAGE }));
    const videoMedias = (product.videos ?? [])?.map((i) => ({ ...i, type: MediaType.VIDEO }));
    return [...imgMedias, ...videoMedias];
  }, [product]);

  const { processFiles } = useMediaProcessor(
    (m) => {
      const media = m[0];
      if (media) {
        if (media.type === MediaType.IMAGE) {
          const { file } = media;
          const imagesCopy = product.images;
          uploadImageBlob(
            file,
            (img) => {
              setProduct({ ...product, images: [...(imagesCopy ?? []), img] });
            },
            () => {
              updateItem({ images: product.images.map((i) => i.url) });
            }
          );
        }

        if (media.type === MediaType.VIDEO) {
          const { type, ...v } = media;
          setProduct({ ...product, videos: [...(product.videos ?? []), v] });
          setCurrentVideo(product?.videos?.length - 1);
          toggleModal("process_video");
        }
      }
    },
    productMedias,
    null,
    false
  );

  const { imageThumbnails, videoThumbnails } = useComputeThumbnails(product);

  const isThumbnail = (data: { type: string; index: number }) => {
    return product.thumbnail_type === data.type && product.thumbnail === data.index;
  };

  const getThumbnailProgress = (
    m: { isUploading: boolean; uploadProgress: number; meta: { type: string } },
    videoProgresses: VideoProgresses,
    taskId = ""
  ) => {
    if (m.meta.type === "image") {
      return m.isUploading ? m.uploadProgress : null;
    }

    let computedProgress = 0;
    const videoProgress = videoProgresses?.[taskId];

    if (!videoProgress && m.isUploading === false && m.uploadProgress < 100) {
      return null;
    }

    if (!videoProgress && m.isUploading && m.uploadProgress > 0) {
      return Math.floor(m.uploadProgress / 5);
    }

    if (videoProgress) {
      switch (videoProgress.step) {
        case VideoStepStatus.COMPRESSING:
          computedProgress = Math.ceil(((videoProgress.progress ?? 0) / 100) * 40);
          break;
        case VideoStepStatus.UPLOADING:
          computedProgress = Math.ceil(((videoProgress.progress ?? 0) / 100) * 40) + 40;
          break;
        case VideoStepStatus.SUCCESS:
          computedProgress = 80;
      }

      return Math.ceil((m.uploadProgress ?? 100) / 5 + computedProgress);
    }
  };

  /**
   *
   *
   *
   *
   */

  //track older image state for comparison
  // const [prevImages, setPrevImages] = useState(product?.images);

  // useImageUploads(product.images, FILE_TYPES.ITEMS, (images) => setProduct({ ...product, images }));

  // useEffect(() => {
  //   const product = productRef.value;
  //   const uploadedImages = product.images.filter((i) => i.file && i.url && i.uploadProgress === 100);

  //   if (uploadedImages?.length > 0) {
  //     updateItem(
  //       {
  //         images: product.images.filter((i) => i.url).map((i) => i.url),
  //       },
  //       () => {
  //         setProduct({
  //           ...product,
  //           images: product.images.filter((i) => i.url && i.file).map((i) => ({ ...i, file: null })),
  //         });
  //       }
  //     );
  //   }
  // }, [productRef]);

  useEffect(() => {
    setProduct(convertItemToProduct(item));
  }, [item]);

  const changeThumbnail = (media: Partial<Media>, index: number, type: string) => {
    if ((index === product.thumbnail && type === product.thumbnail_type) || !media.url || media.uploadProgress < 100)
      return;

    updateItem({ thumbnail: index, thumbnail_type: type }, () => {
      setProduct({ ...product, thumbnail: index, thumbnail_type: type });
    });
  };

  const removePickedMedia = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, index: number, type: string) => {
    e.stopPropagation();

    if (index === product.thumbnail && type === product.thumbnail_type) {
      toast.error({
        title: "Error!",
        message: `Cannot delete thumbnail image.`,
      });
      return;
    }

    const productCopy = { ...product };
    productCopy.thumbnail = product.thumbnail > index ? product.thumbnail - 1 : product.thumbnail;

    if (type === "image") {
      productCopy.images.splice(index, 1);
    } else if (type === "video") {
      productCopy.videos.splice(index, 1);
    }

    updateItem(
      {
        videos: productCopy.videos.map((v) => ({
          name: (v.file as any).name ?? "video",
          url: v.url,
          thumbnail: v.meta?.thumbnail?.url ?? v.thumbnail,
        })),

        images: productCopy.images.map((i) => i.url),
      },
      () => {
        // setPrevImages(productCopy.images);
        setProduct({ ...productCopy });
      }
    );
  };

  const updateItem = async (data: any, callBack?: VoidFunction, toastOpts = null) => {
    if (!toastOpts) {
      toastOpts = {
        loading: {
          title: "Please wait",
          message: "Updating Product...",
        },
        success: {
          title: "Successful",
          message: "Product images updated successfully",
        },
        error: {
          title: "Failed",
          message: "We couldn't update product images",
          actionText: "Retry",
          actionFunc: () => {},
        },
      };
    }

    const res = await toast.promise(async () => {
      const [res, error] = await makeRequest({ id: item.id, item: data });
      if (error) {
        return Promise.reject(error);
      } else {
        return Promise.resolve(res);
      }
    }, toastOpts);

    if (error) {
      return;
    }

    const updatedProduct = { ...res.data, category: categories.find((c) => res.data.category === c.id) };
    updateProduct(updatedProduct);

    callBack && callBack();
  };

  return (
    <div className="overflow-x-scroll overflow-y-visible w-full flex items-center py-3.75">
      {[...imageThumbnails, ...videoThumbnails].map((media, index) => (
        <div key={index} className={`mr-2.5 sm:mr-3.5 relative flex-shrink-0 cursor-pointer group`}>
          <figure
            className={classNames(
              "w-25 h-25 sm:w-30 sm:h-30 relative ease-out duration-300 transition-all  box-content"
              // {
              //   "border-accent-green-500 rounded-15 sm:rounded-20": isThumbnail(media.meta),
              //   "border-transparent rounded-15 sm:rounded-20": !isThumbnail(media.meta),
              // }
            )}
          >
            <div
              className={classNames(
                "rounded-full py-1 px-2 text-green-500 bg-white text-[10px] !leading-none text-center font-semibold absolute bottom-1.5 right-1.5 inline-block z-20",
                {
                  "border-accent-green-500": isThumbnail(media.meta),
                  hidden: !isThumbnail(media.meta),
                }
              )}
            >
              THUMBNAIL
            </div>
            <LazyImage
              src={media.src}
              className={classNames("h-full w-full object-cover relative z-10 rounded-15")}
              loaderClasses={classNames("rounded-15")}
            />

            {media.meta.type === "video" && (
              <VideoCircle
                size={32}
                variant="Bold"
                className="text-white z-50 absolute top-1/2 -translate-y-1/2 mx-auto left-0 right-0"
              />
            )}
          </figure>
          {getThumbnailProgress(media, videoProgresses.value, media.meta.id) && (
            <div
              className={`h-1 w-full rounded-10 overflow-hidden absolute -bottom-2  ${
                media.error ? "bg-accent-red-500 bg-opacity-10" : "bg-grey-divider"
              }`}
            >
              <div
                className={`h-full transition-all duration-200 ease-out ${
                  media.error ? "bg-accent-red-500" : "bg-accent-green-500"
                }`}
                style={{ width: `${getThumbnailProgress(media, videoProgresses.value, media.meta.id)}%` }}
              ></div>
            </div>
          )}
          {product.thumbnail !== index && (
            <button
              className="absolute -top-1 -right-1 h-6 w-6 rounded-full bg-primary-900 bg-opacity-80 text-white z-50 flex items-center justify-center pointer-events-none opacity-0 transition-all ease-out duration-200 group-hover:pointer-events-auto group-hover:opacity-100"
              onClick={(e) => removePickedMedia(e, media.meta.index, media.meta.type)}
            >
              {/* prettier-ignore */}
              <svg width="50%" viewBox="0 0 15 15" fill="none">
                <path d="M11.25 3.75L3.75 11.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3.75 3.75L11.25 11.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          )}

          {!isThumbnail(media.meta) && (
            <button
              className="h-4 w-4 rounded-full border-grey-fields-100 border-2 border-solid absolute bottom-2 right-2 z-20"
              onClick={() => changeThumbnail(media, media.meta.index, media.meta.type)}
            />
          )}
        </div>
      ))}

      <input
        type="file"
        ref={imagePicker}
        name="product-medias"
        accept={canTranscode ? "video/mp4,webm,ogg,video/quicktime,image/*,.heic" : "image/*,.heic"}
        id="product-media-picker"
        className="hidden"
        onChange={(e) => processFiles(e.target.files, excludedMediaTypes)}
      />

      <button
        className="h-25 w-25 sm:w-30 sm:h-30 text-grey-muted hover:text-dark transition-all ease-out duration-150 flex-shrink-0"
        onClick={() => imagePicker.current.click()}
      >
        {/* prettier-ignore */}
        <svg  viewBox="0 0 120 120" fill="none">
          <path d="M60.0001 51.2501V68.7501" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M51.2498 60H68.7498" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <rect x="0.75" y="0.75" width="118.5" height="118.5" rx="14.25" stroke="currentColor" strokeWidth="1.5" strokeDasharray="6 6"/>
        </svg>
      </button>
      <Portal>
        {currentVideo !== undefined && product.videos[currentVideo] && (
          <>
            <ProcessVideoModal
              currentVideo={product.videos?.[currentVideo]}
              onTrim={(start, end) => {
                setTrim({ start, end });
              }}
              onCancel={() => {
                imagePicker.current.value = null;
                const videosCopy = [...product.videos];
                videosCopy.splice(currentVideo, 1);
                setProduct({ ...product, videos: videosCopy });
                setCurrentVideo(undefined);
              }}
              show={modals.process_video.show}
              toggle={() => toggleModal("process_video")}
              setVideo={(video) => {
                const videosCopy = [...product.videos];
                videosCopy[currentVideo] = video;
                setProduct({ ...product, videos: videosCopy });
              }}
              callback={() => {
                if (!getProductThumbnail(product)) {
                  setProduct({ ...product, thumbnail_type: "video" });
                }
              }}
              value={trim}
              transcodeVideo={(dims) => {
                const video = product.videos[currentVideo];
                transcodeVideo({
                  dimensions: dims,
                  trim,
                  taskId: video.meta.id,
                  file: video.file as any,
                  meta: {
                    videoIndex: currentVideo,
                    productIndex: 0,
                  },
                });
              }}
            />
          </>
        )}
      </Portal>
    </div>
  );
};

export default ProductMedias;

/* 
    video progresses provide some sort of overall progress
    also the video thumbnail has a separate progress
    thumbnail takes 20%  compression 40% and upload 40%

*/
