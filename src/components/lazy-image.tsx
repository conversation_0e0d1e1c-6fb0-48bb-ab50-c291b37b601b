import { useEffect, useRef, useState } from "react";

interface LazyImageProps extends React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement> {
  src: string;
  showLoader?: boolean;
  loaderClasses?: string;
}

const LazyImage: React.FC<LazyImageProps> = ({ src, showLoader = true, loaderClasses, ...props }) => {
  const imageRef = useRef<HTMLImageElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);

  const [resourceBlobUrl, setResourceBlobUrl] = useState<string>();

  useEffect(() => {
    if (src?.startsWith?.("/") || src?.startsWith?.("blob:")) {
      setResourceBlobUrl(src);
      return;
    }
    const awsLink = convertAwsToCloudFront(src);
    if (awsLink) {
      setResourceBlobUrl(awsLink);
    } else {
      setResourceBlobUrl(src);
    }
  }, [src]);

  useEffect(() => {
    const imageObserver = new IntersectionObserver(
      (entries, observer) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.unobserve(imageRef.current);
          }
        });
      },
      { threshold: 0.1 }
    );

    imageObserver.observe(imageRef.current);

    return () => imageObserver.disconnect();
  }, []);

  function getImage() {
    if (isVisible) {
      return resourceBlobUrl;
    }
    return "";
  }

  return (
    <>
      {!hasLoaded && showLoader && (
        <div className={`bg-grey-loader h-full w-full absolute top-0 animate-pulse ${loaderClasses}`}></div>
      )}
      <img
        // crossOrigin="anonymous"
        src={getImage()}
        {...props}
        style={!hasLoaded ? { opacity: 0 } : { ...props.style }}
        ref={imageRef}
        onLoad={() => setHasLoaded(true)}
      />
    </>
  );
};

export function convertAwsToCloudFront(inputUrl: string): string | null {
  // return `${window.location.origin}/api/res?url=${encodeURIComponent(inputUrl)}`; //temporary for prod issue
  // if (!window.location.pathname.startsWith("/products")) {
  //   return inputUrl; // return the original url if not on the products page
  // }

  let url: URL;
  // console.log(inputUrl)
  try {
    url = new URL(inputUrl);
  } catch (err) {
    // Not a valid URL
    return inputUrl;
  }

  const host = url.host;
  let resourcePath = "";

  if (!host.startsWith("catlog-s3") && !host.startsWith("catlog-dev")) {
    return `${window.location.origin}/api/res?url=${encodeURIComponent(inputUrl)}`;
  }

  // Pattern: bucket.s3.amazonaws.com/key...
  const subdomainMatch = host.match(/^([^.]+)\.s3(?:[.-][^.]*)*\.amazonaws\.com$/);
  if (subdomainMatch) {
    // Path is everything after the slash
    resourcePath = url.pathname.replace(/^\//, "");
  }

  // Pattern: s3.amazonaws.com/bucket/key...
  const pathStyleMatch =
    host === "s3.amazonaws.com" || (host.endsWith(".amazonaws.com") && url.pathname.startsWith("/"));
  if (host === "s3.amazonaws.com") {
    // URL: s3.amazonaws.com/bucket/key...
    const parts = url.pathname.split("/").filter(Boolean);
    if (parts.length >= 2) {
      resourcePath = parts.slice(1).join("/");
    }
  }

  if (!resourcePath) {
    // Not an S3 URL we recognize
    return null;
  }

  let cloudFrontDomain = host.startsWith("catlog-dev")
    ? s3BucketToCloudFrontMap["catlog-dev"]
    : host.startsWith("catlog-s3")
    ? s3BucketToCloudFrontMap["catlog-s3"]
    : null;

  if (!cloudFrontDomain) {
    return inputUrl; //fallback to original url if all else fails
  }

  // Ensure CloudFront domain has no trailing slash
  const cfDomain = cloudFrontDomain.replace(/\/+$/, "");
  return `${cfDomain}/${resourcePath}`;
}

// d3tbq23ocs3ijh.cloudfront.net
// d2u1crcjwgedag.cloudfront.net

const s3BucketToCloudFrontMap = {
  "catlog-dev": "https://d3tbq23ocs3ijh.cloudfront.net",
  "catlog-s3": "https://d2u1crcjwgedag.cloudfront.net",
};

export default LazyImage;
