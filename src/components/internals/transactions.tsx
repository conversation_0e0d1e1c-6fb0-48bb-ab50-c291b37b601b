import React, { useEffect, useState } from "react";
import { GetkycParams } from "../../api/interfaces/internals";
import { CheckTransactions } from "../../api/internals";
import { useFetcher } from "../../api/utils";
import { toCurrency } from "../../assets/js/utils/functions";
import usePagination from "../hooks/usePagination";
import { AppBtn } from "../ui/buttons";
import ContentWithCopy from "../ui/content-with-copy";
import Table, { TableBody, TableCell, TableHead, TableHeadItem, TableRow } from "../ui/table";
import dayjs from "dayjs";
import Pagination from "../ui/pagination";

const TransactionsData = () => {
  const PER_PAGE = 50;
  const [wallets, setWallets] = useState<Wallet[]>([]);
  // const [sortBy, setSortBy] = useState("");
  const { currentPage, perPage, setPerPage, goNext, goPrevious, setPage } = usePagination();
  const { isLoading, error, response } = useFetcher<GetkycParams>(CheckTransactions, {
    pagination: {
      page: currentPage,
      per_page: perPage,
    },
  });
  // const stores: KYCInfo[] = response?.data ?? [];

  const today = dayjs();

  useEffect(() => {
    if (response?.data) {
      const walletIds = Object.keys(response?.data);
      const wallets = walletIds.map((k) => ({ ...response?.data[k], id: k }));

      setWallets(wallets);
    }
  }, [response]);

  if (isLoading) {
    return <p>Loading...</p>;
  }

  if (!wallets || wallets.length === 0 || error) {
    if (error) {
      return <p className="py-10 text-red-500 text-center">Something went wrong!</p>;
    }

    if (wallets.length === 0) {
      return (
        <div className="py-10 flex items-center flex-col text-center">
          <span className="text-gray-500 text-sm inline-block">No wallets to show</span>
          <AppBtn
            className="mt-2.5"
            onClick={() => {
              if (currentPage > 1) window.location.reload();
            }}
          >
            Go Back
          </AppBtn>
        </div>
      );
    }

    return <p className="py-15 text-center">Loading...</p>;
  }

  return (
    <div className="p-5 sm:p-8">
      <div>
        <Table className="">
          <TableHead>
            <TableHeadItem>Wallet Id</TableHeadItem>
            <TableHeadItem>Store Name</TableHeadItem>
            <TableHeadItem>Credits</TableHeadItem>
            <TableHeadItem>Debits</TableHeadItem>
            <TableHeadItem>Balance</TableHeadItem>
            <TableHeadItem>Actual Balance</TableHeadItem>
            <TableHeadItem>Deficit</TableHeadItem>
            {/* <TableHeadItem>Selfie</TableHeadItem>
            <TableHeadItem>Id Card</TableHeadItem> */}
          </TableHead>
          <TableBody>
            {wallets.map((wallet, index) => (
              <>
                {wallet ? (
                  <TableRow key={index}>
                    <TableCell className="">
                      <ContentWithCopy text={wallet?.id}>
                        <span className="text-dark">{wallet?.id}</span>
                      </ContentWithCopy>
                    </TableCell>
                    <TableCell>
                      <span className="text-dark">{wallet?.store}</span>
                    </TableCell>
                    <TableCell>{toCurrency(wallet?.credits / 100, wallet?.currency)}</TableCell>
                    <TableCell>{toCurrency(wallet?.debits / 100, wallet?.currency)}</TableCell>{" "}
                    <TableCell>{toCurrency(wallet?.balance / 100, wallet?.currency)}</TableCell>{" "}
                    <TableCell>{toCurrency(wallet?.actual_balance / 100, wallet?.currency)}</TableCell>
                    <TableCell
                      className={
                        Math.ceil(wallet?.actual_balance) !== Math.ceil(wallet?.balance)
                          ? "!bg-red-600 !text-white"
                          : ""
                      }
                    >
                      {toCurrency(
                        (Math.ceil(wallet?.balance) - Math.ceil(wallet?.actual_balance)) / 100,
                        wallet?.currency
                      )}
                    </TableCell>
                  </TableRow>
                ) : null}
              </>
            ))}
          </TableBody>
        </Table>

        {/* <div className="mt-5 flex mb-16">
          <span className="text-sm text-dark pt-1">
            Showing{" "}
            <b>
              {PER_PAGE * (currentPage - 1) + 1} - {PER_PAGE * (currentPage - 1) + response?.}
            </b>{" "}
            of <b>{response.total_kycs}</b> Wallets
          </span>
          <div className="ml-auto flex items-center space-x-2.5">
            {currentPage > 1 && (
              <AppBtn color="neutral" size="sm" onClick={goPrevious}>
                Previous
              </AppBtn>
            )}
            <AppBtn color="neutral" size="sm" onClick={goNext}>
              Next
            </AppBtn>
          </div> */}
        <Pagination
          data={response}
          {...{
            currentPage,
            setPage,
            goNext,
            length: wallets.length,
            label: "Wallets",
            goPrevious,
            per_page: perPage,
            setPerPage,
          }}
        />
      </div>
    </div>
  );
};

export default TransactionsData;

export interface Wallet {
  actual_balance: number;
  debits: number;
  credits: number;
  balance: number;
  currency: string;
  store: string;
  id: string;
}
