import React, { useMemo } from "react";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "../ui/modal";
import { AppBtn } from "../ui/buttons";
import { countryFlags } from "@/pages/sign-up";
import { COUNTRY_CURRENCY_MAP, COUNTRY_FULL_NAME_MAP } from "@/assets/js/utils/constants";
import { getDropdownOptionsFromCountries } from "@/assets/js/utils/utils";
import { CountryInterface } from "@/assets/interfaces";
import { SelectDropdown, SelectWithModal } from "../ui/form-elements";

interface Props {
  show: boolean;
  toggle: () => void;
  country: string;
  setCountry: (country: string) => void;
  proceed: () => void;
  supportedCountries: CountryInterface[];
}

const StoreCountryModal: React.FC<Props> = ({ show, toggle, country, setCountry, proceed, supportedCountries }) => {
  const countryOptions = useMemo(() => {
    return getDropdownOptionsFromCountries(supportedCountries);
  }, [supportedCountries]);

  return (
    <Modal show={show} toggle={toggle} size="sm" title="Confirm Country">
      <ModalBody>
        <div className="flex flex-col items-center">
          <figure className="h-15 w-15 rounded-full overflow-hidden border-4 border-primary-500 border-opacity-5 box-content">
            {countryFlags[country]}
          </figure>
          <h2 className="text-2lg text-black text-center mt-2.5 font-bold leading-tight mb-3.75">
            Is your business <br /> based in {COUNTRY_FULL_NAME_MAP[country]}?
          </h2>
          <SelectWithModal
            label="Change Country"
            hasSearch
            searchLabel="Search countries"
            options={countryOptions}
            value={country}
            onChange={(e) => setCountry(e.target.value)}
            className="w-full"
          />
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn onClick={proceed} size="lg" isBlock>
          Continue
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default StoreCountryModal;
