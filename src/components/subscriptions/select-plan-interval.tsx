import { useState } from "react";
import { toCurrency } from "../../assets/js/utils/functions";
import { CURRENCIES, Plan, PLAN_TYPE } from "../../assets/interfaces";
import { AppBtn } from "../ui/buttons";
import Radio from "../ui/form-elements/radio";
import Modal, { ModalBody, ModalFooter } from "../ui/modal";
import SuccessLabel from "../ui/success-label";
import { PlanOption } from "./subscriptions-controller";
import InfoCard from "../ui/info-card";
import { UPFRONT_SUBSCRIPTION_AMOUNTS } from "./select-plan-preference";

interface Props {
  subscribeToPlan: (plan: string, plan_option: string) => void;
  plans: PlanOption[];
  show: boolean;
  toggle: (state: boolean) => void;
  currentPlan: Plan;
  isLoading?: boolean;
  currency: string;
  isUpfront?: boolean;
  isFreeTrial?: boolean;
  planType: PLAN_TYPE;
}

const SelectPlanInterval: React.FC<Props> = ({
  show,
  toggle,
  plans,
  subscribeToPlan,
  currentPlan,
  currency,
  isLoading,
  isUpfront,
  isFreeTrial,
  planType,
}) => {
  const [selectedPlan, setSelectedPlan] = useState({ plan: currentPlan?.id, plan_option: currentPlan?.plan_option_id });

  return (
    <>
      <Modal {...{ show, toggle }} title="Select Plan Interval" size="midi">
        <ModalBody>
          {/* <span className=" text-dark block mb-2">Select plan in</span> */}
          {/* <SuccessLabel
            perm
            message="Our prices are changing. To help you lock-in our current prices, we're offering 15% off when you subscribe to any quarterly plan today. Offer ends on the 8th of October."
          /> */}
          {(isUpfront || isFreeTrial) && (
            <InfoCard
              message={
                isUpfront
                  ? `You'll only pay ${toCurrency(
                      UPFRONT_SUBSCRIPTION_AMOUNTS[planType][currency] / 100,
                      currency,
                      false,
                      0
                    )} today — your selection will only take effect after 30 days `
                  : `You won't be charged today — payment only required after your 7-day free trial.`
              }
              className="mb-2.5"
            />
          )}
          {plans
            .sort((a, b) => a?.amount - b?.amount)
            .map((plan, index) => {
              return (
                <div
                  key={index}
                  className="flex items-start justify-between bg-grey-fields-100 mb-2.5 rounded-xl p-4 cursor-pointer"
                  onClick={() => setSelectedPlan({ plan: plan.plan_id, plan_option: plan.id })}
                >
                  <div>
                    <span className="block text-dark text-sm">
                      {plan.interval_text}{" "}
                      {plan?.discount ? (
                        <span className="px-2 font-semibold bg-accent-green-500 ml-1 text-xxs uppercase py-1 text-white rounded-full">
                          {plan?.discount}% OFF
                        </span>
                      ) : null}
                    </span>
                    <h3 className="text-black-secondary font-semibold text-sm mt-3.5 -mb-1.5">
                      {toCurrency(plan.amount, currency, false, 0)}
                    </h3>
                    {plan.actual_amount != plan.amount && plan.actual_amount && (
                      <span className="text-black-muted text-xs line-through">
                        {toCurrency(plan.actual_amount, currency, false, 0)}
                      </span>
                    )}
                  </div>
                  <Radio
                    name={plan.id}
                    chosen={`${selectedPlan.plan}-${selectedPlan.plan_option}`}
                    onChange={() => setSelectedPlan({ plan: plan.plan_id, plan_option: plan.id })}
                    value={`${plan.plan_id}-${plan.id}`}
                    id={plan.id}
                  />
                </div>
              );
            })}
        </ModalBody>
        <ModalFooter>
          <AppBtn
            disabled={selectedPlan === undefined || isLoading}
            isBlock
            onClick={() => subscribeToPlan(selectedPlan.plan, selectedPlan.plan_option)}
            size="lg"
          >
            {isLoading ? "Subscribing..." : "Continue"}
          </AppBtn>
        </ModalFooter>
      </Modal>
    </>
  );
};
export default SelectPlanInterval;
