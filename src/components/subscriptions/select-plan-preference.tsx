import { useState, useMemo } from "react";
import { toCurrency } from "../../assets/js/utils/functions";
import { CURRENCIES, Plan, PLAN_TYPE, PlanOption } from "../../assets/interfaces";
import { AppBtn } from "../ui/buttons";
import Radio from "../ui/form-elements/radio";
import Modal, { ModalBody, ModalFooter } from "../ui/modal";
import SuccessLabel from "../ui/success-label";
import { toNaira } from "@/assets/js/utils/utils";
import { RequestInterface } from "@/api/utils";
import ErrorLabel from "../ui/error-label";

interface Props {
  subscribeToPlan: (plan: string, plan_option: string) => void;
  show: boolean;
  toggle: (state: boolean) => void;
  selectedPlan: Plan;
  payUpfront: boolean;
  setPayUpfront: (state: boolean) => void;
  isLoading?: boolean;
  currency: CURRENCIES;
  subscriptionRequest: RequestInterface<any>;
  selectedPlanOption: PlanOption & { plan_option_id: string };
}

const SelectPlanPreference: React.FC<Props> = ({
  show,
  toggle,
  selectedPlan,
  selectedPlanOption,
  payUpfront,
  setPayUpfront,
  isLoading,
  currency,
  subscribeToPlan,
  subscriptionRequest,
}) => {
  const [upfrontAmount, normalAmount] = useMemo(() => {
    const upfrontAmount = UPFRONT_SUBSCRIPTION_AMOUNTS[selectedPlan.type][currency];

    return [
      toCurrency(toNaira(upfrontAmount), currency, false, 0),
      toCurrency(selectedPlanOption.amount, currency, false, 0),
    ];
  }, [selectedPlan, selectedPlanOption]);

  return (
    <>
      <Modal {...{ show, toggle }} title="One Time Offer" size="midi">
        <ModalBody>
          {subscriptionRequest.error && <ErrorLabel error="Something went wrong, please try again" />}
          <div className="flex flex-col items-center">
            <figure className="h-18 w-18 rounded-full bg-accent-orange-500 m-auto flex items-center justify-center text-white flex-shrink-0">
              {/* prettier-ignore */}
              <svg className="w-1/2" viewBox="0 0 24 24" fill="none">
                <path d="M20 12V18C20 20.21 18.21 22 16 22H8C5.79 22 4 20.21 4 18V12C4 11.45 4.45 11 5 11H6.97C7.52 11 7.97 11.45 7.97 12V15.14C7.97 15.88 8.38 16.56 9.03 16.91C9.32 17.07 9.64 17.15 9.97 17.15C10.35 17.15 10.73 17.04 11.06 16.82L12.01 16.2L12.89 16.79C13.5 17.2 14.28 17.25 14.93 16.9C15.59 16.55 16 15.88 16 15.13V12C16 11.45 16.45 11 17 11H19C19.55 11 20 11.45 20 12Z" fill="currentColor"/>
                <path d="M21.5 7V8C21.5 9.1 20.97 10 19.5 10H4.5C2.97 10 2.5 9.1 2.5 8V7C2.5 5.9 2.97 5 4.5 5H19.5C20.97 5 21.5 5.9 21.5 7Z" fill="currentColor"/>
                <path d="M11.6388 5.00141H6.11881C5.77881 4.63141 5.78881 4.06141 6.14881 3.70141L7.56881 2.28141C7.93881 1.91141 8.54881 1.91141 8.91881 2.28141L11.6388 5.00141Z" fill="currentColor"/>
                <path d="M17.8716 5.00141H12.3516L15.0716 2.28141C15.4416 1.91141 16.0516 1.91141 16.4216 2.28141L17.8416 3.70141C18.2016 4.06141 18.2116 4.63141 17.8716 5.00141Z" fill="currentColor"/>
                <path d="M13.9714 11C14.5214 11 14.9714 11.45 14.9714 12V15.13C14.9714 15.93 14.0814 16.41 13.4214 15.96L12.5214 15.36C12.1914 15.14 11.7614 15.14 11.4214 15.36L10.4814 15.98C9.82141 16.42 8.94141 15.94 8.94141 15.15V12C8.94141 11.45 9.39141 11 9.94141 11H13.9714Z" fill="currentColor"/>
              </svg>
            </figure>

            <h2 className="text-center text-black font-bold text-2xl mx-auto mt-2.5 !leading-tight">
              Here's a special <br /> offer for you
            </h2>
          </div>
          <div className="mt-7.5">
            {options(upfrontAmount, normalAmount).map((option, index) => {
              return (
                <div
                  key={index}
                  className="flex items-start justify-between bg-grey-fields-100 mb-2.5 rounded-xl p-4 cursor-pointer"
                  onClick={() => setPayUpfront(option.key === "upfront")}
                >
                  <div>
                    <h3 className="text-black-secondary font-semibold text-1sm -mb-1.5">{option.label}</h3>
                    <span className="text-black-muted text-1xs">{option.description}</span>
                  </div>
                  <Radio
                    name="mode"
                    chosen={payUpfront ? "upfront" : "normal"}
                    onChange={() => setPayUpfront(option.key === "upfront")}
                    value={option.key}
                    id={option.key}
                  />
                </div>
              );
            })}
          </div>
        </ModalBody>
        <ModalFooter>
          <AppBtn
            disabled={selectedPlan === undefined || isLoading}
            isBlock
            onClick={() => subscribeToPlan(selectedPlan.id, selectedPlanOption.plan_option_id)}
            size="lg"
          >
            {isLoading ? "Subscribing..." : payUpfront ? `Use for 30 days` : `Pay ${normalAmount} after 7 days`}
          </AppBtn>
        </ModalFooter>
      </Modal>
    </>
  );
};

const options = (upfrontAmount: string, normalAmount: string) => [
  {
    key: "upfront",
    label: `Pay ${upfrontAmount} Today`,
    description: "To use your selected plan for 30 days",
  },
  {
    key: "normal",
    label: "Try free for 7 days",
    description: `You'll need to pay ${normalAmount} after 7 days`,
  },
];

export const UPFRONT_SUBSCRIPTION_AMOUNTS = {
  [PLAN_TYPE.BASIC]: {
    [CURRENCIES.NGN]: 3000_00,
    [CURRENCIES.GHC]: 35_00,
    [CURRENCIES.KES]: 300_00,
    [CURRENCIES.ZAR]: 60_00,
  },
  [PLAN_TYPE.BUSINESS_PLUS]: {
    [CURRENCIES.NGN]: 6500_00,
    [CURRENCIES.GHC]: 75_00,
    [CURRENCIES.KES]: 600_00,
    [CURRENCIES.ZAR]: 120_00,
  },
  [PLAN_TYPE.KITCHEN]: {
    [CURRENCIES.NGN]: 15000_00,
    [CURRENCIES.GHC]: 250_00,
    [CURRENCIES.KES]: 2500_00,
    [CURRENCIES.ZAR]: 499_00, //placholder
  },
};

export default SelectPlanPreference;
