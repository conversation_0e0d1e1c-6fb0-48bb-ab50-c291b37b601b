import React, { useState } from "react";
import SliderInput from "../ui/form-elements/slider-input";
import { amountFormat, millify, toCurrency } from "@/assets/js/utils/functions";
import { AppBtn } from "../ui/buttons";
import { PLAN_TYPE, Plan } from "@/assets/interfaces";
import { GroupedKitchenPlan } from "./subscriptions-controller";
import classNames from "classnames";

interface Props {
  plan: GroupedKitchenPlan;
  selectPlan: (optionId: string) => void;
  isLoading?: boolean;
  isRecommended?: boolean;
  openFeatures: (plan: GroupedKitchenPlan) => void;
  currentPlan?: Plan;
}

const KitchenPlanCard: React.FC<Props> = (props) => {
  const { plan, selectPlan, isLoading, isRecommended, openFeatures, currentPlan } = props;

  const [selectedIndex, setSelectedIndex] = useState(
    currentPlan && currentPlan.type === PLAN_TYPE.KITCHEN
      ? plan.options.findIndex((o) => o.id === currentPlan?.plan_option_id) + 1
      : 1
  );

  const selected = plan.options[selectedIndex - 1];

  return (
    <article className="rounded-10 sm:rounded-15 border-grey-border border-opacity-50 border max-w-[750px] mx-auto overflow-hidden">
      {!currentPlan ? (
        <div className="font-medium py-3 sm:py-3.75 text-1xs sm:text-sm bg-green-500 bg-opacity-10 text-green-500 px-4 text-center">
          14 Days Free Trial + 50 Free Chowbot Tokens
        </div>
      ) : null}
      {currentPlan && currentPlan?.chowbot_tokens ? (
        <div className="font-medium py-3 sm:py-3.75 text-1xs sm:text-sm bg-grey-fields-100 text-dark px-4 text-center">
          <div className="flex items-center justify-center">
            <svg width="20" viewBox="0 0 18 18" fill="none" className="text-accent-green-500 mr-1">
              <path
                d="M16.17 8.055L15.15 6.87C14.955 6.645 14.7975 6.225 14.7975 5.925V4.65C14.7975 3.855 14.145 3.2025 13.35 3.2025H12.075C11.7825 3.2025 11.355 3.045 11.13 2.85L9.94503 1.83C9.42753 1.3875 8.58003 1.3875 8.05503 1.83L6.87753 2.8575C6.65253 3.045 6.22503 3.2025 5.93253 3.2025H4.63503C3.84003 3.2025 3.18753 3.855 3.18753 4.65V5.9325C3.18753 6.225 3.03003 6.645 2.84253 6.87L1.83003 8.0625C1.39503 8.58 1.39503 9.42 1.83003 9.9375L2.84253 11.13C3.03003 11.355 3.18753 11.775 3.18753 12.0675V13.35C3.18753 14.145 3.84003 14.7975 4.63503 14.7975H5.93253C6.22503 14.7975 6.65253 14.955 6.87753 15.15L8.06253 16.17C8.58003 16.6125 9.42753 16.6125 9.95253 16.17L11.1375 15.15C11.3625 14.955 11.7825 14.7975 12.0825 14.7975H13.3575C14.1525 14.7975 14.805 14.145 14.805 13.35V12.075C14.805 11.7825 14.9625 11.355 15.1575 11.13L16.1775 9.945C16.6125 9.4275 16.6125 8.5725 16.17 8.055ZM12.12 7.5825L8.49753 11.205C8.39253 11.31 8.25003 11.37 8.10003 11.37C7.95003 11.37 7.80753 11.31 7.70253 11.205L5.88753 9.39C5.67003 9.1725 5.67003 8.8125 5.88753 8.595C6.10503 8.3775 6.46503 8.3775 6.68253 8.595L8.10003 10.0125L11.325 6.7875C11.5425 6.57 11.9025 6.57 12.12 6.7875C12.3375 7.005 12.3375 7.365 12.12 7.5825Z"
                fill="currentColor"
              />
            </svg>
            <div>
              Current Plan:{" "}
              <span className="text-black-secondary inline">
                {amountFormat(currentPlan.chowbot_tokens, 0)} Chowbot Tokens
              </span>
            </div>
          </div>
        </div>
      ) : null}
      <div className="pt-2.5 sm:pt-7.75 p-6.25 sm:p-7.5 flex flex-col">
        <div className="flex flex-col pb-3.75 sm:pb-5 border-b border-grey-border border-opacity-50">
          <div className="w-full flex items-center justify-between flex-wrap">
            <div className="flex flex-col min-w-[280px] py-2.5">
              <h3 className="text-black text-2xl sm:text-[30px] lg:text-[32px] font-bold !leading-none">{plan.name}</h3>
              <p className="text-black-placeholder text-1xs sm:text-sm !leading-tight mt-1 sm:mt-1.25 max-w-[400px]">
                For food businesses looking to sell more on Whatsapp, simplify delivery process & keep records
                efficiently{" "}
              </p>
            </div>
            <div className="font-bold font-display flex-shrink-0 py-2.5">
              <h2 className="text-primary-500 text-3xl sm:text-[34px] lg:4xl">
                {toCurrency(selected.amount, plan.country.currency, false, 0)}
              </h2>
              <span className="text-grey-muted text-1xs sm:text-sm lg:text-1sm">/Monthly</span>
            </div>
          </div>
          <div className="flex flex-col items-center mt-4.25 sm:mt-6">
            <span className="text-dark font-semibold font-body text-1xs sm:text-sm">
              {/* {amountFormat(selected?.chowbot_tokens, 0)} Chowbot Tokens */}
              How many customers do you have monthly?
            </span>
            <div className="flex flex-col w-full">
              <SliderInput
                className="mt-2.5"
                value={selectedIndex}
                max={7}
                min={1}
                start={0}
                onChange={setSelectedIndex}
                theme="success"
                showLabel={false}
                minLabel="250"
                maxLabel="10k"
              />
              <div className="flex items-center justify-between w-full mt-1.5">
                <div className="text-placeholder text-xs flex-1 text-left">0</div>
                {plan.options.map((plan, index) => (
                  <div
                    className={classNames("text-xxs sm:text-xs flex-1 text-left", {
                      "text-black-secondary font-medium": selectedIndex - 1 === index,
                      "text-placeholder": selectedIndex - 1 !== index,
                    })}
                    key={index}
                  >
                    {millify(plan?.chowbot_tokens, 1)}
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="text-sm mt-3.75 sm:mt-5 flex items-start bg-accent-yellow-pastel rounded-10 px-2.5 py-2.5 text-left">
            <figure className="h-6.25 w-6.25 sm:h-8 sm:w-8 rounded-full bg-white shadow-pill flex items-center justify-center flex-shrink-0 mr-2 sm:mr-3 text-base">
              {/* prettier-ignore */}
              <svg width="70%" className="text-accent-yellow-500" viewBox="0 0 24 24" fill="none">
                <path d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM11.25 8C11.25 7.59 11.59 7.25 12 7.25C12.41 7.25 12.75 7.59 12.75 8V13C12.75 13.41 12.41 13.75 12 13.75C11.59 13.75 11.25 13.41 11.25 13V8ZM12.92 16.38C12.87 16.51 12.8 16.61 12.71 16.71C12.61 16.8 12.5 16.87 12.38 16.92C12.26 16.97 12.13 17 12 17C11.87 17 11.74 16.97 11.62 16.92C11.5 16.87 11.39 16.8 11.29 16.71C11.2 16.61 11.13 16.51 11.08 16.38C11.03 16.26 11 16.13 11 16C11 15.87 11.03 15.74 11.08 15.62C11.13 15.5 11.2 15.39 11.29 15.29C11.39 15.2 11.5 15.13 11.62 15.08C11.86 14.98 12.14 14.98 12.38 15.08C12.5 15.13 12.61 15.2 12.71 15.29C12.8 15.39 12.87 15.5 12.92 15.62C12.97 15.74 13 15.87 13 16C13 16.13 12.97 16.26 12.92 16.38Z" fill="currentColor"/>
              </svg>
            </figure>
            <div className="text-xs !leading-snug sm:text-1xs text-black-muted">
              <b className="font-medium text-black-secondary">Chowbot uses tokens:</b> One token allows one customer to
              interact with your business via the chatbot for 24 hours. WhatsApp takes a fee for each conversation, but
              we’ve discounted the tokens to support your business
            </div>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row space-y-2.5 sm:space-y-0 sm:space-x-3.75 pt-3.75 sm:pt-5">
          <div className="flex-1">
            <AppBtn size="lg" isBlock color="neutral" onClick={() => openFeatures(plan)}>
              View Features
            </AppBtn>
          </div>
          <div className="flex-1">
            <AppBtn
              size="lg"
              isBlock
              disabled={currentPlan?.plan_option_id === selected?.id}
              onClick={() => selectPlan(selected.id)}
            >
              Select Plan
            </AppBtn>
          </div>
        </div>
      </div>
    </article>
  );
};

export default KitchenPlanCard;
