import cx from "classnames";
import React from "react";
import { toCurrency } from "../../assets/js/utils/functions";
import { COUNTRIES, CountryInterface, CURRENCIES, Plan, PLAN_TYPE, SubscriptionPlan } from "../../assets/interfaces";
import { AppBtn } from "../ui/buttons";
import { COUNTRY_CURRENCY_MAP, FOOD_COMPARISONS, PLAN_HOOKS } from "@/assets/js/utils/constants";
import { GroupedPlan } from "./subscriptions-controller";
import authContext from "@/contexts/auth-context";
import { UPFRONT_SUBSCRIPTION_AMOUNTS } from "./select-plan-preference";
import { Building } from "iconsax-react";

interface Props {
  plan: GroupedPlan;
  selectPlan: () => void;
  isRecommended?: boolean;
  openFeatures: (plan: GroupedPlan) => void;
  currentPlan?: Plan;
  isUpfront?: boolean;
  isFreeTrial?: boolean;
  currency: CURRENCIES;
}

const NewPlanSelectorCard: React.FC<Props> = ({
  plan,
  selectPlan,
  isRecommended,
  openFeatures,
  currentPlan,
  isUpfront,
  isFreeTrial,
  currency,
}) => {
  const { name, is_paid_plan, amount, description: descriptions, interval_text, interval, options } = plan;

  const isCurrentPlan = currentPlan?.type === plan.type;
  const planHasOptions = options && Object.values(options).length > 1;

  const pricingData = {
    amount: isUpfront ? UPFRONT_SUBSCRIPTION_AMOUNTS[plan.type][currency] / 100 : amount,
    interval_text: isUpfront ? "For 30 Days" : interval_text,
  };

  return (
    <>
      <article className="rounded-15 border-grey-border border-opacity-50 border max-w-[450px] m-auto overflow-hidden relative">
        {currentPlan && isCurrentPlan && (
          <div className="flex items-center justify-center px-2.5 py-2.5 bg-grey-fields-100 text-accent-green-500 w-full">
            {/* prettier-ignore */}
            <svg width="16" viewBox="0 0 18 18" fill="none">
              <path d="M16.17 8.055L15.15 6.87C14.955 6.645 14.7975 6.225 14.7975 5.925V4.65C14.7975 3.855 14.145 3.2025 13.35 3.2025H12.075C11.7825 3.2025 11.355 3.045 11.13 2.85L9.94503 1.83C9.42753 1.3875 8.58003 1.3875 8.05503 1.83L6.87753 2.8575C6.65253 3.045 6.22503 3.2025 5.93253 3.2025H4.63503C3.84003 3.2025 3.18753 3.855 3.18753 4.65V5.9325C3.18753 6.225 3.03003 6.645 2.84253 6.87L1.83003 8.0625C1.39503 8.58 1.39503 9.42 1.83003 9.9375L2.84253 11.13C3.03003 11.355 3.18753 11.775 3.18753 12.0675V13.35C3.18753 14.145 3.84003 14.7975 4.63503 14.7975H5.93253C6.22503 14.7975 6.65253 14.955 6.87753 15.15L8.06253 16.17C8.58003 16.6125 9.42753 16.6125 9.95253 16.17L11.1375 15.15C11.3625 14.955 11.7825 14.7975 12.0825 14.7975H13.3575C14.1525 14.7975 14.805 14.145 14.805 13.35V12.075C14.805 11.7825 14.9625 11.355 15.1575 11.13L16.1775 9.945C16.6125 9.4275 16.6125 8.5725 16.17 8.055ZM12.12 7.5825L8.49753 11.205C8.39253 11.31 8.25003 11.37 8.10003 11.37C7.95003 11.37 7.80753 11.31 7.70253 11.205L5.88753 9.39C5.67003 9.1725 5.67003 8.8125 5.88753 8.595C6.10503 8.3775 6.46503 8.3775 6.68253 8.595L8.10003 10.0125L11.325 6.7875C11.5425 6.57 11.9025 6.57 12.12 6.7875C12.3375 7.005 12.3375 7.365 12.12 7.5825Z" fill="currentColor"/>
            </svg>
            <span className="block text-1xs ml-1 font-semibold leading-none">Current Plan</span>
          </div>
        )}
        {/* <figure className="bg-grey-fields-100 rounded-full h-15 w-15 flex items-center justify-center absolute top-5 right-4 text-placeholder text-opacity-50">
          <Building size={24} variant="Bulk" strokeWidth={1} />
        </figure> */}
        <div className="p-4">
          <div className="flex justify-between mb-2 ">
            <div className="flex items-center mt-1">
              <h5 className={cx("text-[17px] md:text-xl font-bold text-black-secondary font-display")}>{name}</h5>
            </div>

            {/* {isCurrentPlan && (
              <div className="flex items-center h-[fit-content] justify-between px-2.5 py-2 bg-accent-green-500 text-accent-green-500 bg-opacity-10 rounded-full">
                prettier-ignore
                <svg width="16" viewBox="0 0 18 18" fill="none">
                  <path d="M16.17 8.055L15.15 6.87C14.955 6.645 14.7975 6.225 14.7975 5.925V4.65C14.7975 3.855 14.145 3.2025 13.35 3.2025H12.075C11.7825 3.2025 11.355 3.045 11.13 2.85L9.94503 1.83C9.42753 1.3875 8.58003 1.3875 8.05503 1.83L6.87753 2.8575C6.65253 3.045 6.22503 3.2025 5.93253 3.2025H4.63503C3.84003 3.2025 3.18753 3.855 3.18753 4.65V5.9325C3.18753 6.225 3.03003 6.645 2.84253 6.87L1.83003 8.0625C1.39503 8.58 1.39503 9.42 1.83003 9.9375L2.84253 11.13C3.03003 11.355 3.18753 11.775 3.18753 12.0675V13.35C3.18753 14.145 3.84003 14.7975 4.63503 14.7975H5.93253C6.22503 14.7975 6.65253 14.955 6.87753 15.15L8.06253 16.17C8.58003 16.6125 9.42753 16.6125 9.95253 16.17L11.1375 15.15C11.3625 14.955 11.7825 14.7975 12.0825 14.7975H13.3575C14.1525 14.7975 14.805 14.145 14.805 13.35V12.075C14.805 11.7825 14.9625 11.355 15.1575 11.13L16.1775 9.945C16.6125 9.4275 16.6125 8.5725 16.17 8.055ZM12.12 7.5825L8.49753 11.205C8.39253 11.31 8.25003 11.37 8.10003 11.37C7.95003 11.37 7.80753 11.31 7.70253 11.205L5.88753 9.39C5.67003 9.1725 5.67003 8.8125 5.88753 8.595C6.10503 8.3775 6.46503 8.3775 6.68253 8.595L8.10003 10.0125L11.325 6.7875C11.5425 6.57 11.9025 6.57 12.12 6.7875C12.3375 7.005 12.3375 7.365 12.12 7.5825Z" fill="currentColor"/>
                </svg>
                <span className="block text-xs ml-1 font-semibold leading-none">Current Plan</span>
              </div>
            )} */}
          </div>
          <div className="flex flex-col mb-1.5">
            <h1 className="text-primary text-2lg sm:text-[26px] text-black font-bold !leading-none">
              {is_paid_plan ? toCurrency(pricingData.amount, currency, false, 0) : "Free"}
              <span className="text-xs sm:text-1xs font-normal text-grey-subtext"> / {pricingData.interval_text}</span>
            </h1>
            {isUpfront && (
              <h5 className="text-sm sm:text-1sm text-placeholder font-bold leading-none line-through font-display mt-1.5">
                {toCurrency(amount, currency, false, 0)}
              </h5>
            )}
            {isFreeTrial && (
              <span className="text-1xs text-dark font-medium leading-none inline-block mt-1.5">
                No Payment or card required today
              </span>
            )}
          </div>
          <hr className="w-full border-t border-grey-divider my-3.75 h-px"></hr>
          <span className="text-dark text-sm flex flex-col">
            {PLAN_HOOKS[plan.type].hook} for the price of {plan.type === PLAN_TYPE.BASIC ? "1" : "2"}{" "}
            {FOOD_COMPARISONS[currency].fooditem}
            {plan.type === PLAN_TYPE.BASIC ? "" : "s"} {FOOD_COMPARISONS[currency].emoji}
          </span>
          <div className="mt-2.5">
            <button
              className="font-medium text-sm flex items-center text-primary-500"
              onClick={() => openFeatures(plan)}
            >
              What's Included
              {/* prettier-ignore */}
              <svg className="w-3.75 ml-px mt-px" viewBox="0 0 15 16" fill="none">
                <path d="M3.96484 11.5355L11.0359 4.46446" stroke="#332089" strokeWidth="1.7" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M11.0352 11.5355L11.0352 4.46446L3.96409 4.46445" stroke="#332089" strokeWidth="1.7" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </button>
          </div>
        </div>

        <div className="mb-3 flex items-center justify-between m-auto px-4">
          {(planHasOptions || !isCurrentPlan) && (
            <AppBtn color="primary" isBlock size="lg" onClick={selectPlan}>
              {isCurrentPlan
                ? "Change Interval"
                : isUpfront
                ? "Select Plan"
                : isFreeTrial
                ? "Try Free for 7 Days"
                : planHasOptions
                ? "Choose Interval"
                : "Select Plan"}
            </AppBtn>
          )}

          {!planHasOptions && isCurrentPlan && (
            <AppBtn color="success" isBlock size="lg">
              Current Plan
            </AppBtn>
          )}
        </div>
      </article>
    </>
  );
};

export default NewPlanSelectorCard;
