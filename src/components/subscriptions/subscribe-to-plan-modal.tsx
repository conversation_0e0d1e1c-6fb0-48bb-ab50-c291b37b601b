import router, { useRouter } from "next/router";
import React, { useState } from "react";
import { toCurrency } from "../../assets/js/utils/functions";
import { COUNTRIES, CURRENCIES, CountryInterface, PAYMENT_TYPES, Plan } from "../../assets/interfaces";
import useCopyClipboard from "../hooks/useCopyClipboard";
import { AppBtn } from "../ui/buttons";
import Modal, { ModalBody } from "../ui/modal";
import PayWithMomo from "./pay-with-momo";
import PayWithPaystack from "./pay-with-paystack";
import SuccessAnimation from "../ui/success-animation";
import ErrorIcon from "../../assets/icons/statuses/error.svg";
import MakePayments from "../make-payments";
import authContext from "../../contexts/auth-context";

interface Props {
  show: boolean;
  toggle: (show: boolean) => void;
  plan: Plan;
  renew?: boolean;
  handleSuccess?: (ref: string) => void;
  payUpfront?: boolean;
}

enum PAYMENT_METHODS {
  PAYSTACK = "PAYSTACK",
  CARD = "CARD",
  WALLET = "WALLET",
  CATLOG_CREDITS = "CATLOG_CREDITS",
  TRF_MOMO = "TRF_MOMO",
}

const SubscribeToPlanModal: React.FC<Props> = ({ show, toggle, plan, renew, handleSuccess, payUpfront }) => {
  const { fetchUserSession } = authContext.useContainer();
  const [state, setState] = useState<"verifying" | "make-payment" | "success" | "error">("make-payment");

  const onSuccess = (ref?: string) => {
    if (handleSuccess) {
      handleSuccess(ref);
    } else {
      fetchUserSession();
      router.push(payUpfront ? "/get-started" : "/dashboard");
      // router.push("/dashboard");
    }

    toggle(false);
  };

  return (
    <Modal
      {...{ show, toggle }}
      title={`${renew ? "Renew your" : "Subscribe to"} ${plan.name} plan`}
      closeable={state !== "success"}
      size="midi"
      bgClose={false}
    >
      <ModalBody>
        <MakePayments
          plan={plan}
          paymentType={PAYMENT_TYPES.SUBSCRIPTION}
          successMessage={`You've successfully subscribed to the ${plan.name} plan`}
          handleSuccess={onSuccess}
          upfrontSubscription={payUpfront}
        />
      </ModalBody>
    </Modal>
  );
};

export default SubscribeToPlanModal;
