import { useRouter } from "next/router";
import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { GetPlansParams } from "../../api/interfaces/payments.interface";
import { GetPlans, SwitchToFreePlan, CreateFreeSubscription } from "../../api";
import { useFetcher, useRequest } from "../../api/utils";
import { reloadPage } from "../../assets/js/utils/functions";
import { CURRENCIES, CountryInterface, PLAN_TYPE, Plan } from "../../assets/interfaces";
import { useModals } from "../../components/hooks/useModals";
import {
  PlanConfirmationModal,
  SelectPlanInterval,
  PlanFeatures,
  PlanSelectorCard,
  SubscribeToPlanModal,
} from "./index";
import { AppBtn } from "../../components/ui/buttons";
import ErrorBox from "../../components/ui/error";
import authContext from "../../contexts/auth-context";
import KitchenPlanCard from "./kitchen-plan-card";
import SelectPlanPreference, { UPFRONT_SUBSCRIPTION_AMOUNTS } from "./select-plan-preference";
import { COUNTRY_CURRENCY_MAP, CURRENCY_COUNTRY_MAP } from "@/assets/js/utils/constants";
import { mixpanelTrack, toNaira } from "@/assets/js/utils/utils";
import useTabs from "../hooks/useTabs";
import { WalletTabButton } from "../dashboard/wallets";
import { TabButton } from "../deliveries/modals/add-address";
import NewPlanSelectorCard from "./new-plan-card-dashboard";
import { InfoCircle } from "iconsax-react";
import InfoCard from "../ui/info-card";

interface ControllerProps {
  context?: "store" | "user";
  redirect?: string;
  isSetup?: boolean;
}

interface KitchenPlanOption {
  id: string;
  price_per_tokens: number;
  chowbot_tokens?: number;
  discount?: number;
  amount: number;
  plan_id: string;
}

export interface PlanOption {
  id: string;
  interval: number;
  interval_text: string;
  amount: number;
  actual_amount?: number;
  discount: number;
  plan_id: string;
  plan_type: PLAN_TYPE;
}

export interface GroupedKitchenPlan {
  options: KitchenPlanOption[];
  name: string;
  description: { title: string; features: string[] }[];
  interval: number;
  type: PLAN_TYPE;
  id: string;
  interval_text: string;
  country: CountryInterface;
  is_paid_plan?: boolean;
  amount: number;
}

export interface GroupedPlan {
  options: { [key: string]: PlanOption };
  name: string;
  description: { title: string; features: string[] }[];
  interval: number;
  type: PLAN_TYPE;
  id: string;
  interval_text: string;
  country: CountryInterface;
  is_paid_plan?: boolean;
  amount: number;
}

const SubscriptionsController: React.FC<ControllerProps> = ({ context = "store", redirect, isSetup }) => {
  const router = useRouter();
  const { user, stores, storeIndex, store, updateUser, updateStore, handleRedirects, redirectTo } =
    authContext.useContainer();
  const currency = store?.country?.currency ?? COUNTRY_CURRENCY_MAP[store?.country as any];
  const { isLoading, error, response } = useFetcher<GetPlansParams>(
    GetPlans,
    {
      country: typeof store?.country === "string" ? store?.country : store?.country?.code,
    },
    ["country"]
  );
  const changePlanRequest = useRequest(SwitchToFreePlan);
  const createFreeSubRequest = useRequest(CreateFreeSubscription);
  const [selectedPlan, setSelectedPlan] = useState<Plan>(null);
  const [planOptionId, setPlanOptionId] = useState<string>(null);
  const [plans, setPlans] = useState<Plan[]>([]);
  const [showSuccess, setShowSuccess] = useState(false);
  const [payUpfront, setPayUpfront] = useState(false);
  const [planFeatures, setPlanFeatures] = useState<{ name: string; features: { title: string; features: string[] }[] }>(
    null
  );

  const { tabs, switchTab, switchByKey, acitveKey, active, activeValue } = useTabs(
    ["Try for 30 Days", "Try Free for 7 Days"],
    isSetup ? 0 : 1
  );
  const { modals, toggleModal } = useModals(["make_payment", "confirm", "features", "select", "preference"]);

  const getSelectedPlanOption = (planOptionId: string) => {
    return plans.find((p) => selectedPlan?.id === p.id && planOptionId === p.plan_option_id);
  };

  const currentPlan: Plan = context === "user" ? user?.subscription?.plan : store?.subscription?.plan;
  const groupedPlans = groupPlans() as GroupedPlan[];
  const groupedKitchenPlans = groupKitchenPlans() as GroupedKitchenPlan;

  useEffect(() => {
    if (response) {
      const thePlans = response?.data.plans;
      thePlans.sort((a, b) => (a?.amount < b?.amount ? -1 : 1));
      setPlans(thePlans);
    }
  }, [response]);

  const handleViewFeatures = (plan: GroupedKitchenPlan | GroupedPlan) => {
    setPlanFeatures({
      name: plan.name,
      features: plan.description,
    });
    toggleModal("features");
  };

  const selectPlan = (plan: GroupedPlan | GroupedKitchenPlan, optionId?: string) => {
    const actualPlan = plans.find((p) => plan.id === p.id && (optionId ? optionId === p.plan_option_id : true));
    setSelectedPlan(actualPlan);

    if (Object.values(plan.options).length > 1 && plan.type !== PLAN_TYPE.KITCHEN) {
      toggleModal("select");
      return;
    }

    if (currentPlan && plan.is_paid_plan) {
      toggleModal("make_payment");
      return;
    }

    toggleModal("confirm");
  };

  async function subscribe(plan: string, plan_option: string, payUpfront?: boolean) {
    const selectedPlanOption = getSelectedPlanOption(plan_option);

    if (!currentPlan && !payUpfront) {
      const [res, err] = await createFreeSubRequest.makeRequest({ plan, plan_option });

      if (res) {
        mixpanelTrack("Start Free Trial", {
          email: user?.email,
          plan: selectedPlan?.name,
          planInterval: selectedPlanOption?.interval_text,
        });
        setTimeout(() => {
          handleSuccess(plan);
        }, 1000);
      }

      return;
    }

    if (!selectedPlan.is_paid_plan) {
      const [res, err] = await changePlanRequest.makeRequest({ plan });

      if (res) {
        setTimeout(() => {
          handleSuccess(plan);
        }, 1000);
      }

      return;
    }

    //since we're only getting the plan id
    //search the actual list of plans for the plan - so the subscribe modal can use it.
    const actualPlan = plans.find((p) => plan === p.id && plan_option === p.plan_option_id);

    if (actualPlan) {
      const updatedPlan = {
        ...actualPlan,
        amount:
          payUpfront && selectedPlanOption && !currentPlan
            ? toNaira(UPFRONT_SUBSCRIPTION_AMOUNTS[actualPlan.type][currency])
            : actualPlan.amount,
      };
      setSelectedPlan(updatedPlan);
    }

    toggleModal("make_payment");
    return;
  }

  //creates a new plans array
  //each plan has an options object
  //the options object has a list of different intervals of the plan
  function groupPlans() {
    const thePlans: Plan[] = [];

    if (!plans) {
      return [];
    }

    plans.forEach((plan) => {
      if (plan.type === PLAN_TYPE.KITCHEN || plan.type === PLAN_TYPE.STARTER) return;

      const planDuration = (() => {
        switch (plan.interval) {
          case 30:
            return "MONTHLY";
          case 90:
            return "QUARTERLY";
          case 180:
            return "BI-ANNUALLY";
          default:
            return "YEARLY";
        }
      })();

      const planIndex = thePlans.findIndex((p) => p.type === plan.type);
      const planExists = planIndex !== -1;

      //add plan to plan options if plan already exists
      if (planExists) {
        thePlans[planIndex].options[planDuration] = {
          id: plan.plan_option_id,
          interval: plan.interval,
          interval_text: plan.interval_text,
          amount: plan.amount,
          discount: plan?.discount,
          plan_id: plan.id,
          actual_amount: plan?.actual_amount,
        } as PlanOption;

        return;
      }

      //add fresh plan if plan doesnt exist
      thePlans.push({
        ...plan,
        options: {
          [planDuration]: {
            id: plan.plan_option_id,
            interval: plan.interval,
            interval_text: plan.interval_text,
            amount: plan.amount,
            discount: plan?.discount,
            plan_id: plan.id,
            actual_amount: plan?.actual_amount,
          } as PlanOption,
        },
      });
    });

    return thePlans;
  }

  function groupKitchenPlans() {
    if (!plans || plans.length < 1) {
      return [];
    }

    const kitchenPlans = plans.filter((plan) => plan.type === PLAN_TYPE.KITCHEN);

    if (kitchenPlans.length < 1) {
      return null;
    }

    const options: KitchenPlanOption[] = kitchenPlans
      .map((plan) => {
        return {
          id: plan.plan_option_id,
          price_per_tokens: plan.price_per_token,
          chowbot_tokens: plan.chowbot_tokens,
          discount: plan?.discount,
          amount: plan?.amount,
          plan_id: plan.id,
        };
      })
      .sort((a, b) => a.chowbot_tokens - b.chowbot_tokens);

    const kitchenPlan = kitchenPlans[0];
    const { price_per_token, chowbot_tokens, discount, plan_option_id, ...rest } = kitchenPlan;

    return {
      ...rest,
      options,
    };
  }

  function handleSuccess(planId: string, paymentRef?: string) {
    const planOption = getSelectedPlanOption(planOptionId);

    if (payUpfront) {
      try {
        mixpanelTrack("Paid Upfront", {
          email: user?.email,
          plan: selectedPlan?.name,
          planInterval: planOption?.interval_text,
        });
      } catch (error) {
        //do nothing
      }
    } else {
      mixpanelTrack("Subscribed", {
        email: user?.email,
        plan: selectedPlan?.name,
        planInterval: planOption?.interval_text,
      });
    }

    updateState(planId);
    fbq("track", "Subscribe", {}, { eventID: paymentRef });
    ttq.track("Subscribe");

    if (redirect) {
      router.push(redirect);
      return;
    }

    if (redirectTo) {
      handleRedirects();
      return;
    }

    if (modals.confirm.show) {
      toggleModal("confirm");
    }

    if (modals.select.show) {
      toggleModal("select");
    }

    setShowSuccess(true);
  }

  //updates the state with subscription data after subscription
  function updateState(planId: string) {
    const today = new Date();
    const next_payment_date = today;
    next_payment_date.setDate(today.getDate() + 30);

    const subscriptionMockData = {
      subscription: {
        created_at: "",
        id: "",
        last_payment_date: today.toISOString(),
        next_payment_date: next_payment_date.toISOString(),
        owner: user?.id,
        payments: [],
        plan: { ...selectedPlan, id: planId },
        status: "ACTIVE",
        updated_at: "",
        is_free: true,
      },
    };

    //update user & store regardless of if context
    if (store.owner === user?.id) {
      const userCopy = { ...user, ...subscriptionMockData };
      const storesCopy = [...stores];
      const storeData = { ...store, ...subscriptionMockData };
      storesCopy[storeIndex] = storeData;

      //update the user with the store internally updated
      updateUser({ ...userCopy, stores: storesCopy });

      return;
    }

    if (context === "store") {
      updateStore(subscriptionMockData);
      return;
    }

    if (context === "user") {
      updateUser(subscriptionMockData);
      return;
    }
  }

  function getPlanOptions() {
    if (store?.flags?.uses_chowbot) {
      return null;
    }

    return selectedPlan ? Object.values(groupedPlans.find((p) => selectedPlan?.id === p.id)?.options ?? {}) : [];
  }

  return (
    <>
      <div className="mt-12 flex flex-col w-full items-center">
        {/* <button className="bg-grey-fields-100 py-2.5 px-6 sm:py-3 sm:px-7 flex items-center mb-8 text-primary-500 rounded-[50px] justify-center">
          <span className="text-1sm font-medium">Compare pricing</span>
          prettier-ignore
          <svg width="10" height="10" viewBox="0 0 10 10" fill="none" className="ml-1 mt-0.5">
            <path d="M1.24264 0.24265V1.73818L7.16643 1.74348L0.71231 8.1976L1.77297 9.25826L8.22709 2.80414L8.23239 8.72793H9.72792V0.24265H1.24264Z" fill="#332089"/>
          </svg>
        </button> */}
        {error && (
          <ErrorBox title="Something went wrong" message="">
            <AppBtn className="mt-5" size="md" onClick={reloadPage}>
              Reload page
            </AppBtn>
          </ErrorBox>
        )}
        {isLoading && (
          <div className="flex flex-col items-center text-primary-500 py-[150px]">
            <div className="spinner"></div>
            <span className="text-sm font-semibold block mt-5 text-grey-placeholder">Loading Plans...</span>
          </div>
        )}

        {showSuccess && (
          <div className="max-w-md w-full mx-auto bg-grey-fields-100 rounded-8 text-sm font-medium text-accent-green-500 text-center px-5 py-2.5 mb-5 -mt-2">
            Your subscription to the {selectedPlan?.name} plan was successful
          </div>
        )}
        {!isLoading && plans && plans?.length > 0 && (
          <div className="w-full mx-auto">
            {!store?.flags?.uses_chowbot && isSetup && (
              <div className="w-full mx-auto max-w-[450px]">
                <div className="flex items-center space-x-2.5 mb-7.5">
                  {tabs.map((t, i) => (
                    <TabButton label={t} isActive={active === i} key={i} onClick={() => switchTab(i)} />
                  ))}
                </div>
                <InfoCard
                  message={
                    active === 0
                      ? "Pay a small amount to try Catlog for 30 Days"
                      : "Try free for 7 days. Full payment required after"
                  }
                  className="mb-2.5"
                />
              </div>
            )}

            {!store?.flags?.uses_chowbot &&
              groupedPlans.map((plan, index) => (
                <div key={index} className="mb-5">
                  <NewPlanSelectorCard
                    plan={plan}
                    isRecommended={plan.type === "BASIC"}
                    selectPlan={() => selectPlan(plan)}
                    openFeatures={handleViewFeatures}
                    currentPlan={currentPlan}
                    isUpfront={active === 0 && isSetup}
                    isFreeTrial={active === 1 && isSetup}
                    currency={currency}
                  />
                </div>
              ))}

            {store?.flags?.uses_chowbot && (
              <KitchenPlanCard
                plan={groupedKitchenPlans}
                selectPlan={(optionId) => selectPlan(groupedKitchenPlans, optionId)}
                openFeatures={handleViewFeatures}
                currentPlan={currentPlan}
              />
            )}
          </div>
        )}
      </div>

      {selectedPlan && !store?.flags?.uses_chowbot && (
        <SelectPlanInterval
          show={modals.select.show}
          toggle={() => toggleModal("select")}
          plans={getPlanOptions()}
          subscribeToPlan={
            (plan, plan_option) => {
              setPlanOptionId(plan_option);
              setPayUpfront(isSetup && active === 0);
              subscribe(plan, plan_option, isSetup && active === 0);
            }
            // currentPlan
            //   ? subscribe
            //   : (plan, plan_option) => {
            //       setPlanOptionId(plan_option);
            //       toggleModal("preference");
            //     }
          }
          currentPlan={currentPlan}
          isLoading={createFreeSubRequest?.isLoading}
          currency={currency}
          isUpfront={active === 0 && isSetup}
          isFreeTrial={active === 1 && isSetup}
          planType={selectedPlan?.type}
        />
      )}

      {selectedPlan && planOptionId && (
        <SelectPlanPreference
          show={modals.preference.show}
          toggle={() => toggleModal("preference")}
          selectedPlan={selectedPlan}
          selectedPlanOption={getSelectedPlanOption(planOptionId) as any}
          subscribeToPlan={subscribe}
          payUpfront={payUpfront}
          setPayUpfront={setPayUpfront}
          isLoading={createFreeSubRequest?.isLoading}
          subscriptionRequest={createFreeSubRequest}
          currency={currency}
        />
      )}

      {/* handles confirmation for when
       - User is subscribing for the first time
      - User is switching to a free plan */}
      {selectedPlan && (
        <PlanConfirmationModal
          show={modals.confirm.show}
          toggle={() => toggleModal("confirm")}
          pickedPlan={selectedPlan}
          subscribe={() => subscribe(selectedPlan.id, selectedPlan.plan_option_id)}
          subscriptionRequest={createFreeSubRequest}
          currentPlan={currentPlan}
        />
      )}

      {/* Hanldes payment collection */}
      {selectedPlan && (
        <SubscribeToPlanModal
          show={modals.make_payment.show}
          toggle={() => toggleModal("make_payment")}
          plan={selectedPlan}
          handleSuccess={(paymentRef) => {
            handleSuccess(selectedPlan.id, paymentRef);
          }} //if upfront, we don't need to handle success so the page automatically reloads
          payUpfront={payUpfront && !currentPlan}
        />
      )}

      {/* shows plan features */}
      {planFeatures && (
        <PlanFeatures show={modals.features.show} toggle={() => toggleModal("features")} plan={planFeatures} />
      )}
    </>
  );
};

export default SubscriptionsController;
