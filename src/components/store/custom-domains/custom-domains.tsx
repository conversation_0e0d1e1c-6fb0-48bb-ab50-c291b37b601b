import { useState, useEffect, useRef, useMemo } from "react";
import { AppBtn } from "../../ui/buttons";
import { useFetcher, useRequest } from "../../../api/utils";
import { FormikProps } from "formik";
import { ConfigurationForm } from "../store-configurations/index";
import { GetDomains, RemoveDomain, GenerateSslCertificate, VerifyDomain } from "../../../api/stores";
import Portal from "@/components/portal";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import { useModals } from "@/components/hooks/useModals";
import ErrorLabel from "@/components/ui/error-label";
import SuccessLabel from "@/components/ui/success-label";
import authContext from "../../../contexts/auth-context";
import { actionIsAllowed, SCOPES } from "../../../assets/js/utils/permissions";
import ErrorBox from "../../ui/error";
import ConfirmDomainDelete from "./confirm-domain-delete";

export interface DomainData {
  domain: string;
  verified: boolean;
  verification_code: string;
  store_id: string;
  created_at: string;
  updated_at: string;
  id: string;
  certificate_issued: boolean;
  verified_at?: string;
  certificate_issued_at?: string;
}

interface Props {}

export const CustomDomains: React.FC<Props> = () => {
  const [domains, setDomains] = useState<DomainData[]>([]);
  const [domainToUpdate, setDomainToUpdate] = useState<{
    id: string;
    action: "verifying" | "generatingSSL" | "removing";
  } | null>(null);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  const { subscription } = authContext.useContainer();
  const { modals, toggleModal } = useModals(["delete_domain"]);

  const serverIp = process.env.NEXT_PUBLIC_SERVER_IP || "**************";

  const getDomainsReq = useFetcher(GetDomains, {});
  const removeDomainReq = useRequest(RemoveDomain);
  const generateSslReq = useRequest(GenerateSslCertificate);
  const verifyDomainReq = useRequest(VerifyDomain);

  const domainToDelete =
    domainToUpdate?.action === "removing" ? domains.find((domain) => domain.id === domainToUpdate.id) : null;

  // const domains = getDomainsReq.response?.data || [];

  let canManageCustomDomains = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_CUSTOM_DOMAINS,
  });

  useEffect(() => {
    if (getDomainsReq.response?.data) {
      setDomains(getDomainsReq.response?.data || []);
    }
  }, [getDomainsReq.response?.data]);

  const openDeleteModal = (domainId: string, domainName: string) => {
    setDomainToUpdate({ id: domainId, action: "removing" });
    toggleModal("delete_domain");
  };

  const handleRemoveDomain = async () => {
    if (domainToUpdate?.action !== "removing") return;

    const [response, error] = await removeDomainReq.makeRequest({ id: domainToUpdate.id });

    if (error) {
      setErrorMessage(error.message ?? "Failed to remove domain");
    } else {
      setSuccessMessage("Domain removed successfully");
      // Update domains list by removing the deleted domain
      setDomains((prevDomains) => prevDomains.filter((domain) => domain.id !== domainToUpdate.id));
    }

    setDomainToUpdate(null);
    toggleModal("delete_domain");
    scrollToTop();
  };

  const handleGenerateSSL = async (domainId: string) => {
    setDomainToUpdate({ id: domainId, action: "generatingSSL" });

    const [response, error] = await generateSslReq.makeRequest({ id: domainId });

    if (error) {
      setErrorMessage(error.message ?? "Failed to generate SSL certificate");
    } else {
      setSuccessMessage("SSL certificate generation initiated");
      // Update the domain in the list with the response data
      if (response.data) {
        setDomains((prevDomains) =>
          prevDomains.map((domain) => (domain.id === domainId ? { ...domain, ...response.data } : domain))
        );
      }
    }

    setDomainToUpdate(null);
    scrollToTop();
  };

  const handleVerifyDomain = async (domainId: string) => {
    setDomainToUpdate({ id: domainId, action: "verifying" });

    const [response, error] = await verifyDomainReq.makeRequest({ id: domainId });

    if (error) {
      setErrorMessage(error.message ?? "Failed to verify domain");
    } else {
      if (response.data?.verified) {
        setSuccessMessage("Domain verified successfully");
      } else {
        setErrorMessage("Domain verification failed, please ensure you've added the A record to your DNS settings");
      }
      // Update the domain in the list with the response data
      if (response.data) {
        setDomains((prevDomains) =>
          prevDomains.map((domain) =>
            domain.id === domainId
              ? {
                  ...domain,
                  verified: response.data.verified,
                  certificate_issued: response.data.certificate_generated,
                }
              : domain
          )
        );
      }
    }

    setDomainToUpdate(null);
    scrollToTop();
  };

  const scrollToTop = () => {
    const element = document.querySelector(".store-config");
    if (element) {
      element.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  if (!canManageCustomDomains) {
    return (
      <ErrorBox
        title="Upgrade required"
        message="Please upgrade to business plus plan to use custom domains on your store"
      >
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  return (
    <>
      <div className="mb-8 md:mb-10">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-orange-500 text-white m-auto flex items-center justify-center">
          {/* prettier-ignore */}
          <svg width="50%" viewBox="0 0 24 24" fill="none">
            <path d="M7.64859 20.9098C7.61859 20.9098 7.57859 20.9298 7.54859 20.9298C5.60859 19.9698 4.02859 18.3798 3.05859 16.4398C3.05859 16.4098 3.07859 16.3698 3.07859 16.3398C4.29859 16.6998 5.55859 16.9698 6.80859 17.1798C7.02859 18.4398 7.28859 19.6898 7.64859 20.9098Z" fill="currentColor"/>
            <path d="M20.9391 16.4498C19.9491 18.4398 18.2991 20.0498 16.2891 21.0198C16.6691 19.7498 16.9891 18.4698 17.1991 17.1798C18.4591 16.9698 19.6991 16.6998 20.9191 16.3398C20.9091 16.3798 20.9391 16.4198 20.9391 16.4498Z" fill="currentColor"/>
            <path d="M21.0191 7.71047C19.7591 7.33047 18.4891 7.02047 17.1991 6.80047C16.9891 5.51047 16.6791 4.23047 16.2891 2.98047C18.3591 3.97047 20.0291 5.64047 21.0191 7.71047Z" fill="currentColor"/>
            <path d="M7.65047 3.08859C7.29047 4.30859 7.03047 5.54859 6.82047 6.80859C5.53047 7.00859 4.25047 7.32859 2.98047 7.70859C3.95047 5.69859 5.56047 4.04859 7.55047 3.05859C7.58047 3.05859 7.62047 3.08859 7.65047 3.08859Z" fill="currentColor"/>
            <path d="M15.4917 6.59C13.1717 6.33 10.8317 6.33 8.51172 6.59C8.76172 5.22 9.08172 3.85 9.53172 2.53C9.55172 2.45 9.54172 2.39 9.55172 2.31C10.3417 2.12 11.1517 2 12.0017 2C12.8417 2 13.6617 2.12 14.4417 2.31C14.4517 2.39 14.4517 2.45 14.4717 2.53C14.9217 3.86 15.2417 5.22 15.4917 6.59Z" fill="currentColor"/>
            <path d="M6.59 15.4917C5.21 15.2417 3.85 14.9217 2.53 14.4717C2.45 14.4517 2.39 14.4617 2.31 14.4517C2.12 13.6617 2 12.8517 2 12.0017C2 11.1617 2.12 10.3417 2.31 9.56172C2.39 9.55172 2.45 9.55172 2.53 9.53172C3.86 9.09172 5.21 8.76172 6.59 8.51172C6.34 10.8317 6.34 13.1717 6.59 15.4917Z" fill="currentColor"/>
            <path d="M22.0002 12.0017C22.0002 12.8517 21.8802 13.6617 21.6902 14.4517C21.6102 14.4617 21.5502 14.4517 21.4702 14.4717C20.1402 14.9117 18.7802 15.2417 17.4102 15.4917C17.6702 13.1717 17.6702 10.8317 17.4102 8.51172C18.7802 8.76172 20.1502 9.08172 21.4702 9.53172C21.5502 9.55172 21.6102 9.56172 21.6902 9.56172C21.8802 10.3517 22.0002 11.1617 22.0002 12.0017Z" fill="currentColor"/>
            <path d="M15.4917 17.4102C15.2417 18.7902 14.9217 20.1502 14.4717 21.4702C14.4517 21.5502 14.4517 21.6102 14.4417 21.6902C13.6617 21.8802 12.8417 22.0002 12.0017 22.0002C11.1517 22.0002 10.3417 21.8802 9.55172 21.6902C9.54172 21.6102 9.55172 21.5502 9.53172 21.4702C9.09172 20.1402 8.76172 18.7902 8.51172 17.4102C9.67172 17.5402 10.8317 17.6302 12.0017 17.6302C13.1717 17.6302 14.3417 17.5402 15.4917 17.4102Z" fill="currentColor"/>
            <path d="M15.7633 15.7633C13.2622 16.0789 10.7378 16.0789 8.23667 15.7633C7.92111 13.2622 7.92111 10.7378 8.23667 8.23667C10.7378 7.92111 13.2622 7.92111 15.7633 8.23667C16.0789 10.7378 16.0789 13.2622 15.7633 15.7633Z" fill="currentColor"/>
          </svg>
        </figure>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3 !leading-tight">
          Custom Domains
        </h2>
        <span className="block text-center text-1xs text-grey-subtext mt-2">
          Make your store look more professional by <br /> connecting your own domain — or buy a new one.
        </span>
      </div>

      <div className="space-y-6">
        <ErrorLabel error={errorMessage} />
        <SuccessLabel message={successMessage} />

        <div className="bg-grey-fields-200 rounded-10 border border-grey-border border-opacity-50">
          <div className="p-4 border-b border-grey-border border-opacity-50">
            <h3 className="text-base font-bold">Your Domains</h3>
          </div>

          <div className="p-4">
            <p className="text-sm mb-4 text-black-muted">
              All your domains, buy a new one, or connect an existing domain you already own.
            </p>

            <div className="flex gap-3.75">
              <AppBtn
                type="submit"
                color="primary"
                size="md"
                href="/my-store/custom-domains/purchase"
                className="px-4 text-sm cursor-pointer"
              >
                Buy a Domain
              </AppBtn>
              <AppBtn
                type="submit"
                color="white"
                size="md"
                href="/my-store/custom-domains/connect"
                className="px-4 text-sm cursor-pointer"
              >
                Connect Domain
              </AppBtn>
            </div>
            {getDomainsReq.isLoading ? (
              <div className="flex flex-col items-center py-5">
                <div className="spinner spinner--md text-primary-500"></div>
                <span className="text-sm text-black-placeholder mt-2 inline-block font-medium">Loading Domains</span>
              </div>
            ) : domains.length === 0 ? (
              <div className="flex flex-col items-center border-t border-grey-border border-opacity-50 mt-3.75 pt-5">
                <div className="flex h-16 w-16 rounded-full bg-white shadow-card items-center justify-center">
                  {/* prettier-ignore */}
                  <svg width="50%" viewBox="0 0 24 24" fill="none">
                    <path opacity="0.4" d="M7.65055 20.9098C7.62055 20.9098 7.58055 20.9298 7.55055 20.9298C5.61055 19.9698 4.03055 18.3798 3.06055 16.4398C3.06055 16.4098 3.08055 16.3698 3.08055 16.3398C4.30055 16.6998 5.56055 16.9698 6.81055 17.1798C7.03055 18.4398 7.29055 19.6898 7.65055 20.9098Z" fill="#AAAAAA"/>
                    <path opacity="0.4" d="M20.9391 16.4498C19.9491 18.4398 18.2991 20.0498 16.2891 21.0198C16.6691 19.7498 16.9891 18.4698 17.1991 17.1798C18.4591 16.9698 19.6991 16.6998 20.9191 16.3398C20.9091 16.3798 20.9391 16.4198 20.9391 16.4498Z" fill="#AAAAAA"/>
                    <path opacity="0.4" d="M21.0191 7.70998C19.7591 7.32998 18.4891 7.01998 17.1991 6.79998C16.9891 5.50998 16.6791 4.22998 16.2891 2.97998C18.3591 3.96998 20.0291 5.63998 21.0191 7.70998Z" fill="#AAAAAA"/>
                    <path opacity="0.4" d="M7.65047 3.09006C7.29047 4.31006 7.03047 5.55006 6.82047 6.81006C5.53047 7.01006 4.25047 7.33006 2.98047 7.71006C3.95047 5.70006 5.56047 4.05006 7.55047 3.06006C7.58047 3.06006 7.62047 3.09006 7.65047 3.09006Z" fill="#AAAAAA"/>
                    <path d="M15.4898 6.59C13.1698 6.33 10.8298 6.33 8.50977 6.59C8.75977 5.22 9.07977 3.85 9.52977 2.53C9.54977 2.45 9.53977 2.39 9.54977 2.31C10.3398 2.12 11.1498 2 11.9998 2C12.8398 2 13.6598 2.12 14.4398 2.31C14.4498 2.39 14.4498 2.45 14.4698 2.53C14.9198 3.86 15.2398 5.22 15.4898 6.59Z" fill="#AAAAAA"/>
                    <path d="M6.59 15.4898C5.21 15.2398 3.85 14.9198 2.53 14.4698C2.45 14.4498 2.39 14.4598 2.31 14.4498C2.12 13.6598 2 12.8498 2 11.9998C2 11.1598 2.12 10.3398 2.31 9.55977C2.39 9.54977 2.45 9.54977 2.53 9.52977C3.86 9.08977 5.21 8.75977 6.59 8.50977C6.34 10.8298 6.34 13.1698 6.59 15.4898Z" fill="#AAAAAA"/>
                    <path d="M22.0002 11.9998C22.0002 12.8498 21.8802 13.6598 21.6902 14.4498C21.6102 14.4598 21.5502 14.4498 21.4702 14.4698C20.1402 14.9098 18.7802 15.2398 17.4102 15.4898C17.6702 13.1698 17.6702 10.8298 17.4102 8.50977C18.7802 8.75977 20.1502 9.07977 21.4702 9.52977C21.5502 9.54977 21.6102 9.55977 21.6902 9.55977C21.8802 10.3498 22.0002 11.1598 22.0002 11.9998Z" fill="#AAAAAA"/>
                    <path d="M15.4898 17.4102C15.2398 18.7902 14.9198 20.1502 14.4698 21.4702C14.4498 21.5502 14.4498 21.6102 14.4398 21.6902C13.6598 21.8802 12.8398 22.0002 11.9998 22.0002C11.1498 22.0002 10.3398 21.8802 9.54977 21.6902C9.53977 21.6102 9.54977 21.5502 9.52977 21.4702C9.08977 20.1402 8.75977 18.7902 8.50977 17.4102C9.66977 17.5402 10.8298 17.6302 11.9998 17.6302C13.1698 17.6302 14.3398 17.5402 15.4898 17.4102Z" fill="#AAAAAA"/>
                    <path d="M15.7633 15.7633C13.2622 16.0789 10.7378 16.0789 8.23667 15.7633C7.92111 13.2622 7.92111 10.7378 8.23667 8.23667C10.7378 7.92111 13.2622 7.92111 15.7633 8.23667C16.0789 10.7378 16.0789 13.2622 15.7633 15.7633Z" fill="#AAAAAA"/>
                  </svg>
                </div>
                <div className="text-center py-4 text-gray-500 text-sm ">No custom domains added yet.</div>
              </div>
            ) : (
              <div className="flex flex-col gap-y-3 mt-3.75">
                {domains.map((domainData, index) => (
                  <div
                    className="bg-white border border-grey-border border-opacity-50 py-3 px-3 rounded-10 flex items-center justify-between"
                    key={index}
                  >
                    <div className="flex flex-col gap-2">
                      <div className="text-xs text-black-muted">
                        <span className="font-bold inline-block whitespace-nowrap overflow-ellipsis overflow-hidden w-full">
                          {domainData.domain}
                        </span>
                        <br />
                        <span className="flex">
                          {domainData.verified ? (
                            <>
                              <CheckIcon className="text-green-600 mr-1" />
                              <span className="text-green-600">Verified</span>
                            </>
                          ) : (
                            <>
                              <WarningIcon className="text-yellow-600 mr-1" />
                              <span className="text-yellow-600">Pending verification</span>
                            </>
                          )}
                        </span>
                      </div>
                      <div className="text-xs text-black-muted flex items-center gap-1">
                        {domainData.verified && (
                          <span>
                            {" "}
                            {domainData.verified && domainData.certificate_issued ? "SSL Active" : "SSL Pending"}
                          </span>
                        )}
                        {domainData.verified && <span>•</span>}
                        <span>{new Date(domainData.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex gap-1 flex-end h-full items-center self-end">
                      {!domainData.verified && (
                        <AppBtn
                          type="button"
                          size="sm"
                          color="neutral"
                          onClick={() => handleVerifyDomain(domainData.id)}
                          disabled={!!domainToUpdate?.id}
                          className="px-4 text-sm ml-auto"
                        >
                          <span className="text-xs">
                            {domainToUpdate?.id === domainData.id && domainToUpdate?.action === "verifying"
                              ? "Verifying..."
                              : "Verify Domain"}
                          </span>
                        </AppBtn>
                      )}
                      {domainData.verified && !domainData.certificate_issued && (
                        <button
                          onClick={() => handleGenerateSSL(domainData.id)}
                          disabled={!!domainToUpdate?.id}
                          className="mr-2 px-2 py-1 text-xs bg-white border border-green-600 text-green-600 rounded hover:bg-green-50 min-w-[90px]"
                        >
                          {domainToUpdate?.id === domainData.id && domainToUpdate?.action === "generatingSSL"
                            ? "Generating..."
                            : "Generate SSL"}
                        </button>
                      )}
                      <button
                        onClick={() => openDeleteModal(domainData.id, domainData.domain)}
                        disabled={domainToUpdate?.id === domainData.id && domainToUpdate?.action === "removing"}
                        className="text-accent-red-500 p-2 ml-[6px] bg-grey-fields-200 rounded-full hover:text-red-600"
                        title="Remove domain"
                      >
                        <DeleteIcon />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <Portal>
          <ConfirmDomainDelete
            show={modals.delete_domain.show}
            toggle={() => toggleModal("delete_domain")}
            domain={domainToDelete}
            removeDomainReq={removeDomainReq}
            removeDomain={handleRemoveDomain}
          />
        </Portal>
      </div>
    </>
  );
};

// SVG Icons
const InfoIcon = () =>
  // prettier-ignore
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="12" y1="16" x2="12" y2="12"></line>
    <line x1="12" y1="8" x2="12.01" y2="8"></line>
  </svg>;

const CheckIcon = ({ size = 14, className = "" }) =>
  // prettier-ignore
  <svg width={size} height={size} viewBox="0 0 13 13" fill="none" className={className}>
    <path d="M6.5 11.8828C9.25 11.8828 11.5 9.63281 11.5 6.88281C11.5 4.13281 9.25 1.88281 6.5 1.88281C3.75 1.88281 1.5 4.13281 1.5 6.88281C1.5 9.63281 3.75 11.8828 6.5 11.8828Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4.375 6.88277L5.79 8.29777L8.625 5.46777" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>;

const WarningIcon = ({ size = 14, className = "" }) =>
  // prettier-ignore
  <svg width={size} height={size} viewBox="0 0 13 13" fill="none" className={className}   >
    <path d="M6.5 5.38281V7.88281" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.50021 11.5875H3.47021C1.73521 11.5875 1.01021 10.3475 1.85021 8.83246L3.41021 6.02246L4.88021 3.38246C5.77021 1.77746 7.23021 1.77746 8.12021 3.38246L9.59021 6.02746L11.1502 8.83746C11.9902 10.3525 11602 11.5925 9.53021 11.5925H6.50021V11.5875Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.49707 9.38281H6.50156" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
  </svg>;

const DeleteIcon = ({ size = 20, className = "" }) =>
  // prettier-ignore
  <svg width={size} height={size} viewBox="0 0 15 15" fill="none" className={className}>
    <path d="M12.75 3.87077C10.8075 3.67827 8.85333 3.5791 6.905 3.5791C5.75 3.5791 4.595 3.63743 3.44 3.7541L2.25 3.87077" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M5.45825 3.2823L5.58659 2.51814C5.67992 1.96397 5.74992 1.5498 6.73575 1.5498H8.26409C9.24992 1.5498 9.32575 1.9873 9.41325 2.52397L9.54159 3.2823" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.4958 5.71484L11.1167 11.589C11.0525 12.5048 11 13.2165 9.37248 13.2165H5.62748C3.99998 13.2165 3.94748 12.5048 3.88332 11.589L3.50415 5.71484" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.52588 10.0078H8.46838" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.04175 7.6748H8.95841" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" />
  </svg>;

export default CustomDomains;
