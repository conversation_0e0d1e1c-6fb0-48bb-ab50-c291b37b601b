import React, { useState } from "react";
import { formatDate } from "../../../assets/js/utils/functions";
import authContext from "../../../contexts/auth-context";
import Badge from "../../ui/badge";
import { AppBtn } from "../../ui/buttons";
import CardWithTitle from "../../ui/card-with-title";
import CancelSubscriptionModal from "@/components/subscriptions/modals/cancel-subscription";
import { useModals } from "@/components/hooks/useModals";
import { useRequest } from "@/api/utils";
import { CancelSubscriptionToggle } from "@/api";
import { toast } from "@/components/ui/toast";

const StoreSubscriptionInfo = () => {
  const { subscription, toggleAppModals, setSubscription } = authContext.useContainer();
  const { modals, toggleModal } = useModals(["cancel"]);
  const [isCanceling, setIsCanceling] = useState(false);

  const toggleSubscriptionReq = useRequest(CancelSubscriptionToggle);

  const toggleSubscription = async () => {
    const isCancelling = !subscription.cancel_at_period_end;
    const [res, err] = await toggleSubscriptionReq.makeRequest({ cancel_at_period_end: isCanceling });
    if (res) {
      // Update the subscription data
      setSubscription({ ...subscription, cancel_at_period_end: !subscription.cancel_at_period_end });

      if (isCancelling) {
        toggleModal("cancel");
      } else {
        toast.success({ message: "Subscription Re-activated", title: "Updated subscription" });
      }
    }
  };

  const subscriptionDetails = getSubscriptionDetails(subscription);

  // Handler function to toggle subscription cancellation
  const cancelSubscription = () => {
    setIsCanceling(!subscription.cancel_at_period_end);
    toggleModal("cancel");
  };

  return (
    <div className="w-full mx-auto max-w-[420px]">
      <div className="pt-0 mb-8 md:mb-10">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-green-500 m-auto flex items-center justify-center">
          {/* prettier-ignore */}
          <svg className="w-[30px] sm:w-8" viewBox="0 0 31 30" fill="none">
            <path d="M24.25 18.75C21.4875 18.75 19.25 20.9875 19.25 23.75C19.25 24.6875 19.5125 25.575 19.975 26.325C20.8375 27.775 22.425 28.75 24.25 28.75C26.075 28.75 27.6625 27.775 28.525 26.325C28.9875 25.575 29.25 24.6875 29.25 23.75C29.25 20.9875 27.0125 18.75 24.25 18.75ZM26.8375 23.2125L24.175 25.675C24 25.8375 23.7625 25.925 23.5375 25.925C23.3 25.925 23.0625 25.8375 22.875 25.65L21.6375 24.4125C21.275 24.05 21.275 23.45 21.6375 23.0875C22 22.725 22.6 22.725 22.9625 23.0875L23.5625 23.6875L25.5625 21.8375C25.9375 21.4875 26.5375 21.5125 26.8875 21.8875C27.2375 22.2625 27.2125 22.85 26.8375 23.2125Z" fill="white"/>
            <path d="M28 9.4375V10C28 10.6875 27.4375 11.25 26.75 11.25H4.25C3.5625 11.25 3 10.6875 3 10V9.425C3 6.5625 5.3125 4.25 8.175 4.25H22.8125C25.675 4.25 28 6.575 28 9.4375Z" fill="white"/>
            <path d="M3 14.3748V20.5748C3 23.4373 5.3125 25.7498 8.175 25.7498H16C16.725 25.7498 17.35 25.1373 17.2875 24.4123C17.1125 22.4998 17.725 20.4248 19.425 18.7748C20.125 18.0873 20.9875 17.5623 21.925 17.2623C23.4875 16.7623 25 16.8248 26.3375 17.2748C27.15 17.5498 28 16.9623 28 16.0998V14.3623C28 13.6748 27.4375 13.1123 26.75 13.1123H4.25C3.5625 13.1248 3 13.6873 3 14.3748ZM10.5 21.5623H8C7.4875 21.5623 7.0625 21.1373 7.0625 20.6248C7.0625 20.1123 7.4875 19.6873 8 19.6873H10.5C11.0125 19.6873 11.4375 20.1123 11.4375 20.6248C11.4375 21.1373 11.0125 21.5623 10.5 21.5623Z" fill="white"/>
          </svg>
        </figure>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          {subscription?.plan?.name} Plan
        </h2>
      </div>
      <dl className="flex flex-col space-y-5">
        {subscriptionDetails.map((sub, index) => {
          return (
            <div key={index} className="flex w-full justify-between items-center">
              <dt className="flex items-center">
                <div className="p-1.5 text-black-placeholder rounded-full bg-grey-fields-200">{sub.icon}</div>
                <span className="text-black-muted ml-2 text-sm">{sub.label} </span>
              </dt>
              <dd className="text-sm text-black-secondary">{sub.value.custom ?? sub.value.text}</dd>
            </div>
          );
        })}
      </dl>

      <div className="flex items-center flex-col sm:flex-row space-y-3.5 sm:space-y-0 sm:space-x-3.5 w-full">
        {subscription?.status === "INACTIVE" && (
          <div className="flex-1 w-full mt-10">
            <AppBtn color="neutral" isBlock onClick={() => toggleAppModals("renewal")} size="lg">
              Renew Subscription
            </AppBtn>
          </div>
        )}
        <div className="flex w-full space-x-3">
          {
            /* Add the Cancel/Resume Subscription button when subscription is active */
            subscription?.status === "ACTIVE" && (
              <div className="flex-1 w-full mt-10">
                <AppBtn
                  isBlock
                  className={`${subscription?.cancel_at_period_end ? "text-accent-green-500" : "text-accent-red-500"}`}
                  onClick={subscription?.cancel_at_period_end ? toggleSubscription : cancelSubscription}
                  size="lg"
                  color="neutral"
                >
                  {subscription?.cancel_at_period_end ? "Resume Subscription" : "Cancel Subscription"}
                </AppBtn>
              </div>
            )
          }
          <AppBtn className="flex-1 mt-10" href="/my-store/change-plan" size="lg">
            Change Plan
          </AppBtn>
        </div>
      </div>
      {/* Include the CancelSubscriptionModal */}
      <CancelSubscriptionModal
        subscription={subscription}
        setSubscription={setSubscription}
        show={modals.cancel.show}
        toggle={() => toggleModal("cancel")}
        isCanceling={isCanceling}
        request={toggleSubscriptionReq}
        toggleSubscription={toggleSubscription}
      />
    </div>
  );
};

const SUB_STATUS_COLORS = {
  ACTIVE: "green",
  INACTIVE: "red",
  PENDING: "orange",
};

export default StoreSubscriptionInfo;

const getSubscriptionDetails = (subscription) => [
  {
    label: "Status",
    icon:
      // prettier-ignore
      <svg className="w-4" viewBox="0 0 16 15" fill="none">
    <path d="M2.03125 9.35645C2.7 11.5064 4.5 13.1627 6.7375 13.6189" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M1.78125 6.8625C2.1 3.70625 4.7625 1.25 8 1.25C11.2375 1.25 13.9 3.7125 14.2187 6.8625" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M9.25635 13.6252C11.4876 13.1689 13.2813 11.5314 13.9626 9.3877" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>,
    value: {
      text: null,
      custom: <Badge text={subscription?.status.toLowerCase()} color={SUB_STATUS_COLORS[subscription?.status]} />,
    },
  },
  {
    label: "Interval",
    icon:
      // prettier-ignore
      <svg className="w-4" viewBox="0 0 16 15" fill="none">
      <path d="M14.25 7.5C14.25 10.95 11.45 13.75 8 13.75C4.55 13.75 1.75 10.95 1.75 7.5C1.75 4.05 4.55 1.25 8 1.25C11.45 1.25 14.25 4.05 14.25 7.5Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.3188 9.4876L8.3813 8.33135C8.0438 8.13135 7.7688 7.6501 7.7688 7.25635V4.69385" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
    value: {
      text: subscription?.plan?.interval_text,
      custom: null,
    },
  },
  {
    label: "Last payment date",
    icon:
      // prettier-ignore
      <svg className="w-4" viewBox="0 0 16 15" fill="none">
      <path d="M5.5 1.25V3.125" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.5 1.25V3.125" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M2.6875 5.68115H13.3125" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M14.25 11.875C14.25 12.3438 14.1187 12.7875 13.8875 13.1625C13.4562 13.8875 12.6625 14.375 11.75 14.375C11.1188 14.375 10.5438 14.1437 10.1063 13.75C9.9125 13.5875 9.74375 13.3875 9.6125 13.1625C9.38125 12.7875 9.25 12.3438 9.25 11.875C9.25 10.4937 10.3687 9.375 11.75 9.375C12.5 9.375 13.1687 9.70624 13.625 10.225C14.0125 10.6687 14.25 11.2438 14.25 11.875Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.7749 11.8752L11.3937 12.4939L12.7249 11.2627" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M13.625 5.3125V10.225C13.1687 9.70624 12.5 9.375 11.75 9.375C10.3687 9.375 9.25 10.4937 9.25 11.875C9.25 12.3438 9.38125 12.7875 9.6125 13.1625C9.74375 13.3875 9.9125 13.5875 10.1063 13.75H5.5C3.3125 13.75 2.375 12.5 2.375 10.625V5.3125C2.375 3.4375 3.3125 2.1875 5.5 2.1875H10.5C12.6875 2.1875 13.625 3.4375 13.625 5.3125Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M7.99707 8.5625H8.00268" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M5.68407 8.5625H5.68968" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M5.68407 10.4375H5.68968" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
    value: {
      text: formatDate(subscription?.last_payment_date, { month: "short", day: "numeric", year: "numeric" }),
      custom: null,
    },
  },
  {
    label: "Next payment date",
    icon:
      // prettier-ignore
      <svg className="w-4" viewBox="0 0 16 15" fill="none">
      <path d="M5.5 1.25V3.125" stroke="currentColor" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.5 1.25V3.125" stroke="currentColor" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M2.6875 5.68115H13.3125" stroke="currentColor" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M13.625 5.3125V10.625C13.625 12.5 12.6875 13.75 10.5 13.75H5.5C3.3125 13.75 2.375 12.5 2.375 10.625V5.3125C2.375 3.4375 3.3125 2.1875 5.5 2.1875H10.5C12.6875 2.1875 13.625 3.4375 13.625 5.3125Z" stroke="currentColor" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.3091 8.5625H10.3147" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.3091 10.4375H10.3147" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M7.99706 8.5625H8.00267" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M7.99706 10.4375H8.00267" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M5.68407 8.5625H5.68968" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M5.68407 10.4375H5.68968" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
    value: {
      text: formatDate(subscription?.next_payment_date, { month: "short", day: "numeric", year: "numeric" }),
      custom: null,
    },
  },
  {
    label: "Payment method",
    icon:
      // prettier-ignore
      <svg className="w-4" viewBox="0 0 16 15" fill="none">
      <path d="M1.75 5.31543H14.25" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M4.25 10.3154H5.5" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M7.0625 10.3154H9.5625" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M4.525 2.19043H11.4687C13.6937 2.19043 14.25 2.74043 14.25 4.93418V10.0654C14.25 12.2592 13.6937 12.8092 11.475 12.8092H4.525C2.30625 12.8154 1.75 12.2654 1.75 10.0717V4.93418C1.75 2.74043 2.30625 2.19043 4.525 2.19043Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
    value: {
      text: null,
      custom: (
        <div className="flex items-center">
          <span>PAYSTACK</span>
        </div>
      ),
    },
  },
];
