import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { VIEW_MODES } from "../../../assets/js/utils/constants";
import { getFieldvalues, toBase64, trimArray } from "../../../assets/js/utils/functions";
import ErrorLabel from "../../ui/error-label";
import { SelectDropdown } from "../../ui/form-elements";
import Radio from "../../ui/form-elements/radio";
import SuccessLabel from "../../ui/success-label";
import { FormikProps } from "formik";
import { StoreConfiguration, StorefrontVersion, StoreInterface, SubscriptionInterface } from "@/assets/interfaces";
import { AppBtn } from "@/components/ui/buttons";
import Portal from "@/components/portal";
import { useModals } from "@/components/hooks/useModals";
import { useLocalObject } from "@/components/hooks/useLocalState";
import { SCOPES, actionIsAllowed } from "@/assets/js/utils/permissions";
import classNames from "classnames";
import router from "next/router";
import dynamic from "next/dynamic";
import Toggle from "@/components/ui/toggle";
import { Notepad2, RowVertical, SliderHorizontal } from "iconsax-react";
import { toast } from "@/components/ui/toast";

const ColorPickerModal = dynamic(() => import("./color-picker-modal"), {
  ssr: false,
});

interface Props {
  form: FormikProps<StoreConfiguration>;
  error: any;
  response: any;
  subscription: SubscriptionInterface;
  store: StoreInterface;
}

const CATLOG_PRIMARY_COLOR = "#332089";
const defaultSuggestions = [
  "#744253",
  "#54428E",
  "#7A306C",
  "#E86252",
  "#6279B8",
  "#A18276",
  "#157A6E",
  "#3E363F",
  "#772E6C",
  "#DE3C4B",
  "#001514",
  "#26547C",
  "#086470",
  "#363020",
  "#EF476F",
  "#9B7D2F",
  "#70877F",
];

export const Customizations: React.FC<Props> = ({ form, error, response, subscription, store }) => {
  const [modesSelectError, setModesSelectError] = useState("");
  const { modals, toggleModal } = useModals(["color_picker"]);
  const [colorHistory, setColorhistory] = useLocalObject<string[]>("store-color-history");

  const canCustomizeColor = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_CUSTOMIZE_COLORS,
  });

  const selectedColor = form?.values?.color;
  const initialSuggestions = useMemo(
    () =>
      trimArray(
        Array.from(
          new Set([
            ...(selectedColor ? [selectedColor] : []),
            CATLOG_PRIMARY_COLOR,
            ...(colorHistory ?? []),
            ...defaultSuggestions,
          ])
        ),
        18
      ),
    []
  );

  // the goal here is to update the suggestions with new colors selected from the pickers without making updates if the color is already in the suggestions array
  const getUniqueSuggestions = useCallback(() => {
    if (initialSuggestions.includes(selectedColor) || !selectedColor) {
      return initialSuggestions;
    } else {
      return trimArray([selectedColor, ...initialSuggestions], 18);
    }
  }, [initialSuggestions, selectedColor]);

  const toggleMode = (mode: string) => {
    if (Object.keys(VIEW_MODES).every((m) => m === mode || !form.values.view_modes[m])) {
      setModesSelectError("You must select at least one mode");

      setTimeout(() => {
        setModesSelectError("");
      }, 2000);

      return;
    }
    if (form.values.view_modes.default === mode && form.values.view_modes[mode]) {
      //check if the currently toggled view mode is the default
      setModesSelectError("To remove this mode change the default mode");

      setTimeout(() => {
        setModesSelectError("");
      }, 2000);

      return;
    }

    form.setFieldValue(`view_modes.${mode}`, !form.values.view_modes[mode]);
  };

  const setColor = (selection?: string) => {
    if (!canCustomizeColor) return;

    form.setFieldValue("color", selection);
    if (selection) {
      setColorhistory(Array.from(new Set([selection, ...(colorHistory ?? [])])));
    }
  };

  const toggleTopSmallCategoryCards = (state: boolean) => {
    form.setFieldValue("small_top_category_cards", state);
    if (state) {
      form.setFieldValue("top_category_cards", false);
      form.setFieldValue("bottom_category_cards", false);
    }
  };

  const toggleTopCategoryCards = (state: boolean) => {
    form.setFieldValue("top_category_cards", state);
    if (state) {
      form.setFieldValue("small_top_category_cards", false);
      form.setFieldValue("bottom_category_cards", true);
    }
  };

  const toggleBottomCategoryCards = (state: boolean) => {
    if (!state && !form.values.small_top_category_cards) {
      toast.error({
        title: "Error",
        message: "You must enable either the small top category cards or the bottom category cards",
      });
      return;
    }

    form.setFieldValue("bottom_category_cards", state);
    if (state) {
      form.setFieldValue("small_top_category_cards", false);
    }
  };

  return (
    <>
      <div className="mb-7.5 md:mb-9">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-orange-500 m-auto flex items-center justify-center">
          {/* prettier-ignore */}
          <svg className="w-[50%] text-white" viewBox="0 0 24 24" fill="none">
            <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM7.67 5.5C7.67 5.09 8.01 4.75 8.42 4.75C8.83 4.75 9.17 5.09 9.17 5.5V9.4C9.17 9.81 8.83 10.15 8.42 10.15C8.01 10.15 7.67 9.81 7.67 9.4V5.5ZM9.52282 16.4313C9.31938 16.5216 9.17 16.7132 9.17 16.9358V18.5C9.17 18.91 8.83 19.25 8.42 19.25C8.01 19.25 7.67 18.91 7.67 18.5V16.9358C7.67 16.7132 7.5206 16.5216 7.31723 16.4311C6.36275 16.0064 5.7 15.058 5.7 13.95C5.7 12.45 6.92 11.22 8.42 11.22C9.92 11.22 11.15 12.44 11.15 13.95C11.15 15.0582 10.4791 16.0066 9.52282 16.4313ZM16.33 18.5C16.33 18.91 15.99 19.25 15.58 19.25C15.17 19.25 14.83 18.91 14.83 18.5V14.6C14.83 14.19 15.17 13.85 15.58 13.85C15.99 13.85 16.33 14.19 16.33 14.6V18.5ZM15.58 12.77C14.08 12.77 12.85 11.55 12.85 10.04C12.85 8.93185 13.5209 7.98342 14.4772 7.55873C14.6806 7.46839 14.83 7.27681 14.83 7.05421V5.5C14.83 5.09 15.17 4.75 15.58 4.75C15.99 4.75 16.33 5.09 16.33 5.5V7.06421C16.33 7.28681 16.4794 7.47835 16.6828 7.56885C17.6372 7.9936 18.3 8.94195 18.3 10.05C18.3 11.55 17.08 12.77 15.58 12.77Z" fill="currentColor"/>
          </svg>
          {/* <svg className="w-[30px] sm:w-8" viewBox="0 0 31 30" fill="none">
            <path d="M23.8373 2.5H21.4623C18.7373 2.5 17.2998 3.9375 17.2998 6.6625V9.0375C17.2998 11.7625 18.7373 13.2 21.4623 13.2H23.8373C26.5623 13.2 27.9998 11.7625 27.9998 9.0375V6.6625C27.9998 3.9375 26.5623 2.5 23.8373 2.5Z" fill="white"/>
            <path d="M9.55 16.7876H7.175C4.4375 16.7876 3 18.2251 3 20.9501V23.3251C3 26.0626 4.4375 27.5001 7.1625 27.5001H9.5375C12.2625 27.5001 13.7 26.0626 13.7 23.3376V20.9626C13.7125 18.2251 12.275 16.7876 9.55 16.7876Z" fill="white"/>
            <path d="M8.3625 13.225C11.3241 13.225 13.725 10.8241 13.725 7.8625C13.725 4.90087 11.3241 2.5 8.3625 2.5C5.40087 2.5 3 4.90087 3 7.8625C3 10.8241 5.40087 13.225 8.3625 13.225Z" fill="white"/>
            <path d="M22.6374 27.4999C25.599 27.4999 27.9999 25.099 27.9999 22.1374C27.9999 19.1758 25.599 16.7749 22.6374 16.7749C19.6758 16.7749 17.2749 19.1758 17.2749 22.1374C17.2749 25.099 19.6758 27.4999 22.6374 27.4999Z" fill="white"/>
          </svg> */}
        </figure>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          Customizations
        </h2>
        <span className="block text-center text-1xs text-grey-subtext mt-2">
          Customize the look and feel <br /> of your Catlog Store
        </span>
      </div>
      <ErrorLabel error={error?.message} />
      <SuccessLabel message={response ? "Store Updated Successfully!" : null} />

      <div className="bg-grey-fields-100 rounded-15 bg-opacity-70 divide-y divide-grey-border divide-opacity-50 mb-7.5">
        <div className="flex p-3.75 items-center justify-between">
          <h4 className="font-display text-black-secondary font-bold text-1sm sm:text-base">Brand Color</h4>
          <div className="p-1 bg-white rounded-md shadow-pill border border-grey-divider">
            <div
              className="h-4 w-4 rounded-[3px]"
              style={{ background: selectedColor ? selectedColor : CATLOG_PRIMARY_COLOR }}
            ></div>
          </div>
        </div>
        <div className="p-3.75">
          <div className="flex items-center justify-between">
            <span className="text-dark font-semibold text-1xs sm:text-sm block">Suggestions</span>
            <button className={`flex items-center text-primary-500`} onClick={() => setColor(null)} type="button">
              <span className="text-xs text-primary-500 font-semibold uppercase">RESET</span>
              {/* prettier-ignore */}
              <svg className="w-3.5 ml-1" viewBox="0 0 24 24" fill="none">
                <path d="M14.55 21.67C18.84 20.54 22 16.64 22 12C22 6.48 17.56 2 12 2C5.33 2 2 7.56 2 7.56M2 7.56V3M2 7.56H4.01H6.44" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2 12C2 17.52 6.48 22 12 22" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="3 3"/>
              </svg>
            </button>
          </div>
          <div className="grid grid-cols-7 sm:grid-cols-9 mt-2.5 gap-2">
            {getUniqueSuggestions().map((c, i) => (
              <div key={i}>
                <button
                  className={classNames(
                    "p-1 bg-white border border-grey-divider rounded-[7px] inline-block cursor-pointer",
                    { "opacity-40 cursor-not-allowed": !canCustomizeColor }
                  )}
                  type="button"
                  disabled={!canCustomizeColor}
                  onClick={() => setColor(c === CATLOG_PRIMARY_COLOR ? null : c)}
                >
                  <div className="h-5 w-5 rounded-[4px] relative overflow-hidden" style={{ background: c }}>
                    {(c === selectedColor || (!selectedColor && c === CATLOG_PRIMARY_COLOR)) && (
                      <div
                        className="h-full w-full flex items-center justify-center"
                        style={{ background: "rgba(0,0,0,0.1)" }}
                      >
                        {/* prettier-ignore */}
                        <svg width="10" viewBox="0 0 32 35" fill="none" className="-mt-0.5 text-white">
                          <path d="M4.18766 22.4105L13.7894 29.9576L28.4843 11.882" stroke="currentColor" strokeWidth="5" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      </div>
                    )}
                  </div>
                </button>
              </div>
            ))}
          </div>
          <div className="flex items-center justify-between mt-3.5">
            {!canCustomizeColor && (
              <button
                className={`flex items-center text-primary-500`}
                onClick={() =>
                  router.push(
                    `/my-store/change-plan?message=${toBase64(
                      `You need to be on a paid plan to customize your store color`
                    )}`
                  )
                }
                type="button"
              >
                <span className="text-xs text-primary-500 font-semibold uppercase">
                  Upgrade subscription to customize
                </span>
                {/* prettier-ignore */}
                <svg className="w-4" viewBox="0 0 15 16" fill="none">
                  <path d="M3.96484 11.5355L11.0359 4.46446" stroke="#332089" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
                  <path d="M11.0352 11.5355L11.0352 4.46446L3.96409 4.46445" stroke="#332089" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </button>
            )}

            {canCustomizeColor && (
              <AppBtn
                color="white"
                size="sm"
                className="!text-1xs font-semibold !h-8 !px-3 border border-grey-divider ml-auto"
                onClick={() => toggleModal("color_picker")}
              >
                Manually Pick Color
              </AppBtn>
            )}
          </div>
        </div>
      </div>

      {store?.flags?.storefront_version === StorefrontVersion.V1 && (
        <div>
          <h2 className="font-bold text-base sm:text-[17px] lg:text-lg text-black mb-3.75">Store View Mode</h2>
          <ul className="bg-grey-fields-100 rounded-20 bg-opacity-70 divide-y divide-grey-border divide-opacity-50">
            {Object.keys(VIEW_MODES).map((key, index) => (
              <li
                className={`flex justify-between items-center p-4.5 w-full cursor-pointer ${
                  form?.values?.view_modes[key] ? "" : ""
                }`}
                onClick={(e) => toggleMode(key)}
                key={index}
              >
                <div className="flex items-center" key={index}>
                  <img
                    className={`h-5 ${key === "horizontal" ? "w-5" : "w-4.5"}`}
                    src={`/images/view-modes-illustration/${key}.svg`}
                    alt=""
                  />
                  <span
                    className={`inline-block text-1xs sm:text-sm ml-2.5 font-medium ${
                      form.values.view_modes[key] ? "text-black-secondary" : "text-dark"
                    }`}
                  >
                    {VIEW_MODES[key]}
                  </span>
                </div>
                <Radio
                  name="view-mode"
                  value={key}
                  chosen={form.values.view_modes[key] ? key : ""}
                  onChange={toggleMode}
                />
              </li>
            ))}
          </ul>
          {modesSelectError && <span className="text-xs text-accent-red-500 block mb-4 mt-2">{modesSelectError}</span>}
          <div className="mt-5">
            <SelectDropdown
              label="Select default mode"
              options={Object.keys(VIEW_MODES)
                .filter((m) => form.values.view_modes[m])
                .map((m) => ({ value: m, text: VIEW_MODES[m] }))}
              {...getFieldvalues("view_modes.default", form)}
            />
          </div>
        </div>
      )}

      {store?.flags?.storefront_version === StorefrontVersion.V2 && (
        <div className="border-t border-grey-divider py-5">
          <h2 className="font-bold text-base sm:text-[17px] lg:text-lg text-black mb-3.75">Store Categories Display</h2>
          <div className="flex justify-between w-full">
            <div className="flex gap-2.5 sm:gap-3.75 ">
              <div className="rounded-full h-[fit-content] p-2 sm:p-2.5 text-accent-yellow-500  bg-accent-yellow-500 bg-opacity-10">
                <Notepad2 variant="Bold" size={18} />
              </div>
              <div>
                <h2 className="font-bold text-sm sm:text-1sm lg:text-base text-black-secondary">Top Catgeory Cards</h2>
                <p className="text-xs sm:text-1xs text-black-muted mt-1">
                  3 Categories preview at the top of your store
                </p>
              </div>
            </div>
            <div className="pl-5 lg:pl-12">
              <Toggle
                intialState={form.values.top_category_cards ?? true}
                onChange={(state) => toggleTopCategoryCards(state)}
              />
            </div>
          </div>
          <div className="flex justify-between w-full mt-5">
            <div className="flex gap-2.5 sm:gap-3.75 ">
              <div className="rounded-full h-[fit-content] p-2 sm:p-2.5 text-accent-orange-500  bg-accent-orange-500 bg-opacity-10">
                <RowVertical variant="Bold" size={18} />
              </div>
              <div>
                <h2 className="font-bold text-sm sm:text-1sm lg:text-base text-black-secondary">
                  Small Top Catgeory Cards
                </h2>
                <p className="text-xs sm:text-1xs text-black-muted mt-1">
                  All categories preview at the top of your store
                </p>
              </div>
            </div>
            <div className="pl-5 lg:pl-12">
              <Toggle
                intialState={form.values.small_top_category_cards ?? false}
                onChange={(state) => toggleTopSmallCategoryCards(state)}
              />
            </div>
          </div>

          <div className="flex justify-between w-full mt-5">
            <div className="flex gap-2.5 sm:gap-3.75 ">
              <div className="rounded-full h-[fit-content] p-2 sm:p-2.5 text-accent-green-500  bg-accent-green-500 bg-opacity-10">
                <SliderHorizontal variant="Bold" size={18} />
              </div>
              <div>
                <h2 className="font-bold text-sm sm:text-1sm lg:text-base text-black-secondary">
                  Bottom Catgeory Cards
                </h2>
                <p className="text-xs sm:text-1xs text-black-muted mt-1">
                  Categories cards at the bottom of your store
                </p>
              </div>
            </div>
            <div className="pl-5 lg:pl-12">
              <Toggle
                intialState={form.values.bottom_category_cards ?? true}
                onChange={(state) => toggleBottomCategoryCards(state)}
              />
            </div>
          </div>
        </div>
      )}

      <Portal>
        <ColorPickerModal
          show={modals.color_picker.show}
          toggle={() => toggleModal("color_picker")}
          {...{ setColor, selectedColor, logo: store?.logo }}
        />
      </Portal>
    </>
  );
};
