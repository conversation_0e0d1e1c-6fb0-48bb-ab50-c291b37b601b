import { useFormik } from "formik";
import { useState } from "react";
import * as Yup from "yup";
import { UpdateStoreColorParams, UpdateStoreConfigParams } from "../../../api/interfaces/stores.interface";
import { RequestInterface, useRequest } from "../../../api/utils";
import { StoreConfiguration } from "../../../assets/interfaces";
import ProductConfiguration from "./products";
import SelectHours from "./select-hours";
import { StoreMessages } from "./store-messages";
import { Customizations } from "./customizations";
import { AppBtn } from "../../ui/buttons";
import authContext from "../../../contexts/auth-context";
import { Currencies } from "./currencies";
import { ChowbotSetupPageProps } from "@/pages/get-started/chowbot";
import ThirdPartyIntegration from "./third-party-integration";
import CustomDomains from "../custom-domains/custom-domains";

export interface ConfigurationForm extends StoreConfiguration {}

interface Props extends ChowbotSetupPageProps {
  activeTab: string;
  updateConfigReq: RequestInterface<UpdateStoreConfigParams>;
  updateStoreColorReq: RequestInterface<UpdateStoreColorParams>;
}

const ConfigSettings: React.FC<Props> = ({ activeTab, updateConfigReq, updateStoreColorReq, onComplete }) => {
  const { store, updateStore, subscription } = authContext.useContainer();
  const { isLoading, makeRequest, error, response, clearResponse } = updateConfigReq;
  const [hoursError, setHoursError] = useState("");

  const form = useFormik<StoreConfiguration>({
    initialValues: {
      ...store?.configuration,
      store_welcome_message:
        store?.configuration.store_welcome_message ?? "Hi there! 👋 What are you shopping for today?",
      hours: store?.configuration.hours ? store?.configuration.hours : {},
    },
    validationSchema,
    onSubmit: async (values) => {
      let { color, ...configuration } = values;

      if (store?.flags?.uses_chowbot && activeTab === "opening_&_closing_hours") {
        const notAllDaysSelected = Object.keys(values.hours).length < 7;

        if (notAllDaysSelected) {
          setHoursError("Please select hours for every week day");
          return;
        }
      }

      if (store?.configuration.color !== color) {
        await updateStoreColorReq.makeRequest({ id: store.id, color });
      }

      let valuesToSend = { id: store.id, configuration };
      const [response, error] = await makeRequest(valuesToSend);

      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors({ ...error.fields });
        }
      } else {
        onComplete?.();
        updateStore({ configuration: { ...response.data?.configuration, color } });
      }
      // scroll to top so response message shows
      const formEl = document.querySelector("#store-config-settings");
      formEl.scrollTo(0, 0);
    },
  });

  const tabComponents = {
    "opening_&_closing_hours": (
      <SelectHours
        {...{ error: hoursError ? { message: hoursError } : error, response }}
        hours={form.values.hours}
        setHours={(values: { [key: string]: string }) => form.setFieldValue("hours", values)}
      />
    ),
    customizations: <Customizations {...{ error, response, form, subscription, store }} />,
    store_messages: <StoreMessages {...{ error, response, form }}></StoreMessages>,
    products: <ProductConfiguration {...{ error, response, form }} />,
    currencies: <Currencies {...{ store, updateStore }} />,
    "third-party_integrations": <ThirdPartyIntegration {...{ error, response, form, subscription }} />,
    custom_domains: <CustomDomains />,
  };

  // These tabs have their own form handling and don't need the update button
  const standaloneComponents = ["currencies", "custom_domains"];

  return (
    <div className="pt-10 sm:pt-11.25 !pb-32" id="store-config-settings">
      {standaloneComponents.includes(activeTab) ? (
        <div className="w-full mx-auto max-w-[450px]">{tabComponents[activeTab]}</div>
      ) : (
        <form onSubmit={form.handleSubmit} className="w-full mx-auto max-w-[450px]">
          {tabComponents[activeTab]}
          <AppBtn isBlock className="mt-7.5" type="submit" disabled={isLoading} size="lg">
            {isLoading ? "Updating..." : "Update Configurations"}
          </AppBtn>
        </form>
      )}
    </div>
  );
};

export default ConfigSettings;

const validationSchema = Yup.object().shape({
  view_modes: Yup.object().shape({
    default: Yup.string().required("Please select a default mode"),
  }),
  fb_pixel: Yup.string().when("facebook_pixel_enabled", {
    is: true,
    then: Yup.string()
      .required("Facebook Pixel Code is required")
      .matches(/^\d{15,20}$/, "Facebook pixel ID should be 15 digits"),
    // .test("isValidPixelId", "Invalid Facebook Pixel code or ID", function (value) {
    //   if (!value) return false;
    //   const regex = /fbq\('init', '(\d+)'\)/;
    //   const match = value.match(regex);
    //   return match !== null || /^\d+$/.test(value);
    // }),
  }),
});
