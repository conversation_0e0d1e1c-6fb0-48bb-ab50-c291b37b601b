import { getFieldvalues } from "../../../assets/js/utils/functions";
import ErrorLabel from "../../ui/error-label";
import { TextArea } from "../../ui/form-elements";
import SuccessLabel from "../../ui/success-label";

interface Props {
  error: any;
  response: any;
  form: any;
}
export const StoreMessages: React.FC<Props> = ({ error, response, form }) => {
  return (
    <>
      <div className="mb-8 md:mb-10">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-green-500 m-auto flex items-center justify-center">
          {/* prettier-ignore */}
          <svg className="w-[30px] sm:w-8" viewBox="0 0 31 30" fill="none">
            <path d="M16.9875 7.5H8.9875C8.6625 7.5 8.35 7.5125 8.05 7.55C4.6875 7.8375 3 9.825 3 13.4875V18.4875C3 23.4875 5 24.475 8.9875 24.475H9.4875C9.7625 24.475 10.125 24.6625 10.2875 24.875L11.7875 26.875C12.45 27.7625 13.525 27.7625 14.1875 26.875L15.6875 24.875C15.875 24.625 16.175 24.475 16.4875 24.475H16.9875C20.65 24.475 22.6375 22.8 22.925 19.425C22.9625 19.125 22.975 18.8125 22.975 18.4875V13.4875C22.975 9.5 20.975 7.5 16.9875 7.5ZM8.625 17.5C7.925 17.5 7.375 16.9375 7.375 16.25C7.375 15.5625 7.9375 15 8.625 15C9.3125 15 9.875 15.5625 9.875 16.25C9.875 16.9375 9.3125 17.5 8.625 17.5ZM12.9875 17.5C12.2875 17.5 11.7375 16.9375 11.7375 16.25C11.7375 15.5625 12.3 15 12.9875 15C13.675 15 14.2375 15.5625 14.2375 16.25C14.2375 16.9375 13.6875 17.5 12.9875 17.5ZM17.3625 17.5C16.6625 17.5 16.1125 16.9375 16.1125 16.25C16.1125 15.5625 16.675 15 17.3625 15C18.05 15 18.6125 15.5625 18.6125 16.25C18.6125 16.9375 18.05 17.5 17.3625 17.5Z" fill="white"/>
            <path d="M27.9748 8.4875V13.4875C27.9748 15.9875 27.1998 17.6875 25.6498 18.625C25.2748 18.85 24.8373 18.55 24.8373 18.1125L24.8498 13.4875C24.8498 8.4875 21.9873 5.625 16.9873 5.625L9.37482 5.6375C8.93732 5.6375 8.63732 5.2 8.86232 4.825C9.79982 3.275 11.4998 2.5 13.9873 2.5H21.9873C25.9748 2.5 27.9748 4.5 27.9748 8.4875Z" fill="white"/>
          </svg>
        </figure>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          Store Messages
        </h2>
        <span className="block text-center text-1xs text-grey-subtext mt-2">Custom store & enquiry messages</span>
      </div>

      <ErrorLabel error={error?.message ?? null} />
      <SuccessLabel message={response ? "Store updated successfully!" : null} />
      <TextArea
        label="Store Welcome Message"
        rows={3}
        {...getFieldvalues("store_welcome_message", form)}
        maxLength={50}
      />
      <TextArea label="Top Notification Message" rows={3} {...getFieldvalues("custom_message", form)} maxLength={60} />
      <TextArea label="Custom enquiry message" rows={3} {...getFieldvalues("enquiry_message", form)} maxLength={60} />
      <TextArea
        label="Order successful message e.g Delivery will take 4 days"
        rows={4}
        {...getFieldvalues("custom_order_success_message", form)}
        maxLength={300}
      />
    </>
  );
};
