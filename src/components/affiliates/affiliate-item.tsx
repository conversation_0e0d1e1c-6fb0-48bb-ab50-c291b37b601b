import React from "react";
import { removeCountryCode, resolvePhone } from "../../assets/js/utils/functions";
import { SCOPES } from "../../assets/js/utils/permissions";
import { AffiliateInterface } from "../../assets/interfaces/affiliates.interface";
import Can from "../ui/can";
import ContentWithCopy from "../ui/content-with-copy";
import { TableCell, TableRow } from "../ui/table";
import IconButton from "../ui/icon-button";
import StoreLogo from "../ui/store-logo";
import Badge from "../ui/badge";
import { RoundActionBtn } from "../ui/buttons";
import useCopyClipboard from "../hooks/useCopyClipboard";
import authContext from "@/contexts/auth-context";
interface Props {
  affiliate: AffiliateInterface;
  showDetails: (data: AffiliateInterface, type: "affiliate" | "edit" | "delete") => void;
}

const AffiliateItem: React.FC<Props> = ({ affiliate, showDetails }) => {
  const { storeLink } = authContext.useContainer();
  const [isCopied, copy] = useCopyClipboard(`${storeLink}?ref=${affiliate.slug}`, { successDuration: 1000 });

  return (
    <TableRow onClick={() => showDetails(affiliate, "affiliate")}>
      <TableCell>
        <div className="flex items-center font-black-secondary font-medium">
          <StoreLogo storeName={affiliate.name} logo={null} className="h-7 w-7 text-xs font-bold mr-1.5" />
          {affiliate.name ?? "No Name"}
        </div>
      </TableCell>
      <TableCell>
        {affiliate.email ? (
          <a className="no-outline font-medium" href={`mailto:${affiliate.email}`}>
            {affiliate.email}
          </a>
        ) : (
          "-"
        )}
      </TableCell>
      <TableCell>
        {affiliate.phone ? (
          <ContentWithCopy text={resolvePhone(affiliate.phone)}>
            <a className="no-outline font-medium" href={`tel:${resolvePhone(affiliate.phone)}`}>
              {removeCountryCode(affiliate.phone)}
            </a>
          </ContentWithCopy>
        ) : (
          "-"
        )}
      </TableCell>
      <TableCell className="hidden sm:table-cell font-medium">
        <Badge color="dark" text={affiliate.type} />
      </TableCell>
      <TableCell className="hidden sm:table-cell">
        <span className="text-black-secondary font-medium">{affiliate.total_orders}</span>
      </TableCell>
      <TableCell stopBubble className={"hidden sm:table-cell"}>
        <Can data={{ permission: SCOPES.CUSTOMERS.UPDATE_CUSTOMERS }}>
          {(isAllowed) => {
            return (
              isAllowed && (
                <div className="flex items-center space-x-2" onClick={(e) => e.stopPropagation()}>
                  <RoundActionBtn size="md" icon="link" onClick={() => copy()} />
                  <RoundActionBtn size="md" icon="edit" onClick={() => showDetails(affiliate, "edit")} />
                  <RoundActionBtn size="md" icon="delete" onClick={() => showDetails(affiliate, "delete")} />
                </div>
              )
            );
          }}
        </Can>
      </TableCell>
    </TableRow>
  );
};

export default AffiliateItem;
