import { useFormik } from "formik";
import React, { useEffect } from "react";
import { AppBtn } from "../../ui/buttons";
import Mo<PERSON>, { <PERSON>dalBody, ModalFooter } from "../../ui/modal";
import * as Yup from "yup";
import { InputField, PhoneInput, SelectDropdown } from "../../ui/form-elements";
import { enumToHumanFriendly, getFieldvalues, splitPhone } from "../../../assets/js/utils/functions";
import ErrorLabel from "../../ui/error-label";
import SuccessLabel from "../../ui/success-label";
import { AFFILIATE_TYPES, AffiliateInterface } from "../../../assets/interfaces/affiliates.interface";
import { phoneValidation } from "../../../assets/js/utils/common-validations";
import { useRequest } from "../../../api/utils";
import { UpdateAffiliate } from "../../../api/affiliates";
import { UpdateAffiliateParams } from "../../../api/interfaces/affiliates.interface";
import { affiliateFormValidationSchema, affiliateTypeLabel } from "./add-affiliate";

interface Props {
  show: boolean;
  toggle: (status: boolean) => void;
  affiliate: AffiliateInterface;
  updateAffiliate: (a: AffiliateInterface) => void;
}

const EditAffiliateModal: React.FC<Props> = ({ show, toggle, affiliate, updateAffiliate }) => {
  const { isLoading, makeRequest, response, error, clearResponse } = useRequest<UpdateAffiliateParams>(UpdateAffiliate);
  const phoneSplits = splitPhone(affiliate?.phone ?? "");

  const form = useFormik({
    initialValues: {
      id: affiliate.id,
      email: affiliate?.email ?? "",
      phone: {
        code: phoneSplits?.[0] ?? "+234",
        digits: phoneSplits?.[1] ?? "",
      },
      name: affiliate.name,
      type: affiliate.type,
    },
    onSubmit: async (values) => {
      const [response, error] = await makeRequest(
        values.type === AFFILIATE_TYPES.PERSON
          ? {
              id: values.id,
              name: values.name,
              email: values.email,
              phone: `${values.phone.code}-${values.phone.digits}`,
              type: values.type,
            }
          : {
              id: values.id,
              name: values.name,
              type: values.type,
              phone: null,
              email: null,
            }
      );

      if (response) {
        updateAffiliate(response?.data);

        setTimeout(() => {
          toggle(false);
        }, 2000);
      }
    },
    validationSchema: affiliateFormValidationSchema,
  });

  useEffect(() => {
    const phoneSplits = splitPhone(affiliate?.phone ?? "");

    form.setValues({
      id: affiliate.id,
      email: affiliate?.email,
      phone: {
        code: phoneSplits?.[0] ?? "+234",
        digits: phoneSplits?.[1] ?? "",
      },
      name: affiliate.name,
      type: affiliate.type,
    });

    clearResponse();
  }, [affiliate]);

  return (
    <Modal {...{ show, toggle }} title={`Edit Affiliate - ${affiliate.name}`} size="midi">
      <form className="flex flex-col flex-auto overflow-hidden" onSubmit={form.handleSubmit}>
        <ModalBody>
          <ErrorLabel error={error?.message ?? null} />
          <SuccessLabel message={response ? "Affiliate updated successfully!" : null} />
          <SelectDropdown
            label="Affiliate Type"
            options={Object.values(AFFILIATE_TYPES).map((type) => ({
              value: type,
              text: affiliateTypeLabel[type],
            }))}
            {...getFieldvalues("type", form)}
          />
          <InputField
            label={form.values.type === AFFILIATE_TYPES.PERSON ? "Name" : "Name e.g. 'Instagram Ad'"}
            {...getFieldvalues("name", form)}
          />
          {form.values.type === AFFILIATE_TYPES.PERSON && (
            <>
              <PhoneInput label="Phone" {...getFieldvalues("phone", form)} />
              <InputField label="Email" {...getFieldvalues("email", form)} />
            </>
          )}
        </ModalBody>
        <ModalFooter>
          <AppBtn isBlock type="submit" disabled={isLoading} size="lg">
            {isLoading ? "Updating..." : "Save updates"}
          </AppBtn>
        </ModalFooter>
      </form>
    </Modal>
  );
};

export default EditAffiliateModal;
