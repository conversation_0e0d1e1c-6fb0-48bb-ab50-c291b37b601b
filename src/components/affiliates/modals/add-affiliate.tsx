import { useFormik } from "formik";
import React, { useEffect } from "react";
import { AppBtn } from "../../ui/buttons";
import Mo<PERSON>, { <PERSON>dalBody, ModalFooter } from "../../ui/modal";
import * as Yup from "yup";
import { InputField, PhoneInput, SelectDropdown } from "../../ui/form-elements";
import { enumToHumanFriendly, getFieldvalues, phoneObjectToString } from "../../../assets/js/utils/functions";
import ErrorLabel from "../../ui/error-label";
import SuccessLabel from "../../ui/success-label";
import { AFFILIATE_TYPES, AffiliateInterface } from "../../../assets/interfaces/affiliates.interface";
import { optionalPhoneValidation, phoneValidation } from "../../../assets/js/utils/common-validations";
import { useRequest } from "../../../api/utils";
import { CreateAffiliate } from "../../../api/affiliates";
import { CreateAffiliateParams } from "../../../api/interfaces/affiliates.interface";
import { TabButton } from "@/components/deliveries/modals/add-address";
import useTabs from "@/components/hooks/useTabs";

interface Props {
  show: boolean;
  toggle: (status: boolean) => void;
  addAffiliate: (affiliate: AffiliateInterface) => void;
}

const AddAffiliateModal: React.FC<Props> = ({ show, toggle, addAffiliate }) => {
  const { isLoading, makeRequest, error, response } = useRequest<CreateAffiliateParams>(CreateAffiliate);
  const form = useFormik({
    initialValues: {
      email: "",
      phone: {
        code: "+234",
        digits: "",
      },
      name: "",
      type: AFFILIATE_TYPES.PERSON,
    },
    onSubmit: async (values) => {
      const [response, error] = await makeRequest(
        values.type === AFFILIATE_TYPES.PERSON
          ? {
              ...values,
              phone: phoneObjectToString(values.phone),
            }
          : { name: values.name, type: values.type }
      );

      if (response) {
        //update affiliate details
        addAffiliate(response?.data);

        setTimeout(() => {
          toggle(false);
        }, 2000);
      }
    },
    validationSchema: affiliateFormValidationSchema,
  });

  useEffect(() => {
    if (!show) {
      form.resetForm();
    }
  }, [show]);

  return (
    <Modal {...{ show, toggle }} title={`Add Affiliate`} size="midi" bgClose={false}>
      <form className="flex flex-col flex-auto overflow-hidden" onSubmit={form.handleSubmit}>
        <ModalBody>
          <ErrorLabel error={error?.message ?? null} />
          <SuccessLabel message={response ? "Affiliate added successfully!" : null} />
          <SelectDropdown
            label="Affiliate Type"
            options={Object.values(AFFILIATE_TYPES).map((type) => ({
              value: type,
              text: affiliateTypeLabel[type],
            }))}
            {...getFieldvalues("type", form)}
          />
          <InputField
            label={form.values.type === AFFILIATE_TYPES.PERSON ? "Name" : "Name e.g. 'Instagram Ad'"}
            {...getFieldvalues("name", form)}
          />
          {form.values.type === AFFILIATE_TYPES.PERSON && (
            <>
              <PhoneInput label="Phone" {...getFieldvalues("phone", form)} />
              <InputField label="Email" {...getFieldvalues("email", form)} />
            </>
          )}
        </ModalBody>
        <ModalFooter>
          <AppBtn isBlock type="submit" size="lg" disabled={isLoading}>
            {isLoading ? "Adding affiliate..." : "Save Affiliate"}
          </AppBtn>
        </ModalFooter>
      </form>
    </Modal>
  );
};

export const affiliateFormValidationSchema = Yup.object().shape({
  type: Yup.string().oneOf(["PERSON", "PLATFORM"]).required(),
  name: Yup.string().required("Name is required"),
  email: Yup.string()
    .transform((value) => (value === null ? "" : value))
    .when("type", {
      is: "PERSON",
      then: (schema) => schema.email("Please enter a valid email").required("Email is required"),
      otherwise: (schema) => schema.notRequired(),
    }),
  phone: Yup.string().when("type", {
    is: "PERSON",
    then: () => phoneValidation(),
    otherwise: () => optionalPhoneValidation,
  }),
});

export const affiliateTypeLabel = {
  [AFFILIATE_TYPES.PERSON]: "Person",
  [AFFILIATE_TYPES.PLATFORM]: "Platform or Campaign",
};

export default AddAffiliateModal;
