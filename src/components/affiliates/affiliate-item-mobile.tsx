import React from "react";
import { AffiliateInterface } from "../../assets/interfaces/affiliates.interface";
import { removeCountryCode, resolvePhone } from "../../assets/js/utils/functions";
import Badge from "../ui/badge";
import { RoundActionBtn } from "../ui/buttons";
import ContentWithCopy from "../ui/content-with-copy";
import Dropdown, { DropdownItem } from "../ui/dropdown-new";

interface AffiliateItemMobileProps {
  index?: number;
  data: AffiliateInterface;
  onAction: (action: "affiliate" | "edit" | "delete" | "copy") => void;
}

export const AffiliateItemMobile: React.FC<AffiliateItemMobileProps> = ({
  index,
  data: { name, phone, total_orders, type },
  onAction,
}) => {
  const dropdownItems: DropdownItem[] = [
    {
      text: "Copy Affiliate Link",
      link: undefined,
      onClick: () => {
        onAction("copy");
      },
      icon: <RoundActionBtn icon="copy" size="sm" />,
    },
    {
      text: "Edit Affiliate",
      link: undefined,
      onClick: () => {
        onAction("edit");
      },
      icon: <RoundActionBtn icon="edit" size="sm" />,
    },
    {
      text: "Delete Affiliate",
      link: undefined,
      onClick: () => {
        onAction("delete");
      },
      icon: <RoundActionBtn icon="delete" size="sm" />,
    },
  ];

  const affiliateColors = [
    "accent-orange-500",
    "accent-green-500",
    "accent-red-500",
    "accent-yellow-500",
    "primary-500",
  ];
  const colorClass = affiliateColors[index % affiliateColors.length];

  return (
    <li
      className="rounded-10 border border-grey-divider p-3 mb-2.5 cursor-pointer hover:bg-grey-fields-100 hover:bg-opacity-30 transition-all ease-out"
      onClick={() => onAction("affiliate")}
    >
      <div className="relative flex items-center">
        <figure
          className={`text-${colorClass} bg-${colorClass} bg-opacity-10 w-14 h-14 rounded-8 flex items-center justify-center overflow-hidden`}
        >
          <div className="w-6 text-current">
            {/* prettier-ignore */}
            <svg width="25" height="25" viewBox="0 0 25 25" fill="none">
              <path d="M12.5007 2.08331C9.77148 2.08331 7.55273 4.30206 7.55273 7.03123C7.55273 9.70831 9.64648 11.875 12.3757 11.9687C12.459 11.9583 12.5423 11.9583 12.6048 11.9687C12.6257 11.9687 12.6361 11.9687 12.6569 11.9687C12.6673 11.9687 12.6673 11.9687 12.6777 11.9687C15.3444 11.875 17.4382 9.70831 17.4486 7.03123C17.4486 4.30206 15.2298 2.08331 12.5007 2.08331Z" fill="currentColor"/>
              <path d="M17.791 14.7396C14.8848 12.8021 10.1452 12.8021 7.2181 14.7396C5.89518 15.625 5.16602 16.8229 5.16602 18.1041C5.16602 19.3854 5.89518 20.5729 7.20768 21.4479C8.66602 22.4271 10.5827 22.9166 12.4993 22.9166C14.416 22.9166 16.3327 22.4271 17.791 21.4479C19.1035 20.5625 19.8327 19.375 19.8327 18.0833C19.8223 16.8021 19.1035 15.6146 17.791 14.7396Z" fill="currentColor"/>
            </svg>
          </div>
        </figure>
        <div className="flex flex-col items-start ml-2.5">
          <Badge color="orange" text={`${total_orders ?? 0} orders`} className="absolute right-0 bottom-0" />
          <span className="text-sm font-medium text-black mb-2.5 inline-block overflow-hidden overflow-ellipsis whitespace-nowrap">
            {name ?? "No Name"}
          </span>
          <div className="flex items-center text-dark text-sm">{phone ? removeCountryCode(phone) : "No Phone"}</div>
        </div>
        <div className="absolute top-0 right-0">
          <Dropdown items={dropdownItems} vPosition="BOTTOM" gap={false}>
            <button className="z-[999] dropdown-toggle text-black-400 p-2 -mt-2 -mr-2">
              {/* prettier-ignore */}
              <svg className="w-5 h-5" viewBox="0 0 20 20" fill="none">
                <g opacity="0.5">
                  <path d="M4.16667 8.73837C3.25 8.73837 2.5 9.48837 2.5 10.405C2.5 11.3217 3.25 12.0717 4.16667 12.0717C5.08333 12.0717 5.83333 11.3217 5.83333 10.405C5.83333 9.48837 5.08333 8.73837 4.16667 8.73837Z" fill="currentColor" />
                  <path d="M15.8337 8.73837C14.917 8.73837 14.167 9.48837 14.167 10.405C14.167 11.3217 14.917 12.0717 15.8337 12.0717C16.7503 12.0717 17.5003 11.3217 17.5003 10.405C17.5003 9.48837 16.7503 8.73837 15.8337 8.73837Z" fill="currentColor" />
                  <path d="M9.99967 8.73837C9.08301 8.73837 8.33301 9.48837 8.33301 10.405C8.33301 11.3217 9.08301 12.0717 9.99967 12.0717C10.9163 12.0717 11.6663 11.3217 11.6663 10.405C11.6663 9.48837 10.9163 8.73837 9.99967 8.73837Z" fill="currentColor" />
                </g>
              </svg>
            </button>
          </Dropdown>
        </div>
      </div>
    </li>
  );
};
