import { useFormik } from "formik";
import { useEffect, useState } from "react";
import useSteps from "../../hooks/useSteps";
import { AppBtn } from "../../ui/buttons";
import Mo<PERSON>, { <PERSON>dalBody, ModalFooter } from "../../ui/modal";
import WithdrawalSummaryStep from "./summary";
import * as Yup from "yup";
import { calculateWithdrawalFee, to<PERSON>air<PERSON> } from "../../../assets/js/utils/utils";
import { MAXIMUM_WITHDRAWAL_AMOUNT, MINIMUM_WITHDRAWAL_AMOUNT } from "../../../assets/js/utils/constants";
import { Fees, Wallet, WithdrawalAccount } from "../../../assets/interfaces/wallets";
import AmountAndAccount from "./amount-and-account";
import EnterWithdrawalToken from "./enter-token";
import { useFetcher, useRequest } from "../../../api/utils";
import { CompleteWithdrawal, GetWithdrawalFees, RequestWithdrawal } from "../../../api/wallets";
import { CompleteWithdrawalParams, RequestWithdrawalParams } from "../../../api/interfaces";
import { StringLocale } from "yup/lib/locale";
import ErrorLabel from "../../ui/error-label";
import SuccessIcon from "../../../assets/icons/statuses/success.svg";
import { getUserCountry, millify, toCurrency } from "../../../assets/js/utils/functions";
import SuccessAnimation from "../../ui/success-animation";
import { CURRENCIES } from "../../../assets/interfaces";
import EnterSecurityPin from "./enter-security-pin";
import WalletContext from "@/contexts/wallet-context";
import { useRouter } from "next/router";
import { toast } from "@/components/ui/toast";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  wallet: Wallet;
  balance: number;
  debitBalance: (amount: number) => void;
  currency: CURRENCIES;
}

export interface WithdrawalForm {
  amount: number;
  code: string;
  withdrawal_account: string;
  account_info: WithdrawalAccount;
  request_id?: string;
  email: "";
  fee: number;
  security_pin: string;
}

const MakeWithdrawalModal: React.FC<Props> = ({ show, toggle, balance, debitBalance, currency, wallet }) => {
  const router = useRouter();

  const { step, stepIndex, next, previous, canNext, changeStep, isActive } = useSteps(
    ["AMOUNT_ACCOUNT", "SUMMARY", "SECURITY_PIN", "ENTER_TOKEN", "STATUS"],
    0
  );
  const [error, setError] = useState("");
  const requestWithdrawalReq = useRequest<RequestWithdrawalParams>(RequestWithdrawal);
  const completeWithdrawalReq = useRequest<CompleteWithdrawalParams>(CompleteWithdrawal);
  const getWithdrawalFees = useFetcher(GetWithdrawalFees, { currency: currency });
  const fees: Fees = getWithdrawalFees?.response?.data ?? null;
  const feesHasLoaded = fees && !getWithdrawalFees?.error && !getWithdrawalFees.isLoading;

  const isLoading = requestWithdrawalReq?.isLoading || completeWithdrawalReq?.isLoading;

  const form = useFormik<WithdrawalForm>({
    initialValues: {
      amount: 0,
      code: "",
      withdrawal_account: "",
      account_info: null,
      request_id: "",
      email: "",
      fee: 0,
      security_pin: "",
    },
    validationSchema: validationSchema(
      balance,
      step,
      toNaira(fees?.min_withdrawal_amount ?? 0),
      toNaira(fees?.max_withdrawal_amount ?? 0),
      currency
    ),
    onSubmit: async (values) => {
      switch (step) {
        case "AMOUNT_ACCOUNT":
          form.setFieldValue("fee", toNaira(calculateWithdrawalFee(form.values?.amount, fees.fees)));
          next();
          break;
        case "SUMMARY":
          const requestWithdrawal = await requestWithdrawalReq.makeRequest({
            withdrawal_account: values.withdrawal_account,
            amount: Number(values.amount),
            wallet: wallet.id,
          });

          if (requestWithdrawal[0]) {
            if (!requestWithdrawal[0]?.data?.request) {
              setError("Something went wrong! Reload page & retry");
              break;
            }

            form.setFieldValue("request_id", requestWithdrawal[0]?.data?.request);
            form.setFieldValue("email", requestWithdrawal[0]?.data?.email);
            next();
          }
          break;
        case "SECURITY_PIN":
          next();
          break;
        case "ENTER_TOKEN":
          const [res, err] = await completeWithdrawalReq.makeRequest({
            code: values.code,
            request_id: values.request_id,
            security_pin: values.security_pin,
          });

          if (err) {
            toast.error({
              title: "Failed",
              message: err?.message ?? "An error occurred, please try again",
            });
          }

          if (err && err.statusCode !== 412) {
            changeStep("AMOUNT_ACCOUNT");
            form.resetForm();
            return;
          }

          if (!err) {
            debitBalance(Number(values.amount) + Number(values.fee));
            next();
          }

          break;

        case "STATUS":
          toggle(false);
          break;

        default:
        //do nothing
      }
    },
  });

  useEffect(() => {
    if (!show) {
      form.resetForm();
      changeStep("AMOUNT_ACCOUNT");
    }
  }, [show]);

  return (
    <Modal
      {...{ show, toggle }}
      title={step === "SUMMARY" ? "Confirm withdrawal" : "Make a withdrawal"}
      size="midi"
      bgClose={false}
    >
      <form className="flex flex-col flex-auto overflow-hidden" onSubmit={form.handleSubmit}>
        <ModalBody className="min-h-[270px]">
          {!wallet?.has_completed_kyc && (
            <div
              className="bg-grey-fields-100 text-accent-red-500 text-1xs -mt-5 sm:-mt-6.25 -mx-5 sm:-mx-6.25 mb-5 sm:mb-6.25 px-3.5 py-2.5 border-t border-grey-border text-center font-medium cursor-pointer"
              onClick={() => router.push("/payments/kyc")}
            >
              Please complete your KYC to make withdrawals
            </div>
          )}
          <ErrorLabel
            error={
              getWithdrawalFees?.error?.message ||
              completeWithdrawalReq?.error?.message ||
              requestWithdrawalReq?.error?.message ||
              error
            }
          />
          {getWithdrawalFees?.isLoading && (
            <div className="py-12 flex items-center justify-center">
              <div className="spinner spinner--md text-primary-500"></div>
            </div>
          )}
          {feesHasLoaded && (
            <>
              <div className={isActive("AMOUNT_ACCOUNT") ? "" : "hidden"}>
                <AmountAndAccount {...{ form, fees, currency, balance }} />
              </div>
              {isActive("SUMMARY") && <WithdrawalSummaryStep {...{ form, fees, currency }} />}
              {isActive("SECURITY_PIN") && <EnterSecurityPin {...{ form }} />}
              {isActive("ENTER_TOKEN") && <EnterWithdrawalToken {...{ form }} />}
              {isActive("STATUS") && (
                <div className="flex flex-col items-center w-full py-8">
                  <figure className="mb-3.5">
                    <SuccessAnimation />
                  </figure>
                  <h2 className="text-center text-black text-2lg sm:text-[26px] lg:text-3xl mx-auto !leading-tight font-light">
                    Your withdrawal of
                    <br />
                    <b className="font-bold">
                      {currency} {millify(Number(form.values.amount))} is processing
                    </b>
                  </h2>
                </div>
              )}
            </>
          )}
        </ModalBody>
        {feesHasLoaded && (
          <ModalFooter>
            <div className="flex items-center space-x-4 w-full">
              {!isActive("AMOUNT_ACCOUNT") && !isActive("STATUS") && (
                <AppBtn isBlock color="neutral" className="flex-1" onClick={previous} size="lg">
                  Back
                </AppBtn>
              )}
              <AppBtn
                isBlock
                type="submit"
                className="flex-1"
                disabled={isLoading || !wallet.has_completed_kyc}
                size="lg"
              >
                {isLoading ? "Loading..." : "Continue"}
              </AppBtn>
            </div>
          </ModalFooter>
        )}
      </form>
    </Modal>
  );
};

const validationSchema = (balance: number, step: string, min: number, max: number, currency) =>
  Yup.object().shape({
    amount: Yup.number()
      .typeError("Amount must be a number")
      .required("Withdrawal amount is required")
      .min(min, `Please withdraw at least ${currency} ${millify(min)}`)
      .max(max, `You can only withdraw ${currency} ${millify(max)} at a time`)
      .test("lower_than_balance", "Insufficient funds", (value) => value <= toNaira(balance)),
    withdrawal_account: Yup.string().required("Withdrawal account is required"),
    code:
      step === "ENTER_TOKEN"
        ? Yup.string().required("OTP token is required").length(6, "OTP token should be 6 digits")
        : undefined,
    security_pin: Yup.string().min(6, "Security Pin must be at least 6 digits"),
  });

export default MakeWithdrawalModal;
