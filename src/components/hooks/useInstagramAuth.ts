import { CheckInstagramToken, GenerateInstagramAccessToken } from "@/api";
import { useFetcher, useRequest } from "@/api/utils";
import { paramsFromObject } from "@/assets/js/utils/functions";
import { clearUrlParams } from "@/assets/js/utils/utils";
import useSearchParams from "@/components/hooks/useSearchParams";
import { toast } from "@/components/ui/toast";
import { useCallback, useEffect, useState } from "react";

const IG_AUTH_URL = "https://www.instagram.com/oauth/authorize";

function useInstagramAuth(onAuthSuccess?: () => void, callBackParam = "") {
  const [isAuthenticated, setAuthenticated] = useState(false);
  const { code, open } = useSearchParams(["code", "open"]);
  const getTokenReq = useRequest(GenerateInstagramAccessToken);
  const checkTokenReq = useFetcher(CheckInstagramToken);
  const checkTokenRes = checkTokenReq.response;

  const getInstagramAuthCode = useCallback(() => {
    const appKey = process.env.NEXT_PUBLIC_INSTAGRAM_APP_KEY;
    const redirectUri = window.location.href.includes("localhost")
      ? `https://socialsizzle.herokuapp.com/auth/`
      : window.location.origin + window.location.pathname + `?open=${callBackParam}`;

    const params = paramsFromObject({
      client_id: appKey,
      redirect_uri: redirectUri,
      scope: "instagram_business_basic",
      response_type: "code",
      enable_fb_login: "0",
      force_authentication: "0",
    });

    const url = `${IG_AUTH_URL}?${params}#weblink`;

    window.location.href = url;
  }, []);

  useEffect(() => {
    if (checkTokenReq.response && !checkTokenReq.error) {
      onAuthSuccess?.();
      setAuthenticated(true);
    }
  }, [checkTokenRes]);

  useEffect(() => {
    const fn = async () => {
      if (code) {
        const redirectUri = window.location.href.includes("localhost")
          ? `https://socialsizzle.herokuapp.com/auth/`
          : window.location.origin + window.location.pathname;
        const getTokenPromise = getTokenReq.makeRequest({ access_code: code, redirect_uri: redirectUri });

        toast.promise(
          async () => {
            const [_, err] = await getTokenPromise;
            if (err) throw err;
          },
          {
            loading: {
              title: "Authenticating your Instagram account",
              message: "Please wait...",
            },
            success: {
              title: "Successful",
              message: "Authenticated your Instagram account successfully!",
            },
            error: {
              title: "Failed",
              message: "We couldn't authenticate your Instagram account!",
              actionText: "Retry",
              actionFunc: () => fn(),
            },
          }
        );

        const [res] = await getTokenPromise;
        
        if (res) {
          onAuthSuccess?.();
          setAuthenticated(true);
        }
        clearUrlParams();
      }
    };
    fn();
  }, [code]);

  return {
    getInstagramAuthCode,
    isAuthenticated,
    open,
    authenticating: getTokenReq.isLoading || checkTokenReq.isLoading,
    getTokenError: getTokenReq.error,
    checkTokenError: checkTokenReq.error,
  };
}

export default useInstagramAuth;
