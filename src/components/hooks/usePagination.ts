import { useEffect, useReducer, useState } from "react";
import useSearchParams from "./useSearchParams";
import { createBrowserHistory } from "history";

function usePagination(
  modifyUrl: boolean = true,
  initialPerPage: number = 10,
  storageKey: string = "pagination_per_page"
) {
  const [currentPage, setCurrentPage] = useReducer((x, a: number) => a + 0, 1);

  // Initialize per_page from localStorage immediately
  const getInitialPerPage = () => {
    if (typeof window !== "undefined") {
      const savedPerPage = localStorage.getItem(storageKey);
      if (savedPerPage) {
        const parsedPerPage = parseInt(savedPerPage, 10);
        if ([10, 25, 50, 75, 100].includes(parsedPerPage)) {
          return parsedPerPage;
        }
      }
    }
    return initialPerPage;
  };

  const [perPage, setPerPage] = useState(getInitialPerPage);

  const params = useSearchParams(["page"]);

  useEffect(() => {
    if (params?.page && Number.parseInt(params?.page) !== currentPage) {
      setCurrentPage(Number.parseInt(params?.page));
    }
  }, [params]);

  useEffect(() => {
    const savedPerPage = localStorage.getItem(storageKey);
    if (savedPerPage) {
      const parsedPerPage = parseInt(savedPerPage, 10);
      if ([10, 25, 50, 75, 100].includes(parsedPerPage)) {
        setPerPage(parsedPerPage);
      }
    }
  }, []);

  const handleSetPage = (page: number) => {
    if (modifyUrl) {
      const history = createBrowserHistory();
      const searchParams = new URLSearchParams(window.location.search);
      searchParams.set("page", page.toString());

      history.push({
        pathname: location.pathname,
        search: `?${searchParams.toString()}`,
      });
    }
    setCurrentPage(page);
  };

  const handleSetPerPage = (newPerPage: number) => {
    setPerPage(newPerPage);
    setCurrentPage(1);
  };

  function goNext() {
    handleSetPage(currentPage + 1);
  }

  function goPrevious() {
    if (currentPage > 0) {
      handleSetPage(currentPage - 1);
    }
  }

  return {
    currentPage,
    perPage,
    setPerPage: handleSetPerPage,
    goNext,
    goPrevious,
    setPage: handleSetPage,
  };
}

export default usePagination;
