import { CreateItems, GetStoreInfoBlocks, GetTieredPricing, GetVariantTemplates } from "@/api";
import { CreateItemsParams } from "@/api/interfaces";
import { useFetcher, useRequest } from "@/api/utils";
import { FILE_TYPES, Media, MediaType, Image, InfoBlockInterface, PricingTierInterface } from "@/assets/interfaces";
import { getRandString, sluggify } from "@/assets/js/utils/functions";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import useImageUploads from "@/components/hooks/useImageUploads";
import { useModals } from "@/components/hooks/useModals";
import useProgress from "@/components/hooks/useProgess";
import { toast } from "@/components/ui/toast";
import authContext from "@/contexts/auth-context";
import { Product, ProductForm } from "@/pages/products/create";
import { useEffect, useMemo, useState } from "react";
import { useStorefrontItems } from "./useStoreItems";
import { mixpanelTrack } from "@/assets/js/utils/utils";

function useProductController(isSetup = false) {
  const { stores, user, store, subscription, updateStore } = authContext.useContainer();
  const [medias, setMedias] = useState<Media[]>([]);
  const [uploadMethod, setUploadMethod] = useState<ProductCreateMethod>(null);
  const [currentStep, setCurrentStep] = useState<ProductUploadStep>("method");
  const [form, setForm] = useState<ProductForm>({ products: [] });
  const [pastProgress, setPastProgress] = useState<ProductForm>(null);
  const [currentProduct, setCurrentProduct] = useState(0);
  const [videoResourcesLoaded, setVideoResourcesLoaded] = useState(false);
  const [canTranscode, setCanTranscode] = useState(false);
  const [infoBlocks, setInfoBlocks] = useState<InfoBlockInterface[]>([]);
  const [pricingTiers, setPricingTiers] = useState<PricingTierInterface[]>([]);
  const [currentPricingTier, setCurrentPricingTier] = useState<PricingTierInterface | null>(null);

  const blocksReq = useFetcher(GetStoreInfoBlocks, { id: store.id });
  const pricingTiersReq = useFetcher(GetTieredPricing, {
    filter: {},
    page: 1,
    per_page: Number.MAX_SAFE_INTEGER,
  });

  const { items: storeItems, fetchItemsReq, getItem } = useStorefrontItems(store?.id);

  const images = useMemo<Image[]>(() => {
    return medias.filter((m) => m.type === MediaType.IMAGE).map(({ type, ...m }) => m);
  }, [medias]);

  useImageUploads(images, FILE_TYPES.ITEMS, (imgs) => {
    const nonImages = medias.filter((m) => m.type !== MediaType.IMAGE);
    const imageMedias: Media[] = imgs.map((i) => ({ type: MediaType.IMAGE, ...i }));
    setMedias([...nonImages, ...imageMedias]);
  });

  const { modals, toggleModal } = useModals([
    "import",
    "tutorial",
    "chowdeck",
    "instagram",
    "progress",
    "count",
    "menu",
    "count",
    "progresses",
    "manual",
    "trim",
    "thumbnail",
    "loader",
    "create_pricing_tier",
    "view_pricing_tier",
    "edit_pricing_tier",
    "select_pricing_tier",
    "assigned_items",
  ]);
  const [loadModals, setLoadModals] = useState({ chowdeck: false, instagram: false });

  const userStores = stores.filter((s) => s.owner === user?.id);
  const selectingMedia = currentStep === "media";
  const allowVariants =
    !isSetup &&
    actionIsAllowed({
      planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_OPTIONS,
      plan: store?.subscription?.plan?.type ?? "STARTER",
    });

  const allowChowdeckImports =
    !isSetup &&
    store?.public_access_tokens?.chowdeck?.reference &&
    actionIsAllowed({
      planPermission: SCOPES.PLAN_PERMISSIONS.CAN_USE_CHOWDECK,
      plan: store?.subscription?.plan?.type ?? "STARTER",
    });

  const canUploadVideos = actionIsAllowed({
    planPermission: SCOPES.PRODUCTS.UPLOAD_VIDEOS,
    plan: store?.subscription?.plan?.type ?? "STARTER",
  });

  const instagramUploadCount = store?.meta?.instagram_item_upload_count ?? 0;
  const canInstagramUpload =
    instagramUploadCount < 2 ||
    actionIsAllowed({
      planPermission: SCOPES.PLAN_PERMISSIONS.CAN_INSTAGRAM_IMPORT,
      plan: store?.subscription?.plan?.type ?? "STARTER",
    });

  const maxInstagramUpload = !subscription?.plan?.type
    ? 5
    : subscription?.plan?.type === "STARTER"
    ? Math.max(2 - instagramUploadCount, 0)
    : Number.MAX_SAFE_INTEGER;

  const createReq = useRequest<CreateItemsParams>(CreateItems);
  const getvariantTemplatesReq = useFetcher(GetVariantTemplates, {});
  const templates: Product["variants"][] = getvariantTemplatesReq.response?.data?.templates || [];

  const canUploadMenu = actionIsAllowed({
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_USE_MENU_UPLOADS,
    plan: subscription?.plan?.type ?? "STARTER",
  });

  useEffect(() => {
    if (form.products.length !== 0) {
      localStorage.setItem("product-upload-progress", JSON.stringify(form));
    }

    if (currentProduct > form.products.length - 1 && form.products.length > 0 && currentStep === "details") {
      setCurrentProduct(form.products.length - 1);
      return;
    }

    if (form.products.length === 0 && currentStep === "details") {
      setCurrentStep("media");
      return;
    }
  }, [form]);

  useEffect(() => {
    const progressFromStorage = JSON.parse(localStorage.getItem("product-upload-progress")) ?? null;
    setPastProgress(progressFromStorage);
  }, []);

  useEffect(() => {
    if (blocksReq.response?.data?.info_blocks) {
      setInfoBlocks(blocksReq.response?.data?.info_blocks);
    }
  }, [blocksReq.response]);

  useEffect(() => {
    if (pricingTiersReq.response?.data) {
      setPricingTiers(pricingTiersReq.response?.data);
    }
  }, [pricingTiersReq.response]);

  const changeView = (dir: string) => {
    if (dir === "forward") {
      setCurrentProduct(currentProduct + 1);
    } else if (dir === "backwards" && currentProduct === 0) {
      if (uploadMethod === ProductCreateMethod.MANUAL) setCurrentStep("media");
      else setCurrentStep("method");
    } else {
      setCurrentProduct(currentProduct - 1);
    }
  };
  const submitForm = async () => {
    const products = form.products.map((p) => {
      return {
        ...p,
        videos: p.videos?.map((v) => ({ name: v.name, url: v.url, thumbnail: v.meta?.thumbnail?.url })),
        images: p.images.map((i) => i.url),
        price: Number(p.price),
        upload_source: uploadMethod,
        slug: `${sluggify(p.name)}-${Date.now()}-${getRandString(3)}`,
        is_always_available: p.is_always_available ?? true,
        info_blocks: p.info_blocks?.map((b) => b.id),
        tiered_pricing: p.tiered_pricing?.id,
      };
    });

    await createReq.makeRequest({
      items: products,
      store: store.id,
    });

    if (createReq.response) {
      if (isSetup) {
        mixpanelTrack("Uploaded First Product", {
          email: user?.email,
          productsCount: products.length,
        });
      }

      localStorage.removeItem("product-upload-progress");
      const storeMeta = store?.meta ?? {};
      const currentIgUploadCount = storeMeta.instagram_item_upload_count ?? 0;
      const instagramProductsCount =
        products?.filter((p) => p.upload_source === ProductCreateMethod.INSTAGRAM).length ?? 0;
      const updateData = {
        item_count: (store?.item_count ?? 0) + products.length,
        meta: { ...storeMeta, instagram_item_upload_count: currentIgUploadCount + instagramProductsCount },
      };

      updateStore(updateData);
    }

    setCurrentStep("response");
  };

  const handleSelectMethod = (method: ProductCreateMethod) => {
    setUploadMethod(method);

    switch (method) {
      case ProductCreateMethod.INSTAGRAM:
        break;
      case ProductCreateMethod.CHOWDECK:
        setLoadModals({ ...loadModals, chowdeck: true });
        toggleModal("chowdeck");
        break;
      case ProductCreateMethod.MANUAL:
        if (isSetup) {
          setCurrentStep("media");
          break;
        }
        toggleModal("manual");
        break;
      case ProductCreateMethod.STORE:
        toggleModal("import");
        break;
      case ProductCreateMethod.MENU_IMAGE:
        toggleModal("menu");
        break;
    }
  };

  return {
    handleSelectMethod,
    modals,
    toggleModal,
    submitForm,
    form,
    changeView,
    selectingMedia,
    allowVariants,
    maxInstagramUpload,
    canUploadMenu,
    canUploadVideos,
    createReq,
    medias,
    setMedias,
    pastProgress,
    userStores,
    canInstagramUpload,
    allowChowdeckImports,
    currentStep,
    setCurrentStep,
    setForm,
    uploadMethod,
    currentProduct,
    templates,
    loadModals,
    setVideoResourcesLoaded,
    videoResourcesLoaded,
    canTranscode,
    setCanTranscode,
    infoBlocksData: {
      blocks: infoBlocks,
      loading: blocksReq.isLoading,
      error: blocksReq.error,
      setBlocks: setInfoBlocks,
    },
    pricingTiersData: {
      tiers: pricingTiers,
      loading: pricingTiersReq.isLoading,
      error: pricingTiersReq.error,
      setTiers: setPricingTiers,
    },
    currentPricingTier,
    setCurrentPricingTier,
    storeItems,
    fetchItemsReq,
    getItem,
  };
}
export default useProductController;

export enum ProductCreateMethod {
  MANUAL = "MANUAL",
  CHOWDECK = "CHOWDECK",
  STORE = "STORE",
  MENU_IMAGE = "MENU_IMAGE",
  INSTAGRAM = "INSTAGRAM",
}

export type ProductUploadStep = "method" | "media" | "import" | "details" | "response";
