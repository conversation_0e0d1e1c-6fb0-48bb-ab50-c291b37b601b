import { useState } from "react";

export type ModalState<T extends string = string> = { [K in T]: { show: boolean } };

export function useModals<T extends string>(
  modalKeys: readonly T[]
): {
  modals: ModalState<T>;
  toggleModal: (key: T, state?: boolean) => void;
  type: T;
} {
  const [modals, setModals] = useState<ModalState<T>>(getModals(modalKeys));

  function toggleModal(key: T, state?: boolean) {
    const m = { ...modals };

    if (m[key]) {
      m[key].show = state === undefined ? !m[key].show : state;
      setModals({ ...m });
    }
  }

  return {
    modals,
    toggleModal,
    type: undefined as unknown as T,
  };
}

function getModals<T extends string>(list: readonly T[]): ModalState<T> {
  let modalList = {} as ModalState<T>;
  list.forEach((li) => (modalList[li] = { show: false }));
  return modalList;
}
