import { CreateItems, EditItems } from "@/api";
import { CreateItemsParams, EditItemParams } from "@/api/interfaces";
import { useRequest } from "@/api/utils";
import { Media, Image, VariantForm, MediaType } from "@/assets/interfaces";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import { convertItemToProduct } from "@/assets/js/utils/utils";
import { useModals } from "@/components/hooks/useModals";
import useRefSync from "@/components/hooks/useRefSync";
import useVideoTranscode, { ffmpegContext, VideoStepStatus } from "@/components/hooks/useVideoTranscode";
import { EditProductProps } from "@/components/products/modals/edit-product";
import { toast } from "@/components/ui/toast";
import authContext from "@/contexts/auth-context";
import { Product } from "@/pages/products/create";
import { useFormik } from "formik";
import { useEffect, useRef, useState } from "react";
import * as Yup from "yup";

function useEditProductForm(props: EditProductProps) {
  const {
    isDuplication = false,
    updateItemList,
    toggle,
    item,
    categories,
    updateItem,
    show,
    templates,
    infoBlocks,
    setInfoBlocks,
    blocksLoading,
    blocksError,
  } = props;
  const { subscription, store } = authContext.useContainer();
  // const [infoBlocks, setInfoBlocks] = useState<InfoBlockInterface[]>(blocks || []);
  const [__, setShowCategoriesModal] = useState(false);

  const bodyRef = useRef<HTMLDivElement>(null);
  const {
    modals,
    toggleModal,
    type: modalsType,
  } = useModals([
    "categories",
    "variants",
    "v_explainer",
    "delete_options",
    "loader",
    "process_video",
    "change_thumbnail",
    "view_pricing_tier",
    "delete_pricing_tier",
    "create_pricing_tier",
    "select_pricing_tier",
    "edit_pricing_tier",
    "assigned_items",
  ]);

  const { isLoading, makeRequest, error, response } = useRequest<EditItemParams>(EditItems);
  //const [product, setProduct] = useState<Product>();
  const createReq = useRequest<CreateItemsParams>(CreateItems);
  const allowVariants = actionIsAllowed({
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_OPTIONS,
    plan: subscription?.plan?.type ?? "STARTER",
  });
  const [errMessage, setErrMessage] = useState("");

  const form = useFormik<Product>({
    initialValues: {
      ...convertItemToProduct(item),
    },
    validationSchema,
    onSubmit: async (values) => {
      const videosAreProcessing = Object.values(videoProgresses.value).some(
        (taskProgress) => taskProgress.step !== VideoStepStatus.SUCCESS
      );

      if (videosAreProcessing) {
        setErrMessage("Please wait for videos to finish processing before submitting");
        toast.error({
          title: "Videos are processing",
          message: "Please wait for videos to finish processing before submitting",
        });
        return;
      }
      setErrMessage("");

      if (values.variants?.options) {
        values.variants.options = values.variants?.options.map((o) => ({ ...o, discount_price: null })) ?? [];
      }
      const payload = {
        ...values,
        info_blocks: values.info_blocks.map((block) => block.id),
        images: values.images.map((i) => i.url),
        price: Number(values.price),
        videos: values.videos?.map((v) => ({ name: v.name, url: v.url, thumbnail: v.meta?.thumbnail?.url })),
        tiered_pricing: values.tiered_pricing?.id,
      };

      if (payload.images.length < 1 && payload.videos.length < 1) {
        setErrMessage("Please add at least one product media");
        bodyRef.current.scrollTop = 0;
        return;
      }

      if (isDuplication) {
        const [res] = await createReq.makeRequest({
          store: item.store,
          items: [{ ...payload, info_blocks: payload.info_blocks.map((block) => block) }],
        });

        if (res) {
          const duplicatedProduct = { ...res.data[0], category: categories.find((c) => res.data[0].category === c.id) };
          updateItemList(duplicatedProduct, true);
          bodyRef.current.scrollTop = 0;
          toggle(false);
        }

        return;
      }

      const [response, error] = await makeRequest({ id: item.id, item: payload as any });
      bodyRef.current.scrollTop = 0;

      if (error) {
        return;
      }

      const updatedProduct = {
        ...response.data,
        info_blocks: values.info_blocks,
        category: categories.find((c) => response.data.category === c.id),
        tags: categories.filter((c) => (response.data.tags ?? []).some((t) => t === c.id)),
      };
      updateItem(updatedProduct);
      toggle(false);
    },
  });

  const formValuesRef = useRefSync(form.values);

  /**
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   */

  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [videoTrim, setVideoTrim] = useState<any>({ start: 0, end: 30 });

  useEffect(() => {
    form.setValues({ ...convertItemToProduct(item) });
  }, [item]);

  const { ffmpegRef, ffmpegLoading, canTranscode, loadProgress } = ffmpegContext.useContainer();
  const { removeVideoProgress, retryVideoTask, videoProgresses, transcodeVideo } = useVideoTranscode(
    (taskId, url, blob) => {
      const values = formValuesRef.current;
      if (url && values) {
        const index = values.videos.findIndex((v) => v.meta.id === taskId);
        if (index >= 0) {
          const videosCopy = [...(values.videos ?? [])];
          const newFile = new File([blob], (videosCopy?.[index]?.file as File)?.name ?? "Video");

          videosCopy[index].url = url;
          videosCopy[index].file = newFile;

          form.setFieldValue("videos", videosCopy);
        }
      }
    },
    {
      ffmpegRef,
    }
  );

  const handleOpenVideoModal = (m: string, i: number) => {
    if (ffmpegLoading) {
      toggleModal("loader");
      return;
    }
    toggleModal(m as typeof modalsType);
    setCurrentVideoIndex(i);
  };

  const handleChangeThumbnail = (i: number) => {
    setCurrentVideoIndex(i);
    toggleModal("change_thumbnail");
  };

  const saveMedias = (medias: Media[]) => {
    form.setFieldValue("images", [...(form.values.images ?? []), ...medias.filter((m) => m.type === MediaType.IMAGE)]);
    const videos = [...(form.values.videos ?? []), ...medias.filter((m) => m.type === MediaType.VIDEO)];
    form.setFieldValue("videos", videos);
  };

  const removeProductVideo = (index: number, taskId: string) => {
    const productCopy = { ...form.values };
    if (!productCopy?.videos?.[index]?.file && videoProgresses?.value?.[taskId]) removeVideoProgress(form.values.id);

    productCopy.videos = productCopy.videos.filter((_, i) => i != index);
    form.setValues(productCopy);
  };

  /**
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   */

  const product = form.values;
  const productImageUploading = product.images.some((i) => i.isUploading);
  const saveImages = (images: Image[]) => {
    form.setFieldValue("images", images);
  };

  const changeThumbnail = (e: React.MouseEvent<HTMLElement, MouseEvent>, id: number, type: string) => {
    e.stopPropagation();
    e.preventDefault();
    form.setFieldValue("thumbnail", id);
    form.setFieldValue("thumbnail_type", type);
  };

  const removePickedMedia = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, idx: number, type: string) => {
    e.stopPropagation();

    if (idx === form.values.thumbnail && form.values.thumbnail_type === type) {
      toast.error({
        title: "Error!",
        message: `Cannot delete thumbnail image.`,
      });
      return;
    }

    const mediasCopy = type === "image" ? [...form.values.images] : [...form.values.videos];
    const newThumbnail = form.values.thumbnail > idx ? form.values.thumbnail - 1 : form.values.thumbnail;
    mediasCopy.splice(idx, 1);
    form.setFieldValue(type === "image" ? "images" : "videos", mediasCopy);
    form.setFieldValue("thumbnail", newThumbnail);
  };

  const updateVariants = (variants: VariantForm) => {
    form.setFieldValue("variants", variants);
    form.handleSubmit();
  };

  const deleteOptions = () => {
    if (modals.delete_options.show) {
      toggleModal("delete_options");
    }

    form.setFieldValue("variants", { ...form.values.variants, options: [] });
  };

  const formContent = {
    title: isDuplication ? "" : "Edit",
    success: isDuplication ? "created" : "updated",
    button: isDuplication ? "Save Item" : "Save Updates",
    buttonIsLoading: isDuplication ? "Saving Item..." : "Saving Updates...",
  };

  const canUploadVideos = actionIsAllowed({
    planPermission: SCOPES.PRODUCTS.UPLOAD_VIDEOS,
    plan: subscription?.plan?.type ?? "STARTER",
  });

  return {
    form,
    handleOpenVideoModal,
    changeThumbnail,
    removePickedMedia,
    removeProductVideo,
    saveImages,
    saveMedias,
    currentVideoIndex,
    modals,
    toggleModal,
    productImageUploading,
    updateVariants,
    transcodeVideo,
    product,
    store,
    videoTrim,
    setVideoTrim,
    deleteOptions,
    canUploadVideos,
    canTranscode,
    loadProgress,
    ffmpegLoading,
    errMessage,
    show,
    templates,
    infoBlocks,
    setInfoBlocks,
    blocksLoading,
    blocksError,
    setShowCategoriesModal,
    toggle,
    isLoading: isLoading || createReq.isLoading,
    allowVariants,
    formContent,
    bodyRef,
    createReq,
    response,
    error,
    retryVideoTask,
    videoProgresses,
    handleChangeThumbnail,
    categories,
    modalsType,
  };
}

const validationSchema = Yup.object().shape({
  name: Yup.string().required("Item name is required"),
  description: Yup.string().required("Item description is required"),
  price: Yup.string()
    .required("Item price is required")
    .test("digits", "Item price should be a number", (value) => !Number.isNaN(Number(value))),
  discount_price: Yup.number()
    .optional()
    .test(
      "islesser",
      "Discount price should be lesser than item price",
      (value, ctx) => value === undefined || Number(ctx.parent.price) > Number(value)
    )
    .min(1, "Price must be greater than 0")
    .integer("Price must be a number"),
});

export default useEditProductForm;
