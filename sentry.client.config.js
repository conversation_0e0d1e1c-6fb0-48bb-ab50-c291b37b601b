// This file configures the initialization of Sentry on the browser.
// The config you add here will be used whenever a page is visited.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

const SENTRY_DSN =
  process.env.SENTRY_DSN ||
  process.env.NEXT_PUBLIC_SENTRY_DSN ||
  "https://<EMAIL>/6231721";
const SENTRY_ENABLED = (process.env.NEXT_PUBLIC_SENTRY_ENABLED || process.env.SENTRY_ENABLED) === "true";

Sentry.init({
  enabled: SENTRY_ENABLED,
  dsn: SENTRY_ENABLED ? SENTRY_DSN : undefined,
  // Disable performance tracing when not needed
  tracesSampleRate: 1.0,
});
